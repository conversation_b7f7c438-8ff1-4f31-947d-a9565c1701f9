{"name": "@cap-js/cds-types", "version": "0.2.0", "description": "Type definitions for main packages of CAP, like `@sap/cds`", "repository": "github:cap-js/cds-types", "homepage": "https://cap.cloud.sap/", "keywords": ["CAP", "CDS", "Node.js"], "author": "SAP SE (https://www.sap.com)", "license": "SEE LICENSE IN LICENSE", "typings": "apis/cds.d.ts", "files": ["apis/", "LICENSE", "README.md"], "scripts": {"test": "jest --silent", "api-extractor": "api-extractor run --local --verbose", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "setup": "npm i && npm i file:. --no-save --force"}, "peerDependencies": {"@sap/cds": ">=7"}, "devDependencies": {"@sap/cds": "^7.5.0", "@stylistic/eslint-plugin": "^1.5.3", "@types/jest": "^29.5.11", "@types/node": "^20", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.18.1", "axios": "^1.6.2", "chai": "^4.3.10", "eslint": "^8.56.0", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.3.2", "winston": "^3.11.0"}, "jest": {"transform": {"^.+\\.(ts|tsx)$": "ts-jest"}}}