#Text Types
#
#XACT: Text with explicit importance for accessibility.
#XBUT: Button
#XCKL: Checkbox
#XFLD: Field label
#XLNK: Hyperlink
#XMIT: Menu item (Menu item, either top-level like "File" or lower-level like "Save as...")
#XMSG: Message
#XRBL: Radio button
#XSEL: Selection (Values in a drop-down list, or a status. For example: "In Process", "Shipped" or "Open".)
#XTIT: Title (or heading) of a non-actionable user interface element like a column, wizard, or screen area.
#XTOL: Explanatory text for an UI element, such as a tooltip, input help.
#YINS: Instruction for a user, for example, a permanent text on a screen that introduces a group of fields.
#----------------------------------------------------------------------------------------------------------------------
#For text elements that are not supposed to be translated, use the text type NOTR
#----------------------------------------------------------------------------------------------------------------------
#Recommended pattern
#
#<TextType>:<AdditionalContextInformation>
#If there is a maximum length restriction, please indicate as shown below.
#<TextType>,<MaximumLength>:<AdditionalContextInformation>
#----------------------------------------------------------------------------------------------------------------------
# This is the resource bundle for foundation
# __ldi.translation.uuid=dd6c5800-b108-11e8-be90-bd1cf6ac87fb
#----------------------------------------------------------------------------------------------------------------------

#XTIT: Created By (Answer to: "Which user has created a certain entity?")
CreatedBy=\u062A\u0645 \u0627\u0644\u0625\u0646\u0634\u0627\u0621 \u0628\u0648\u0627\u0633\u0637\u0629

#XTIT: Created On (Answer to: "When has a certain entity been created?")
CreatedAt=\u062A\u0627\u0631\u064A\u062E \u0627\u0644\u0625\u0646\u0634\u0627\u0621

#XTIT: Changed By (Answer to: "Which user has changed a certain entity?")
ChangedBy=\u062A\u0645 \u0627\u0644\u062A\u063A\u064A\u064A\u0631 \u0628\u0648\u0627\u0633\u0637\u0629

#XTIT: Changed On (Answer to: "When has a certain entity been changed?")
ChangedAt=\u062A\u0627\u0631\u064A\u062E \u0627\u0644\u062A\u063A\u064A\u064A\u0631

#XTIT: Currency
Currency=\u0627\u0644\u0639\u0645\u0644\u0629

#XTIT: Currency Code
CurrencyCode=\u0631\u0645\u0632 \u0627\u0644\u0639\u0645\u0644\u0629

#XTIT: Currency Code Description
CurrencyCode.Description=\u0631\u0645\u0632 \u0627\u0644\u0639\u0645\u0644\u0629 \u0627\u0644\u0645\u062D\u062F\u062F \u062D\u0633\u0628 ISO 4217

#XTIT: Currency Symbol
CurrencySymbol=\u0631\u0645\u0632 \u0627\u0644\u0639\u0645\u0644\u0629

#XTIT: Currency Minor Unit Fractions (Answer to: "How many fractions has a currency's minor unit?", e.g. "0" or "2")
CurrencyMinorUnit=\u0643\u0633\u0648\u0631 \u0648\u062D\u062F\u0627\u062A \u062B\u0627\u0646\u0648\u064A\u0629 \u0644\u0644\u0639\u0645\u0644\u0629

#XTIT: Country/Region
Country=\u0627\u0644\u062F\u0648\u0644\u0629/\u0627\u0644\u0645\u0646\u0637\u0642\u0629

#XTIT: Country/Region Code
CountryCode=\u0631\u0645\u0632 \u0627\u0644\u062F\u0648\u0644\u0629/\u0627\u0644\u0645\u0646\u0637\u0642\u0629

#XTIT: Country/Region Code Description
CountryCode.Description=\u0631\u0645\u0632 \u0627\u0644\u062F\u0648\u0644\u0629/\u0627\u0644\u0645\u0646\u0637\u0642\u0629 \u0627\u0644\u0645\u062D\u062F\u062F \u062D\u0633\u0628 ISO 4217

#XTIT: Language
Language=\u0627\u0644\u0644\u063A\u0629

#XTIT: Language Code
LanguageCode=\u0631\u0645\u0632 \u0627\u0644\u0644\u063A\u0629

#XTIT: Language Code Description
LanguageCode.Description=\u0631\u0645\u0632 \u0627\u0644\u062F\u0648\u0644\u0629 \u0627\u0644\u0645\u062D\u062F\u062F \u062D\u0633\u0628 ISO 639-1

#XTIT Time zone code
TimeZoneCode=\u0631\u0645\u0632 \u0627\u0644\u0645\u0646\u0637\u0642\u0629 \u0627\u0644\u0632\u0645\u0646\u064A\u0629

#XTIT: User Identifier
UserID=\u0645\u0639\u0631\u0641 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645

#XTIT: Any kind of name
Name=\u0627\u0644\u0627\u0633\u0645

#XTIT: Any kind of description
Description=\u0627\u0644\u0648\u0635\u0641

#XTOL: A user's unique Indentifier
UserID.Description=\u0645\u0639\u0631\u0641 \u0627\u0644\u0645\u0633\u062A\u062E\u062F\u0645 \u0627\u0644\u0641\u0631\u064A\u062F

#XTIT: Admin data for a draft document
Draft_DraftAdministrativeData=\u0627\u0644\u0628\u064A\u0627\u0646\u0627\u062A \u0627\u0644\u0625\u062F\u0627\u0631\u064A\u0629 \u0644\u0644\u0645\u0633\u0648\u062F\u0629

#XTIT: Technical ID of a draft document
Draft_DraftUUID=\u0645\u0633\u0648\u062F\u0629 (\u0627\u0644\u0645\u0639\u0631\u0641 \u0627\u0644\u062A\u0642\u0646\u064A)

#XTIT: Creation time of a draft
Draft_CreationDateTime=\u062A\u0627\u0631\u064A\u062E \u0625\u0646\u0634\u0627\u0621 \u0627\u0644\u0645\u0633\u0648\u062F\u0629

#XTIT: User created the draft
Draft_CreatedByUser=\u0645\u0646\u0634\u0626 \u0627\u0644\u0645\u0633\u0648\u062F\u0629

#XTIT: The current user (me) created the draft
Draft_DraftIsCreatedByMe=\u062A\u0645 \u0625\u0646\u0634\u0627\u0621 \u0627\u0644\u0645\u0633\u0648\u062F\u0629 \u0628\u0648\u0627\u0633\u0637\u062A\u064A

#XTIT: Time a draft was last changed on
Draft_LastChangeDateTime=\u062A\u0627\u0631\u064A\u062E \u0622\u062E\u0631 \u062A\u063A\u064A\u064A\u0631 \u0644\u0644\u0645\u0633\u0648\u062F\u0629

#XTIT: User that changed the draft last
Draft_LastChangedByUser=\u0622\u062E\u0631 \u062A\u063A\u064A\u064A\u0631 \u0644\u0644\u0645\u0633\u0648\u062F\u0629 \u0628\u0648\u0627\u0633\u0637\u0629

#XTIT: User that is working on the draft
Draft_InProcessByUser=\u0627\u0644\u0645\u0633\u0648\u062F\u0629 \u0642\u064A\u062F \u0627\u0644\u0645\u0639\u0627\u0644\u062C\u0629 \u0628\u0648\u0627\u0633\u0637\u0629

#XTIT: The current user (me) is working on the draft
Draft_DraftIsProcessedByMe=\u0627\u0644\u0645\u0633\u0648\u062F\u0629 \u0642\u064A\u062F \u0627\u0644\u0645\u0639\u0627\u0644\u062C\u0629 \u0628\u0648\u0627\u0633\u0637\u062A\u064A
