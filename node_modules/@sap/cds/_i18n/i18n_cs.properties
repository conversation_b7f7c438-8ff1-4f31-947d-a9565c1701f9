#Text Types
#
#XACT: Text with explicit importance for accessibility.
#XBUT: Button
#XCKL: Checkbox
#XFLD: Field label
#XLNK: Hyperlink
#XMIT: Menu item (Menu item, either top-level like "File" or lower-level like "Save as...")
#XMSG: Message
#XRBL: Radio button
#XSEL: Selection (Values in a drop-down list, or a status. For example: "In Process", "Shipped" or "Open".)
#XTIT: Title (or heading) of a non-actionable user interface element like a column, wizard, or screen area.
#XTOL: Explanatory text for an UI element, such as a tooltip, input help.
#YINS: Instruction for a user, for example, a permanent text on a screen that introduces a group of fields.
#----------------------------------------------------------------------------------------------------------------------
#For text elements that are not supposed to be translated, use the text type NOTR
#----------------------------------------------------------------------------------------------------------------------
#Recommended pattern
#
#<TextType>:<AdditionalContextInformation>
#If there is a maximum length restriction, please indicate as shown below.
#<TextType>,<MaximumLength>:<AdditionalContextInformation>
#----------------------------------------------------------------------------------------------------------------------
# This is the resource bundle for foundation
# __ldi.translation.uuid=dd6c5800-b108-11e8-be90-bd1cf6ac87fb
#----------------------------------------------------------------------------------------------------------------------

#XTIT: Created By (Answer to: "Which user has created a certain entity?")
CreatedBy=Vytvo\u0159il(a)

#XTIT: Created On (Answer to: "When has a certain entity been created?")
CreatedAt=Vytvo\u0159eno dne

#XTIT: Changed By (Answer to: "Which user has changed a certain entity?")
ChangedBy=Autor zm\u011Bny

#XTIT: Changed On (Answer to: "When has a certain entity been changed?")
ChangedAt=Zm\u011Bn\u011Bno dne

#XTIT: Currency
Currency=M\u011Bna

#XTIT: Currency Code
CurrencyCode=K\u00F3d m\u011Bny

#XTIT: Currency Code Description
CurrencyCode.Description=K\u00F3d m\u011Bny zadan\u00FD dle ISO 4217

#XTIT: Currency Symbol
CurrencySymbol=Symbol m\u011Bny

#XTIT: Currency Minor Unit Fractions (Answer to: "How many fractions has a currency's minor unit?", e.g. "0" or "2")
CurrencyMinorUnit=Zlomky pomocn\u00E9 jednotky m\u011Bny

#XTIT: Country/Region
Country=St\u00E1t/region

#XTIT: Country/Region Code
CountryCode=K\u00F3d st\u00E1tu/regionu

#XTIT: Country/Region Code Description
CountryCode.Description=K\u00F3d st\u00E1tu/regionu zadan\u00FD dle ISO 3166-1

#XTIT: Language
Language=Jazyk

#XTIT: Language Code
LanguageCode=K\u00F3d jazyka

#XTIT: Language Code Description
LanguageCode.Description=K\u00F3d jazyka zadan\u00FD dle ISO 639-1

#XTIT Time zone code
TimeZoneCode=K\u00F3d \u010Dasov\u00E9ho p\u00E1sma

#XTIT: User Identifier
UserID=ID u\u017Eivatele

#XTIT: Any kind of name
Name=Jm\u00E9no

#XTIT: Any kind of description
Description=Popis

#XTOL: A user's unique Indentifier
UserID.Description=Jedine\u010Dn\u00E9 ID u\u017Eivatele

#XTIT: Admin data for a draft document
Draft_DraftAdministrativeData=Spr\u00E1vn\u00ED data n\u00E1vrhu

#XTIT: Technical ID of a draft document
Draft_DraftUUID=N\u00E1vrh (technick\u00E9 ID)

#XTIT: Creation time of a draft
Draft_CreationDateTime=N\u00E1vrh vytvo\u0159en dne

#XTIT: User created the draft
Draft_CreatedByUser=N\u00E1vrh vytvo\u0159il

#XTIT: The current user (me) created the draft
Draft_DraftIsCreatedByMe=N\u00E1vrh vytvo\u0159en mnou

#XTIT: Time a draft was last changed on
Draft_LastChangeDateTime=N\u00E1vrh naposledy zm\u011Bn\u011Bn dne

#XTIT: User that changed the draft last
Draft_LastChangedByUser=N\u00E1vrh naposledy zm\u011Bnil

#XTIT: User that is working on the draft
Draft_InProcessByUser=N\u00E1vrh zpracov\u00E1v\u00E1

#XTIT: The current user (me) is working on the draft
Draft_DraftIsProcessedByMe=N\u00E1vrh zpracov\u00E1van\u00FD mnou
