#Text Types
#
#XACT: Text with explicit importance for accessibility.
#XBUT: Button
#XCKL: Checkbox
#XFLD: Field label
#XLNK: Hyperlink
#XMIT: Menu item (Menu item, either top-level like "File" or lower-level like "Save as...")
#XMSG: Message
#XRBL: Radio button
#XSEL: Selection (Values in a drop-down list, or a status. For example: "In Process", "Shipped" or "Open".)
#XTIT: Title (or heading) of a non-actionable user interface element like a column, wizard, or screen area.
#XTOL: Explanatory text for an UI element, such as a tooltip, input help.
#YINS: Instruction for a user, for example, a permanent text on a screen that introduces a group of fields.
#----------------------------------------------------------------------------------------------------------------------
#For text elements that are not supposed to be translated, use the text type NOTR
#----------------------------------------------------------------------------------------------------------------------
#Recommended pattern
#
#<TextType>:<AdditionalContextInformation>
#If there is a maximum length restriction, please indicate as shown below.
#<TextType>,<MaximumLength>:<AdditionalContextInformation>
#----------------------------------------------------------------------------------------------------------------------
# This is the resource bundle for foundation
# __ldi.translation.uuid=dd6c5800-b108-11e8-be90-bd1cf6ac87fb
#----------------------------------------------------------------------------------------------------------------------

#XTIT: Created By (Answer to: "Which user has created a certain entity?")
CreatedBy=Angelegt von

#XTIT: Created On (Answer to: "When has a certain entity been created?")
CreatedAt=Angelegt am

#XTIT: Changed By (Answer to: "Which user has changed a certain entity?")
ChangedBy=Ge\u00E4ndert von

#XTIT: Changed On (Answer to: "When has a certain entity been changed?")
ChangedAt=Ge\u00E4ndert am

#XTIT: Currency
Currency=W\u00E4hrung

#XTIT: Currency Code
CurrencyCode=W\u00E4hrungscode

#XTIT: Currency Code Description
CurrencyCode.Description=W\u00E4hrungscode gem\u00E4\u00DF ISO 4217

#XTIT: Currency Symbol
CurrencySymbol=W\u00E4hrungssymbol

#XTIT: Currency Minor Unit Fractions (Answer to: "How many fractions has a currency's minor unit?", e.g. "0" or "2")
CurrencyMinorUnit=Bruchteile W\u00E4hrungsuntereinheit

#XTIT: Country/Region
Country=Land/Region

#XTIT: Country/Region Code
CountryCode=L\u00E4nder-/Regionscode

#XTIT: Country/Region Code Description
CountryCode.Description=L\u00E4nder-/Regionscode gem\u00E4\u00DF ISO 3166-1

#XTIT: Language
Language=Sprache

#XTIT: Language Code
LanguageCode=Sprachencode

#XTIT: Language Code Description
LanguageCode.Description=Sprachencode gem\u00E4\u00DF ISO 639-1

#XTIT: User Identifier
UserID=Benutzer-ID

#XTIT: Any kind of name
Name=Name

#XTIT: Any kind of description
Description=Beschreibung

#XTOL: A user's unique Indentifier
UserID.Description=Die eindeutige ID eines Benutzers

#XTIT: Admin data for a draft document
Draft_DraftAdministrativeData=Verwaltungsdaten des Entwurfs

#XTIT: Technical ID of a draft document
Draft_DraftUUID=Entwurf (technische ID)

#XTIT: Creation time of a draft
Draft_CreationDateTime=Entwurf angelegt am

#XTIT: User created the draft
Draft_CreatedByUser=Entwurf angelegt von

#XTIT: The current user (me) created the draft
Draft_DraftIsCreatedByMe=Entwurf angelegt von mir

#XTIT: Time a draft was last changed on
Draft_LastChangeDateTime=Entwurf zuletzt ge\u00E4ndert am

#XTIT: User that changed the draft last
Draft_LastChangedByUser=Entwurf zuletzt ge\u00E4ndert von

#XTIT: User that is working on the draft
Draft_InProcessByUser=Entwurf in Bearbeitung durch

#XTIT: The current user (me) is working on the draft
Draft_DraftIsProcessedByMe=Entwurf in Bearbeitung durch mich
