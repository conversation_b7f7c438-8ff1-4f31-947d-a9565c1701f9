#Text Types
#
#XACT: Text with explicit importance for accessibility.
#XBUT: Button
#XCKL: Checkbox
#XFLD: Field label
#XLNK: Hyperlink
#XMIT: Menu item (Menu item, either top-level like "File" or lower-level like "Save as...")
#XMSG: Message
#XRBL: Radio button
#XSEL: Selection (Values in a drop-down list, or a status. For example: "In Process", "Shipped" or "Open".)
#XTIT: Title (or heading) of a non-actionable user interface element like a column, wizard, or screen area.
#XTOL: Explanatory text for an UI element, such as a tooltip, input help.
#YINS: Instruction for a user, for example, a permanent text on a screen that introduces a group of fields.
#----------------------------------------------------------------------------------------------------------------------
#For text elements that are not supposed to be translated, use the text type NOTR
#----------------------------------------------------------------------------------------------------------------------
#Recommended pattern
#
#<TextType>:<AdditionalContextInformation>
#If there is a maximum length restriction, please indicate as shown below.
#<TextType>,<MaximumLength>:<AdditionalContextInformation>
#----------------------------------------------------------------------------------------------------------------------
# This is the resource bundle for foundation
# __ldi.translation.uuid=dd6c5800-b108-11e8-be90-bd1cf6ac87fb
#----------------------------------------------------------------------------------------------------------------------

#XTIT: Created By (Answer to: "Which user has created a certain entity?")
CreatedBy=Created By

#XTIT: Created On (Answer to: "When has a certain entity been created?")
CreatedAt=Created On

#XTIT: Changed By (Answer to: "Which user has changed a certain entity?")
ChangedBy=Changed By

#XTIT: Changed On (Answer to: "When has a certain entity been changed?")
ChangedAt=Changed On

#XTIT: Currency
Currency=Currency

#XTIT: Currency Code
CurrencyCode=Currency Code

#XTIT: Currency Code Description
CurrencyCode.Description=Currency code as specified by ISO 4217

#XTIT: Currency Symbol
CurrencySymbol=Currency Symbol

#XTIT: Currency Minor Unit Fractions (Answer to: "How many fractions has a currency's minor unit?", e.g. "0" or "2")
CurrencyMinorUnit=Currency Minor Unit Fractions

#XTIT: Country/Region
Country=Country/Region

#XTIT: Country/Region Code
CountryCode=Country/Region Code

#XTIT: Country/Region Code Description
CountryCode.Description=Country/region code as specified by ISO 3166-1

#XTIT: Language
Language=Language

#XTIT: Language Code
LanguageCode=Language Code

#XTIT: Language Code Description
LanguageCode.Description=Language code as specified by ISO 639-1

#XTIT: User Identifier
UserID=User ID

#XTIT: Any kind of name
Name=Name

#XTIT: Any kind of description
Description=Description

#XTOL: A user's unique Indentifier
UserID.Description=User's unique ID

#XTIT: Admin data for a draft document
Draft_DraftAdministrativeData=Draft Administrative Data

#XTIT: Technical ID of a draft document
Draft_DraftUUID=Draft (Technical ID)

#XTIT: Creation time of a draft
Draft_CreationDateTime=Draft Created On

#XTIT: User created the draft
Draft_CreatedByUser=Draft Created By

#XTIT: The current user (me) created the draft
Draft_DraftIsCreatedByMe=Draft Created By Me

#XTIT: Time a draft was last changed on
Draft_LastChangeDateTime=Draft Last Changed On

#XTIT: User that changed the draft last
Draft_LastChangedByUser=Draft Last Changed By

#XTIT: User that is working on the draft
Draft_InProcessByUser=Draft In Process By

#XTIT: The current user (me) is working on the draft
Draft_DraftIsProcessedByMe=Draft In Process By Me
