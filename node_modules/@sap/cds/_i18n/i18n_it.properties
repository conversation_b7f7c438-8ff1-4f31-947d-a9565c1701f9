#Text Types
#
#XACT: Text with explicit importance for accessibility.
#XBUT: Button
#XCKL: Checkbox
#XFLD: Field label
#XLNK: Hyperlink
#XMIT: Menu item (Menu item, either top-level like "File" or lower-level like "Save as...")
#XMSG: Message
#XRBL: Radio button
#XSEL: Selection (Values in a drop-down list, or a status. For example: "In Process", "Shipped" or "Open".)
#XTIT: Title (or heading) of a non-actionable user interface element like a column, wizard, or screen area.
#XTOL: Explanatory text for an UI element, such as a tooltip, input help.
#YINS: Instruction for a user, for example, a permanent text on a screen that introduces a group of fields.
#----------------------------------------------------------------------------------------------------------------------
#For text elements that are not supposed to be translated, use the text type NOTR
#----------------------------------------------------------------------------------------------------------------------
#Recommended pattern
#
#<TextType>:<AdditionalContextInformation>
#If there is a maximum length restriction, please indicate as shown below.
#<TextType>,<MaximumLength>:<AdditionalContextInformation>
#----------------------------------------------------------------------------------------------------------------------
# This is the resource bundle for foundation
# __ldi.translation.uuid=dd6c5800-b108-11e8-be90-bd1cf6ac87fb
#----------------------------------------------------------------------------------------------------------------------

#XTIT: Created By (Answer to: "Which user has created a certain entity?")
CreatedBy=Autore creazione

#XTIT: Created On (Answer to: "When has a certain entity been created?")
CreatedAt=Data di creazione

#XTIT: Changed By (Answer to: "Which user has changed a certain entity?")
ChangedBy=Autore modifica

#XTIT: Changed On (Answer to: "When has a certain entity been changed?")
ChangedAt=Data di modifica

#XTIT: Currency
Currency=Divisa

#XTIT: Currency Code
CurrencyCode=Codice divisa

#XTIT: Currency Code Description
CurrencyCode.Description=Codice divisa come indicato da ISO 4217

#XTIT: Currency Symbol
CurrencySymbol=Simbolo divisa

#XTIT: Currency Minor Unit Fractions (Answer to: "How many fractions has a currency's minor unit?", e.g. "0" or "2")
CurrencyMinorUnit=Frazioni suddivisione unit\u00E0 monetaria

#XTIT: Country/Region
Country=Paese/regione

#XTIT: Country/Region Code
CountryCode=Codice paese/regione

#XTIT: Country/Region Code Description
CountryCode.Description=Codice paese/regione come indicato nell'ISO 3166-1

#XTIT: Language
Language=Lingua

#XTIT: Language Code
LanguageCode=Codice lingua

#XTIT: Language Code Description
LanguageCode.Description=Codice lingua come indicato da ISO 639-1

#XTIT: User Identifier
UserID=ID utente

#XTIT: Any kind of name
Name=Nome

#XTIT: Any kind of description
Description=Descrizione

#XTOL: A user's unique Indentifier
UserID.Description=ID univoco utente

#XTIT: Admin data for a draft document
Draft_DraftAdministrativeData=Dati amministrativi bozza

#XTIT: Technical ID of a draft document
Draft_DraftUUID=Bozza (ID tecnico)

#XTIT: Creation time of a draft
Draft_CreationDateTime=Data di creazione bozza

#XTIT: User created the draft
Draft_CreatedByUser=Autore creazione bozza

#XTIT: The current user (me) created the draft
Draft_DraftIsCreatedByMe=Bozza creata da me

#XTIT: Time a draft was last changed on
Draft_LastChangeDateTime=Ultima modifica bozza

#XTIT: User that changed the draft last
Draft_LastChangedByUser=Autore dell'ultima modifica bozza

#XTIT: User that is working on the draft
Draft_InProcessByUser=Bozza attualmente elaborata da

#XTIT: The current user (me) is working on the draft
Draft_DraftIsProcessedByMe=Bozza attualmente elaborata da me
