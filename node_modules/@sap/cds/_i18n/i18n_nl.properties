#Text Types
#
#XACT: Text with explicit importance for accessibility.
#XBUT: Button
#XCKL: Checkbox
#XFLD: Field label
#XLNK: Hyperlink
#XMIT: Menu item (Menu item, either top-level like "File" or lower-level like "Save as...")
#XMSG: Message
#XRBL: Radio button
#XSEL: Selection (Values in a drop-down list, or a status. For example: "In Process", "Shipped" or "Open".)
#XTIT: Title (or heading) of a non-actionable user interface element like a column, wizard, or screen area.
#XTOL: Explanatory text for an UI element, such as a tooltip, input help.
#YINS: Instruction for a user, for example, a permanent text on a screen that introduces a group of fields.
#----------------------------------------------------------------------------------------------------------------------
#For text elements that are not supposed to be translated, use the text type NOTR
#----------------------------------------------------------------------------------------------------------------------
#Recommended pattern
#
#<TextType>:<AdditionalContextInformation>
#If there is a maximum length restriction, please indicate as shown below.
#<TextType>,<MaximumLength>:<AdditionalContextInformation>
#----------------------------------------------------------------------------------------------------------------------
# This is the resource bundle for foundation
# __ldi.translation.uuid=dd6c5800-b108-11e8-be90-bd1cf6ac87fb
#----------------------------------------------------------------------------------------------------------------------

#XTIT: Created By (Answer to: "Which user has created a certain entity?")
CreatedBy=Gecre\u00EBerd door

#XTIT: Created On (Answer to: "When has a certain entity been created?")
CreatedAt=Gecre\u00EBerd op

#XTIT: Changed By (Answer to: "Which user has changed a certain entity?")
ChangedBy=Gewijzigd door

#XTIT: Changed On (Answer to: "When has a certain entity been changed?")
ChangedAt=Gewijzigd op

#XTIT: Currency
Currency=Valuta

#XTIT: Currency Code
CurrencyCode=Valutacode

#XTIT: Currency Code Description
CurrencyCode.Description=Valutacode zoals gespecificeerd door ISO 4217

#XTIT: Currency Symbol
CurrencySymbol=Valutasymbool

#XTIT: Currency Minor Unit Fractions (Answer to: "How many fractions has a currency's minor unit?", e.g. "0" or "2")
CurrencyMinorUnit=Breuken kleinste eenheid valuta

#XTIT: Country/Region
Country=Land/regio

#XTIT: Country/Region Code
CountryCode=Land-/regiocode

#XTIT: Country/Region Code Description
CountryCode.Description=Land-/regiocode zoals gespecificeerd door ISO 3166-1

#XTIT: Language
Language=Taal

#XTIT: Language Code
LanguageCode=Taalcode

#XTIT: Language Code Description
LanguageCode.Description=Taalcode zoals gespecificeerd door ISO 639-1

#XTIT Time zone code
TimeZoneCode=Code tijdzone

#XTIT: User Identifier
UserID=Gebruikers-ID

#XTIT: Any kind of name
Name=Naam

#XTIT: Any kind of description
Description=Omschrijving

#XTOL: A user's unique Indentifier
UserID.Description=Unieke ID van gebruiker

#XTIT: Admin data for a draft document
Draft_DraftAdministrativeData=Conceptbeheergegevens

#XTIT: Technical ID of a draft document
Draft_DraftUUID=Concept (technische ID)

#XTIT: Creation time of a draft
Draft_CreationDateTime=Concept gecre\u00EBerd op

#XTIT: User created the draft
Draft_CreatedByUser=Concept gecre\u00EBerd door

#XTIT: The current user (me) created the draft
Draft_DraftIsCreatedByMe=Concept gecre\u00EBerd door mij

#XTIT: Time a draft was last changed on
Draft_LastChangeDateTime=Concept laatst gewijzigd op

#XTIT: User that changed the draft last
Draft_LastChangedByUser=Concept laatst gewijzigd door

#XTIT: User that is working on the draft
Draft_InProcessByUser=Concept wordt bewerkt door

#XTIT: The current user (me) is working on the draft
Draft_DraftIsProcessedByMe=Concept wordt bewerkt door mij
