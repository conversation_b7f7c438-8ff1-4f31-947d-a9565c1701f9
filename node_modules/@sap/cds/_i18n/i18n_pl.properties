#Text Types
#
#XACT: Text with explicit importance for accessibility.
#XBUT: Button
#XCKL: Checkbox
#XFLD: Field label
#XLNK: Hyperlink
#XMIT: Menu item (Menu item, either top-level like "File" or lower-level like "Save as...")
#XMSG: Message
#XRBL: Radio button
#XSEL: Selection (Values in a drop-down list, or a status. For example: "In Process", "Shipped" or "Open".)
#XTIT: Title (or heading) of a non-actionable user interface element like a column, wizard, or screen area.
#XTOL: Explanatory text for an UI element, such as a tooltip, input help.
#YINS: Instruction for a user, for example, a permanent text on a screen that introduces a group of fields.
#----------------------------------------------------------------------------------------------------------------------
#For text elements that are not supposed to be translated, use the text type NOTR
#----------------------------------------------------------------------------------------------------------------------
#Recommended pattern
#
#<TextType>:<AdditionalContextInformation>
#If there is a maximum length restriction, please indicate as shown below.
#<TextType>,<MaximumLength>:<AdditionalContextInformation>
#----------------------------------------------------------------------------------------------------------------------
# This is the resource bundle for foundation
# __ldi.translation.uuid=dd6c5800-b108-11e8-be90-bd1cf6ac87fb
#----------------------------------------------------------------------------------------------------------------------

#XTIT: Created By (Answer to: "Which user has created a certain entity?")
CreatedBy=Utworzone przez

#XTIT: Created On (Answer to: "When has a certain entity been created?")
CreatedAt=Utworzono dnia

#XTIT: Changed By (Answer to: "Which user has changed a certain entity?")
ChangedBy=Zmienione przez

#XTIT: Changed On (Answer to: "When has a certain entity been changed?")
ChangedAt=Zmieniono dnia

#XTIT: Currency
Currency=Waluta

#XTIT: Currency Code
CurrencyCode=Kod waluty

#XTIT: Currency Code Description
CurrencyCode.Description=Kod waluty okre\u015Blony zgodnie z norm\u0105 ISO 4217

#XTIT: Currency Symbol
CurrencySymbol=Symbol waluty

#XTIT: Currency Minor Unit Fractions (Answer to: "How many fractions has a currency's minor unit?", e.g. "0" or "2")
CurrencyMinorUnit=Warto\u015Bci u\u0142amkowe jednostki pomocniczej waluty

#XTIT: Country/Region
Country=Kraj/region

#XTIT: Country/Region Code
CountryCode=Kod kraju/regionu

#XTIT: Country/Region Code Description
CountryCode.Description=Kod kraju/regionu zgodny z norm\u0105 ISO 3166-1

#XTIT: Language
Language=J\u0119zyk

#XTIT: Language Code
LanguageCode=Kod j\u0119zyka

#XTIT: Language Code Description
LanguageCode.Description=Kod j\u0119zyka okre\u015Blony zgodnie z norm\u0105 ISO 639-1

#XTIT: User Identifier
UserID=Identyfikator u\u017Cytkownika

#XTIT: Any kind of name
Name=Nazwa

#XTIT: Any kind of description
Description=Opis

#XTOL: A user's unique Indentifier
UserID.Description=Unikalny identyfikator u\u017Cytkownika

#XTIT: Admin data for a draft document
Draft_DraftAdministrativeData=Dane administracyjne wersji roboczej

#XTIT: Technical ID of a draft document
Draft_DraftUUID=Wersja robocza (identyfikator techniczny)

#XTIT: Creation time of a draft
Draft_CreationDateTime=Wersja robocza utworzona dnia

#XTIT: User created the draft
Draft_CreatedByUser=Wersja robocza utworzona przez

#XTIT: The current user (me) created the draft
Draft_DraftIsCreatedByMe=Wersja robocza utworzona przeze mnie

#XTIT: Time a draft was last changed on
Draft_LastChangeDateTime=Wersja robocza ostatnio zmieniona dnia

#XTIT: User that changed the draft last
Draft_LastChangedByUser=Wersja robocza ostatnio zmieniona przez

#XTIT: User that is working on the draft
Draft_InProcessByUser=Wersja robocza w przetwarzaniu przez

#XTIT: The current user (me) is working on the draft
Draft_DraftIsProcessedByMe=Wersja robocza w przetwarzaniu przeze mnie
