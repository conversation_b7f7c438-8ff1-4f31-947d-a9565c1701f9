#Text Types
#
#XACT: Text with explicit importance for accessibility.
#XBUT: Button
#XCKL: Checkbox
#XFLD: Field label
#XLNK: Hyperlink
#XMIT: Menu item (Menu item, either top-level like "File" or lower-level like "Save as...")
#XMSG: Message
#XRBL: Radio button
#XSEL: Selection (Values in a drop-down list, or a status. For example: "In Process", "Shipped" or "Open".)
#XTIT: Title (or heading) of a non-actionable user interface element like a column, wizard, or screen area.
#XTOL: Explanatory text for an UI element, such as a tooltip, input help.
#YINS: Instruction for a user, for example, a permanent text on a screen that introduces a group of fields.
#----------------------------------------------------------------------------------------------------------------------
#For text elements that are not supposed to be translated, use the text type NOTR
#----------------------------------------------------------------------------------------------------------------------
#Recommended pattern
#
#<TextType>:<AdditionalContextInformation>
#If there is a maximum length restriction, please indicate as shown below.
#<TextType>,<MaximumLength>:<AdditionalContextInformation>
#----------------------------------------------------------------------------------------------------------------------
# This is the resource bundle for foundation
# __ldi.translation.uuid=dd6c5800-b108-11e8-be90-bd1cf6ac87fb
#----------------------------------------------------------------------------------------------------------------------

#XTIT: Created By (Answer to: "Which user has created a certain entity?")
CreatedBy=Criado por

#XTIT: Created On (Answer to: "When has a certain entity been created?")
CreatedAt=Criado em

#XTIT: Changed By (Answer to: "Which user has changed a certain entity?")
ChangedBy=Modificado por

#XTIT: Changed On (Answer to: "When has a certain entity been changed?")
ChangedAt=Modificado em

#XTIT: Currency
Currency=Moeda

#XTIT: Currency Code
CurrencyCode=C\u00F3digo de moeda

#XTIT: Currency Code Description
CurrencyCode.Description=C\u00F3digo de moeda como especificado pelo ISO 4217

#XTIT: Currency Symbol
CurrencySymbol=S\u00EDmbolo da moeda

#XTIT: Currency Minor Unit Fractions (Answer to: "How many fractions has a currency's minor unit?", e.g. "0" or "2")
CurrencyMinorUnit=Fra\u00E7\u00F5es de unidade auxiliar de moeda

#XTIT: Country/Region
Country=Pa\u00EDs/regi\u00E3o

#XTIT: Country/Region Code
CountryCode=C\u00F3digo do pa\u00EDs/regi\u00E3o

#XTIT: Country/Region Code Description
CountryCode.Description=C\u00F3digo do pa\u00EDs/regi\u00E3o como especificado pelo ISO 3166-1

#XTIT: Language
Language=Idioma

#XTIT: Language Code
LanguageCode=C\u00F3digo de idioma

#XTIT: Language Code Description
LanguageCode.Description=C\u00F3digo de idioma como especificado pelo ISO 639-1

#XTIT: User Identifier
UserID=ID do usu\u00E1rio

#XTIT: Any kind of name
Name=Nome

#XTIT: Any kind of description
Description=Descri\u00E7\u00E3o

#XTOL: A user's unique Indentifier
UserID.Description=ID exclusivo do usu\u00E1rio

#XTIT: Admin data for a draft document
Draft_DraftAdministrativeData=Dados administrativos de rascunho

#XTIT: Technical ID of a draft document
Draft_DraftUUID=Rascunho (ID t\u00E9cnico)

#XTIT: Creation time of a draft
Draft_CreationDateTime=Rascunho criado em

#XTIT: User created the draft
Draft_CreatedByUser=Rascunho criado por

#XTIT: The current user (me) created the draft
Draft_DraftIsCreatedByMe=Rascunho criado por mim

#XTIT: Time a draft was last changed on
Draft_LastChangeDateTime=\u00DAltima modifica\u00E7\u00E3o do rascunho em

#XTIT: User that changed the draft last
Draft_LastChangedByUser=\u00DAltima modifica\u00E7\u00E3o do rascunho por

#XTIT: User that is working on the draft
Draft_InProcessByUser=Rascunho sendo processado por

#XTIT: The current user (me) is working on the draft
Draft_DraftIsProcessedByMe=Rascunho sendo processado por mim
