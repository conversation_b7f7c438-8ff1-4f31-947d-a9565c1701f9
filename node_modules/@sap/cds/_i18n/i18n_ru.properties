#Text Types
#
#XACT: Text with explicit importance for accessibility.
#XBUT: Button
#XCKL: Checkbox
#XFLD: Field label
#XLNK: Hyperlink
#XMIT: Menu item (Menu item, either top-level like "File" or lower-level like "Save as...")
#XMSG: Message
#XRBL: Radio button
#XSEL: Selection (Values in a drop-down list, or a status. For example: "In Process", "Shipped" or "Open".)
#XTIT: Title (or heading) of a non-actionable user interface element like a column, wizard, or screen area.
#XTOL: Explanatory text for an UI element, such as a tooltip, input help.
#YINS: Instruction for a user, for example, a permanent text on a screen that introduces a group of fields.
#----------------------------------------------------------------------------------------------------------------------
#For text elements that are not supposed to be translated, use the text type NOTR
#----------------------------------------------------------------------------------------------------------------------
#Recommended pattern
#
#<TextType>:<AdditionalContextInformation>
#If there is a maximum length restriction, please indicate as shown below.
#<TextType>,<MaximumLength>:<AdditionalContextInformation>
#----------------------------------------------------------------------------------------------------------------------
# This is the resource bundle for foundation
# __ldi.translation.uuid=dd6c5800-b108-11e8-be90-bd1cf6ac87fb
#----------------------------------------------------------------------------------------------------------------------

#XTIT: Created By (Answer to: "Which user has created a certain entity?")
CreatedBy=\u0421\u043E\u0437\u0434\u0430\u043B

#XTIT: Created On (Answer to: "When has a certain entity been created?")
CreatedAt=\u0414\u0430\u0442\u0430 \u0441\u043E\u0437\u0434\u0430\u043D\u0438\u044F

#XTIT: Changed By (Answer to: "Which user has changed a certain entity?")
ChangedBy=\u0410\u0432\u0442\u043E\u0440 \u0438\u0437\u043C\u0435\u043D\u0435\u043D\u0438\u044F

#XTIT: Changed On (Answer to: "When has a certain entity been changed?")
ChangedAt=\u0414\u0430\u0442\u0430 \u0438\u0437\u043C\u0435\u043D\u0435\u043D\u0438\u044F

#XTIT: Currency
Currency=\u0412\u0430\u043B\u044E\u0442\u0430

#XTIT: Currency Code
CurrencyCode=\u041A\u043E\u0434 \u0432\u0430\u043B\u044E\u0442\u044B

#XTIT: Currency Code Description
CurrencyCode.Description=\u041A\u043E\u0434 \u0432\u0430\u043B\u044E\u0442\u044B \u0432 \u0441\u043E\u043E\u0442\u0432\u0435\u0442\u0441\u0442\u0432\u0438\u0438 \u0441 ISO 4217

#XTIT: Currency Symbol
CurrencySymbol=\u0421\u0438\u043C\u0432\u043E\u043B \u0432\u0430\u043B\u044E\u0442\u044B

#XTIT: Currency Minor Unit Fractions (Answer to: "How many fractions has a currency's minor unit?", e.g. "0" or "2")
CurrencyMinorUnit=\u0414\u043E\u043B\u0438 \u0432\u0441\u043F\u043E\u043C\u043E\u0433\u0430\u0442\u0435\u043B\u044C\u043D\u043E\u0439 \u0432\u0430\u043B\u044E\u0442\u043D\u043E\u0439 \u0435\u0434\u0438\u043D\u0438\u0446\u044B

#XTIT: Country/Region
Country=\u0421\u0442\u0440\u0430\u043D\u0430/\u0440\u0435\u0433\u0438\u043E\u043D

#XTIT: Country/Region Code
CountryCode=\u041A\u043E\u0434 \u0441\u0442\u0440\u0430\u043D\u044B/\u0440\u0435\u0433\u0438\u043E\u043D\u0430

#XTIT: Country/Region Code Description
CountryCode.Description=\u041A\u043E\u0434 \u0441\u0442\u0440\u0430\u043D\u044B/\u0440\u0435\u0433\u0438\u043E\u043D\u0430 \u0432 \u0441\u043E\u043E\u0442\u0432\u0435\u0442\u0441\u0442\u0432\u0438\u0438 \u0441 ISO 3166-1

#XTIT: Language
Language=\u042F\u0437\u044B\u043A

#XTIT: Language Code
LanguageCode=\u041A\u043E\u0434 \u044F\u0437\u044B\u043A\u0430

#XTIT: Language Code Description
LanguageCode.Description=\u041A\u043E\u0434 \u044F\u0437\u044B\u043A\u0430 \u0432 \u0441\u043E\u043E\u0442\u0432\u0435\u0442\u0441\u0442\u0432\u0438\u0438 \u0441 ISO 639-1

#XTIT: User Identifier
UserID=\u0418\u0434. \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044F

#XTIT: Any kind of name
Name=\u0418\u043C\u044F

#XTIT: Any kind of description
Description=\u041E\u043F\u0438\u0441\u0430\u043D\u0438\u0435

#XTOL: A user's unique Indentifier
UserID.Description=\u0423\u043D\u0438\u043A\u0430\u043B\u044C\u043D\u044B\u0439 \u0438\u0434. \u043F\u043E\u043B\u044C\u0437\u043E\u0432\u0430\u0442\u0435\u043B\u044F

#XTIT: Admin data for a draft document
Draft_DraftAdministrativeData=\u0427\u0435\u0440\u043D\u043E\u0432\u0438\u043A \u0430\u0434\u043C\u0438\u043D\u0438\u0441\u0442\u0440\u0430\u0442\u0438\u0432\u043D\u044B\u0445 \u0434\u0430\u043D\u043D\u044B\u0445

#XTIT: Technical ID of a draft document
Draft_DraftUUID=\u0427\u0435\u0440\u043D\u043E\u0432\u0438\u043A (\u0442\u0435\u0445\u043D\u0438\u0447\u0435\u0441\u043A\u0438\u0439 \u0438\u0434.)

#XTIT: Creation time of a draft
Draft_CreationDateTime=\u0414\u0430\u0442\u0430 \u0441\u043E\u0437\u0434\u0430\u043D\u0438\u044F \u0447\u0435\u0440\u043D\u043E\u0432\u0438\u043A\u0430

#XTIT: User created the draft
Draft_CreatedByUser=\u0421\u043E\u0437\u0434\u0430\u043B \u0447\u0435\u0440\u043D\u043E\u0432\u0438\u043A

#XTIT: The current user (me) created the draft
Draft_DraftIsCreatedByMe=\u0427\u0435\u0440\u043D\u043E\u0432\u0438\u043A \u0441\u043E\u0437\u0434\u0430\u043D \u043C\u043D\u043E\u0439

#XTIT: Time a draft was last changed on
Draft_LastChangeDateTime=\u0414\u0430\u0442\u0430 \u043F\u043E\u0441\u043B\u0435\u0434\u043D\u0435\u0433\u043E \u0438\u0437\u043C\u0435\u043D\u0435\u043D\u0438\u044F \u0447\u0435\u0440\u043D\u043E\u0432\u0438\u043A\u0430

#XTIT: User that changed the draft last
Draft_LastChangedByUser=\u0418\u0437\u043C\u0435\u043D\u0438\u043B \u0447\u0435\u0440\u043D\u043E\u0432\u0438\u043A

#XTIT: User that is working on the draft
Draft_InProcessByUser=\u041E\u0431\u0440\u0430\u0431\u0430\u0442\u044B\u0432\u0430\u0435\u0442 \u0447\u0435\u0440\u043D\u043E\u0432\u0438\u043A

#XTIT: The current user (me) is working on the draft
Draft_DraftIsProcessedByMe=\u0427\u0435\u0440\u043D\u043E\u0432\u0438\u043A \u043E\u0431\u0440\u0430\u0431\u0430\u0442\u044B\u0432\u0430\u0435\u0442\u0441\u044F \u043C\u043D\u043E\u0439
