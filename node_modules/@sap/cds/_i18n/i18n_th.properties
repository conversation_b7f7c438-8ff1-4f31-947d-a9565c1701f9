#Text Types
#
#XACT: Text with explicit importance for accessibility.
#XBUT: Button
#XCKL: Checkbox
#XFLD: Field label
#XLNK: Hyperlink
#XMIT: Menu item (Menu item, either top-level like "File" or lower-level like "Save as...")
#XMSG: Message
#XRBL: Radio button
#XSEL: Selection (Values in a drop-down list, or a status. For example: "In Process", "Shipped" or "Open".)
#XTIT: Title (or heading) of a non-actionable user interface element like a column, wizard, or screen area.
#XTOL: Explanatory text for an UI element, such as a tooltip, input help.
#YINS: Instruction for a user, for example, a permanent text on a screen that introduces a group of fields.
#----------------------------------------------------------------------------------------------------------------------
#For text elements that are not supposed to be translated, use the text type NOTR
#----------------------------------------------------------------------------------------------------------------------
#Recommended pattern
#
#<TextType>:<AdditionalContextInformation>
#If there is a maximum length restriction, please indicate as shown below.
#<TextType>,<MaximumLength>:<AdditionalContextInformation>
#----------------------------------------------------------------------------------------------------------------------
# This is the resource bundle for foundation
# __ldi.translation.uuid=dd6c5800-b108-11e8-be90-bd1cf6ac87fb
#----------------------------------------------------------------------------------------------------------------------

#XTIT: Created By (Answer to: "Which user has created a certain entity?")
CreatedBy=\u0E2A\u0E23\u0E49\u0E32\u0E07\u0E42\u0E14\u0E22

#XTIT: Created On (Answer to: "When has a certain entity been created?")
CreatedAt=\u0E2A\u0E23\u0E49\u0E32\u0E07\u0E40\u0E21\u0E37\u0E48\u0E2D

#XTIT: Changed By (Answer to: "Which user has changed a certain entity?")
ChangedBy=\u0E40\u0E1B\u0E25\u0E35\u0E48\u0E22\u0E19\u0E41\u0E1B\u0E25\u0E07\u0E42\u0E14\u0E22

#XTIT: Changed On (Answer to: "When has a certain entity been changed?")
ChangedAt=\u0E40\u0E1B\u0E25\u0E35\u0E48\u0E22\u0E19\u0E41\u0E1B\u0E25\u0E07\u0E40\u0E21\u0E37\u0E48\u0E2D

#XTIT: Currency
Currency=\u0E2A\u0E01\u0E38\u0E25\u0E40\u0E07\u0E34\u0E19

#XTIT: Currency Code
CurrencyCode=\u0E23\u0E2B\u0E31\u0E2A\u0E2A\u0E01\u0E38\u0E25\u0E40\u0E07\u0E34\u0E19

#XTIT: Currency Code Description
CurrencyCode.Description=\u0E23\u0E2B\u0E31\u0E2A\u0E2A\u0E01\u0E38\u0E25\u0E40\u0E07\u0E34\u0E19\u0E15\u0E32\u0E21\u0E17\u0E35\u0E48\u0E23\u0E30\u0E1A\u0E38\u0E43\u0E19 ISO 4217

#XTIT: Currency Symbol
CurrencySymbol=\u0E2A\u0E31\u0E0D\u0E25\u0E31\u0E01\u0E29\u0E13\u0E4C\u0E2A\u0E01\u0E38\u0E25\u0E40\u0E07\u0E34\u0E19

#XTIT: Currency Minor Unit Fractions (Answer to: "How many fractions has a currency's minor unit?", e.g. "0" or "2")
CurrencyMinorUnit=\u0E40\u0E28\u0E29\u0E2A\u0E48\u0E27\u0E19\u0E2B\u0E19\u0E48\u0E27\u0E22\u0E23\u0E2D\u0E07\u0E02\u0E2D\u0E07\u0E2A\u0E01\u0E38\u0E25\u0E40\u0E07\u0E34\u0E19

#XTIT: Country/Region
Country=\u0E1B\u0E23\u0E30\u0E40\u0E17\u0E28/\u0E20\u0E39\u0E21\u0E34\u0E20\u0E32\u0E04

#XTIT: Country/Region Code
CountryCode=\u0E23\u0E2B\u0E31\u0E2A\u0E1B\u0E23\u0E30\u0E40\u0E17\u0E28/\u0E20\u0E39\u0E21\u0E34\u0E20\u0E32\u0E04

#XTIT: Country/Region Code Description
CountryCode.Description=\u0E23\u0E2B\u0E31\u0E2A\u0E1B\u0E23\u0E30\u0E40\u0E17\u0E28/\u0E20\u0E39\u0E21\u0E34\u0E20\u0E32\u0E04\u0E15\u0E32\u0E21\u0E17\u0E35\u0E48\u0E23\u0E30\u0E1A\u0E38\u0E43\u0E19 ISO 3166-1

#XTIT: Language
Language=\u0E20\u0E32\u0E29\u0E32

#XTIT: Language Code
LanguageCode=\u0E23\u0E2B\u0E31\u0E2A\u0E20\u0E32\u0E29\u0E32

#XTIT: Language Code Description
LanguageCode.Description=\u0E23\u0E2B\u0E31\u0E2A\u0E20\u0E32\u0E29\u0E32\u0E15\u0E32\u0E21\u0E17\u0E35\u0E48\u0E23\u0E30\u0E1A\u0E38\u0E43\u0E19 ISO 639-1

#XTIT Time zone code
TimeZoneCode=\u0E23\u0E2B\u0E31\u0E2A\u0E40\u0E02\u0E15\u0E40\u0E27\u0E25\u0E32

#XTIT: User Identifier
UserID=ID \u0E1C\u0E39\u0E49\u0E43\u0E0A\u0E49

#XTIT: Any kind of name
Name=\u0E0A\u0E37\u0E48\u0E2D

#XTIT: Any kind of description
Description=\u0E04\u0E33\u0E2D\u0E18\u0E34\u0E1A\u0E32\u0E22

#XTOL: A user's unique Indentifier
UserID.Description=ID \u0E17\u0E35\u0E48\u0E44\u0E21\u0E48\u0E0B\u0E49\u0E33\u0E02\u0E2D\u0E07\u0E1C\u0E39\u0E49\u0E43\u0E0A\u0E49

#XTIT: Admin data for a draft document
Draft_DraftAdministrativeData=\u0E02\u0E49\u0E2D\u0E21\u0E39\u0E25\u0E14\u0E49\u0E32\u0E19\u0E01\u0E32\u0E23\u0E1A\u0E23\u0E34\u0E2B\u0E32\u0E23\u0E2A\u0E33\u0E2B\u0E23\u0E31\u0E1A\u0E41\u0E1A\u0E1A\u0E23\u0E48\u0E32\u0E07

#XTIT: Technical ID of a draft document
Draft_DraftUUID=\u0E41\u0E1A\u0E1A\u0E23\u0E48\u0E32\u0E07 (ID \u0E17\u0E32\u0E07\u0E40\u0E17\u0E04\u0E19\u0E34\u0E04)

#XTIT: Creation time of a draft
Draft_CreationDateTime=\u0E2A\u0E23\u0E49\u0E32\u0E07\u0E41\u0E1A\u0E1A\u0E23\u0E48\u0E32\u0E07\u0E40\u0E21\u0E37\u0E48\u0E2D

#XTIT: User created the draft
Draft_CreatedByUser=\u0E2A\u0E23\u0E49\u0E32\u0E07\u0E41\u0E1A\u0E1A\u0E23\u0E48\u0E32\u0E07\u0E42\u0E14\u0E22

#XTIT: The current user (me) created the draft
Draft_DraftIsCreatedByMe=\u0E41\u0E1A\u0E1A\u0E23\u0E48\u0E32\u0E07\u0E17\u0E35\u0E48\u0E09\u0E31\u0E19\u0E2A\u0E23\u0E49\u0E32\u0E07\u0E02\u0E36\u0E49\u0E19

#XTIT: Time a draft was last changed on
Draft_LastChangeDateTime=\u0E40\u0E1B\u0E25\u0E35\u0E48\u0E22\u0E19\u0E41\u0E1B\u0E25\u0E07\u0E41\u0E1A\u0E1A\u0E23\u0E48\u0E32\u0E07\u0E04\u0E23\u0E31\u0E49\u0E07\u0E25\u0E48\u0E32\u0E2A\u0E38\u0E14\u0E40\u0E21\u0E37\u0E48\u0E2D

#XTIT: User that changed the draft last
Draft_LastChangedByUser=\u0E40\u0E1B\u0E25\u0E35\u0E48\u0E22\u0E19\u0E41\u0E1B\u0E25\u0E07\u0E41\u0E1A\u0E1A\u0E23\u0E48\u0E32\u0E07\u0E04\u0E23\u0E31\u0E49\u0E07\u0E25\u0E48\u0E32\u0E2A\u0E38\u0E14\u0E42\u0E14\u0E22

#XTIT: User that is working on the draft
Draft_InProcessByUser=\u0E41\u0E1A\u0E1A\u0E23\u0E48\u0E32\u0E07\u0E2D\u0E22\u0E39\u0E48\u0E23\u0E30\u0E2B\u0E27\u0E48\u0E32\u0E07\u0E14\u0E33\u0E40\u0E19\u0E34\u0E19\u0E01\u0E32\u0E23\u0E42\u0E14\u0E22

#XTIT: The current user (me) is working on the draft
Draft_DraftIsProcessedByMe=\u0E41\u0E1A\u0E1A\u0E23\u0E48\u0E32\u0E07\u0E17\u0E35\u0E48\u0E09\u0E31\u0E19\u0E01\u0E33\u0E25\u0E31\u0E07\u0E14\u0E33\u0E40\u0E19\u0E34\u0E19\u0E01\u0E32\u0E23
