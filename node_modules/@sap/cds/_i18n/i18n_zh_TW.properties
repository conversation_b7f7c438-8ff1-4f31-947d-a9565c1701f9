#Text Types
#
#XACT: Text with explicit importance for accessibility.
#XBUT: Button
#XCKL: Checkbox
#XFLD: Field label
#XLNK: Hyperlink
#XMIT: Menu item (Menu item, either top-level like "File" or lower-level like "Save as...")
#XMSG: Message
#XRBL: Radio button
#XSEL: Selection (Values in a drop-down list, or a status. For example: "In Process", "Shipped" or "Open".)
#XTIT: Title (or heading) of a non-actionable user interface element like a column, wizard, or screen area.
#XTOL: Explanatory text for an UI element, such as a tooltip, input help.
#YINS: Instruction for a user, for example, a permanent text on a screen that introduces a group of fields.
#----------------------------------------------------------------------------------------------------------------------
#For text elements that are not supposed to be translated, use the text type NOTR
#----------------------------------------------------------------------------------------------------------------------
#Recommended pattern
#
#<TextType>:<AdditionalContextInformation>
#If there is a maximum length restriction, please indicate as shown below.
#<TextType>,<MaximumLength>:<AdditionalContextInformation>
#----------------------------------------------------------------------------------------------------------------------
# This is the resource bundle for foundation
# __ldi.translation.uuid=dd6c5800-b108-11e8-be90-bd1cf6ac87fb
#----------------------------------------------------------------------------------------------------------------------

#XTIT: Created By (Answer to: "Which user has created a certain entity?")
CreatedBy=\u5EFA\u7ACB\u8005

#XTIT: Created On (Answer to: "When has a certain entity been created?")
CreatedAt=\u5EFA\u7ACB\u65E5\u671F

#XTIT: Changed By (Answer to: "Which user has changed a certain entity?")
ChangedBy=\u66F4\u6539\u8005

#XTIT: Changed On (Answer to: "When has a certain entity been changed?")
ChangedAt=\u66F4\u6539\u65E5\u671F

#XTIT: Currency
Currency=\u5E63\u5225

#XTIT: Currency Code
CurrencyCode=\u5E63\u5225\u4EE3\u78BC

#XTIT: Currency Code Description
CurrencyCode.Description=\u5DF2\u6309 ISO 4217 \u6307\u5B9A\u516C\u53F8\u4EE3\u78BC

#XTIT: Currency Symbol
CurrencySymbol=\u5E63\u5225\u7B26\u865F

#XTIT: Currency Minor Unit Fractions (Answer to: "How many fractions has a currency's minor unit?", e.g. "0" or "2")
CurrencyMinorUnit=\u5E63\u5225\u6B21\u8981\u55AE\u4F4D\u5206\u6578

#XTIT: Country/Region
Country=\u570B\u5BB6/\u5730\u5340

#XTIT: Country/Region Code
CountryCode=\u570B\u5BB6/\u5730\u5340\u4EE3\u78BC

#XTIT: Country/Region Code Description
CountryCode.Description=\u5DF2\u6309 ISO 3166-1 \u6307\u5B9A\u570B\u5BB6/\u5730\u5340\u4EE3\u78BC

#XTIT: Language
Language=\u8A9E\u8A00

#XTIT: Language Code
LanguageCode=\u8A9E\u8A00\u4EE3\u78BC

#XTIT: Language Code Description
LanguageCode.Description=\u5DF2\u6309 ISO 639-1 \u6307\u5B9A\u516C\u53F8\u4EE3\u78BC

#XTIT Time zone code
TimeZoneCode=\u6642\u5340\u4EE3\u78BC

#XTIT: User Identifier
UserID=\u4F7F\u7528\u8005 ID

#XTIT: Any kind of name
Name=\u540D\u7A31

#XTIT: Any kind of description
Description=\u8AAA\u660E

#XTOL: A user's unique Indentifier
UserID.Description=\u4F7F\u7528\u8005\u5C08\u5C6C ID

#XTIT: Admin data for a draft document
Draft_DraftAdministrativeData=\u8349\u7A3F\u7BA1\u7406\u8CC7\u6599

#XTIT: Technical ID of a draft document
Draft_DraftUUID=\u8349\u7A3F (\u6280\u8853 ID)

#XTIT: Creation time of a draft
Draft_CreationDateTime=\u8349\u7A3F\u5EFA\u7ACB\u65E5\u671F

#XTIT: User created the draft
Draft_CreatedByUser=\u8349\u7A3F\u5EFA\u7ACB\u8005

#XTIT: The current user (me) created the draft
Draft_DraftIsCreatedByMe=\u6211\u6240\u5EFA\u7ACB\u7684\u8349\u7A3F

#XTIT: Time a draft was last changed on
Draft_LastChangeDateTime=\u8349\u7A3F\u7684\u4E0A\u6B21\u66F4\u6539\u65E5\u671F

#XTIT: User that changed the draft last
Draft_LastChangedByUser=\u8349\u7A3F\u7684\u4E0A\u6B21\u66F4\u6539\u8005

#XTIT: User that is working on the draft
Draft_InProcessByUser=\u6B63\u5728\u8655\u7406\u8349\u7A3F\u7684\u4EBA\u54E1

#XTIT: The current user (me) is working on the draft
Draft_DraftIsProcessedByMe=\u6211\u6B63\u5728\u8655\u7406\u7684\u8349\u7A3F
