/**
 * DO NO LONGER IMPORT THIS FILE
 *
 * Instead use:
 *
 * @example
 * ```
 *   import * as cds from '@sap/cds'
 *   cds.Request
 * or
 *   import { Request } from '@sap/cds'
 * or
 *   @type { import('@sap/cds').Request }
 * ```
 *
 * @deprecated
 */
export * from '@cap-js/cds-types/apis/linked'

// 'reflect' got removed with 7.4
// Also, these got removed/renamed in 7.4
export {
  CSN as ParsedModel,
  Definition,
  entity
} from '@cap-js/cds-types/apis/csn'

export {
  Definitions as ReflectedDefinitions,
  LinkedCSN as LinkedModel,
  LinkedCSN as ReflectedModel,
} from '@cap-js/cds-types/apis/linked'
