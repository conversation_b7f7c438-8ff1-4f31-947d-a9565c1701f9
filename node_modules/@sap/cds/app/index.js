const cds = require('../lib')
const { find, path, fs } = cds.utils

const odata = endpoint => endpoint.kind.startsWith('odata')
const metadata = endpoint => odata(endpoint) ? ` / <a href="${endpoint.path}/$metadata">$metadata</a>` : ``
const asHtmlId = s => s.replace(/[.:/$&?@]/g, '_').toLowerCase()

module.exports = { get html(){

  let html = fs.readFileSync(path.join(__dirname,'index.html'),'utf-8')
  // .replace ('{{subtitle}}', 'Version ' + cds.version)
  .replace (/{{package}}/g, _project())
  .replace (/{{app}}/g, cds.env.folders.app.replace(/*trailing slash*/ /\/$/, ''))
  .replace ('{{apps}}', _app_links().map(
      html => `\n<li><a href="${html}">/${html.replace(/^\//,'').replace('/index.html','')}</a></li>`
    ).join('\n') || '— none —'
  )
  .replace ('{{services}}', cds.service.providers
  .filter(srv => !srv._is_dark)
  .flatMap(srv => srv.endpoints.map(endpoint => ({srv, endpoint})))
  .map (({srv, endpoint}) => `
      <div id="${asHtmlId(srv.name)}-${endpoint.kind}">
        <h3>
          <a href="${endpoint.path}">${endpoint.path}</a>${metadata(endpoint)} ${_moreLinks(srv, endpoint)}
        </h3>
        <ul>${_entities_in(srv).map (e => {
          return `
          <li id="${asHtmlId(srv.name)}-${endpoint.kind}-${asHtmlId(e)}">
            <a href="${endpoint.path}/${e.replace(/\./g, '_')}">${e}</a> ${_moreLinks(srv, endpoint, e)}
          </li>`}).join('')}
        </ul>
      </div>
  `).join(''))

  Object.defineProperty (this,'html',{value:html})
  return html

}}

function _app_links() {
  const folder = path.resolve (cds.root, cds.env.folders.app)
  const files = find (folder, ['*.html', '*/*.html', '*/*/*.html']).map (
    file => path.relative(folder,file).replace (/\\/g,'/')
  )
  return files.concat (cds.app._app_links || [])
}

function _entities_in (service) {
  const exposed=[], {entities} = service
  for (let each in entities) {
    const e = entities [each]
    if (e['@cds.autoexposed'] && !e['@cds.autoexpose'])  continue
    if (/DraftAdministrativeData$/.test(e.name))  continue
    if (/[._]texts$/.test(e.name))  continue
    if (cds.env.effective.odata.containment && each.includes('.') && 'up_' in e.elements && e.elements.up_.target.startsWith(service.namespace+'.')) continue
    exposed.push (each.replace(/\./g,'_'))
  }
  return exposed
}

function _moreLinks (srv, endpoint, entity) {
  return (srv.$linkProviders || [])
    .map (linkProv => linkProv(entity, endpoint))
    .filter (l => l?.href && l?.name)
    .sort ((l1, l2) => l1.name.localeCompare(l2.name))
    .map (l => ` <a class="preview" href="${l.href}" title="${l.title||l.name}"> &rarr; ${l.name}</a>`)
    .join (' ')
}

function _project(){
  const cwd = cds.root
  try {
    const pj = require(cwd+'/package.json')
    return `${pj.name} ${pj.version}`
  } catch(e) {
    return `${cwd}`
  }
}
