#!/usr/bin/env node
const cds = require('../lib') //> ensure we are the first to load @sap/cds locally
const cli = {

  exec (...argv) {
    if (!argv.length) argv = process.argv.slice(2)
    const task = require ('./serve')

    let args = []
    try {
      args = this.args(task, argv)
    } catch (err) { process.exitCode = 1; return console.error(err) }

    return task.apply (this, args)
  },

  // TODO replace w/ common arg parser from node
  args (task, argv) {

    const { options:o=[], flags:f=[], shortcuts:s=[] } = task
    const _global = /^--(profile|production|sql|odata|build-.*|cdsc-.*|odata-.*|folders-.*)$/
    const _flags = { '--production':true }
    const options = {}, args = []
    let k,a, env = null

    if (argv.length) for (let i=0; i < argv.length; ++i) {
      if ((a = argv[i])[0] !== '-') args.push(a)
      else if ((k = s.indexOf(a)) >= 0) k < o.length ? add(o[k],argv[++i]) : add(f[k-o.length])
      else if ((k = o.indexOf(a)) >= 0) add(o[k],argv[++i])
      else if ((k = f.indexOf(a)) >= 0) add(f[k])
      else if (_global.test(a)) add_global(a, _flags[a] || argv[++i])
      else throw 'Invalid option: '+ a
    }
    // consistent production setting for NODE_ENV and CDS_ENV
    if (process.env.NODE_ENV !== 'production') process.env.NODE_ENV = process.env.CDS_ENV?.split(',').find(p => p === 'production') || process.env.NODE_ENV
    else process.env.CDS_ENV = Array.from(new Set([...process.env.CDS_ENV?.split(',') ?? [], 'production']))

    function add (k,v) { options[k.slice(2)] = v || true }
    function add_global (k,v='') {
      if (k === '--production') return process.env.CDS_ENV = Array.from(new Set([...process.env.CDS_ENV?.split(',') ?? [], 'production']))
      if (k === '--profile')    return process.env.CDS_ENV = Array.from(new Set([...process.env.CDS_ENV?.split(',') ?? [], ...v.split(',')]))
      if (k === '--odata') v = { flavor:v }
      let e=env || (env={}), path = k.slice(2).split('-')
      while (path.length > 1) { let p = path.shift(); e = e[p]||(e[p]={}) }
      add (k, e[path[0]] = v)
    }

    if (env) cds.env.add (env)
    return [ args, options ]
  },
}

module.exports = Object.assign ((..._) => cli.exec(..._), cli)
if (!module.parent)  cli.exec()

/* eslint no-console:off */
