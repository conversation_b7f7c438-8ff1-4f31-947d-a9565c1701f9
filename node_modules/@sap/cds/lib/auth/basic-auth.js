module.exports = function basic_auth (options) {

  const cds = require ('../index'), LOG = cds.log('auth'), { decodeURIComponent } = cds.utils
  const users = require ('./mocked-users') (options)
  const login_required = options.login === 'required' || process.env.NODE_ENV === 'production' && options.credentials || cds.requires.multitenancy

  /** @type { import('express').Handler } express_handler */
  return async function basic_auth (req, res, next) {
    // REVISIT: passport also adds a login function with different meaning -> we need to be able to recognize ours -> req._login for now
    // allow subsequent code to request a user login
    req._login = login
    // get basic authorization header
    let auth = req.headers.authorization
    // enforce login if requested
    if (!auth?.match(/^basic/i)) return login_required ? req._login('Logged-in user required!') : next()
    // decode user credentials from autorization header
    let [id,pwd] = Buffer.from(auth.slice(6),'base64').toString().split(':')
    // verify user credentials and set req.user
    let u = req.user = await users.verify (id, pwd)
    // re-request login in case of wrong credentials
    if (u.failed) return req._login (u)
    // set req.tenant
    if (u.tenant) req.tenant = u.tenant
    // support for feature toggles via req.headers.features
    if (req.headers.features) u = req.user = { ...u, features: req.headers.features } // NOTE: need to clone u
    // done...
    if (LOG._debug) LOG.debug('authenticated user:', u)
    next()
  }

  function login (reason='') {
    const req=this, res=req.res
    // REVISIT: this json response is needed to be OData compliant. however, we should probably throw an error anyway so that a custom error middleware can get invoked.
    res.set('www-authenticate', `Basic realm="Users"`).status(401).json({ error: { code: '401', message: 'Unauthorized' } })
    LOG.info (req.method, decodeURIComponent(req.path), '>', res.statusCode, res.statusMessage, ...(!reason ? [] : ['-', reason]))
  }
}
