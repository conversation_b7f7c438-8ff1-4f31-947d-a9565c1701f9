const cds = require('../')
const LOG = cds.log('auth')

// _require for better error message
const _require = require('../../libx/_runtime/common/utils/require')

let xssec = _require('@sap/xssec')
let xssec4 = false
// use v3 compat api
if (xssec.v3) {
  xssec = xssec.v3
  xssec4 = true
}

// getter function extracted to show deprecation warning only once
const _getTokenInfo = tokenInfo => tokenInfo

module.exports = function ias_auth(config) {
  const { kind, credentials, known_claims } = config

  if (!credentials) {
    let msg = `Authentication kind "${kind}" configured, but no IAS instance bound to application.`
    msg += ' Either bind an IAS instance, or switch to an authentication kind that does not require a binding.'
    throw new Error(msg)
  }

  // cds.env.requires.auth.known_claims is not an official config!
  const KNOWN_CLAIMS = new Set(known_claims || require('./ias-claims'))

  function getUser(tokenInfo) {
    const payload = tokenInfo.getPayload()

    const clientid = tokenInfo.getClientId()
    if (clientid === payload.sub) {
      //> grant_type === client_credentials or x509
      const roles = ['system-user']
      if (clientid === credentials.clientid) roles.push('internal-user')
      return new cds.User({ id: 'system', roles, tokenInfo })
    }

    // add all unknown attributes to req.user.attr in order to keep public API small
    const attr = Object.keys(payload)
      .filter(k => !KNOWN_CLAIMS.has(k))
      .reduce((attr, k) => { attr[k] = payload[k]; return attr }, {})

    // same api as xsuaa-auth for easier migration
    if (attr.user_name) attr.logonName = attr.user_name
    if (attr.given_name) attr.givenName = attr.given_name
    if (attr.family_name) attr.familyName = attr.family_name

    return new cds.User({ id: payload.sub, attr, tokenInfo })
  }

  return (req, _, next) => {
    const token = req.headers.authorization?.split(/^bearer /i)[1]

    if (!token && xssec4) {
      LOG._debug && LOG.debug('No authorization header provided, continuing with default user.')
      req.user = new cds.User.default()
      return next()
    }

    xssec.createSecurityContext(token, credentials, 'IAS', function (err, securityContext, tokenInfo) {
      // REVISIT: ias impl not as sophisticated as xsuaa impl, so we need to be more tolerant here -> xssec issue 221
      // if (err && !tokenInfo) {
      //   // here, there is a general problem, .e.g., bad credentials -> throw the error
      //   return next(err)
      // }

      if (err && LOG._debug) LOG.debug('User could not be authenticated due to error:', err)

      // if no general problem, tokenInfo object is always available -> add to req via getter for compat reasons
      // -> the "always available" part is not true for ias (see REVISIT above)
      tokenInfo && Object.defineProperty(req, 'tokenInfo', {
        get() {
          return cds.utils.deprecated(_getTokenInfo, {kind: 'Property', old: 'req.tokenInfo'})(tokenInfo)
         }
      })

      if (!securityContext) {
        if (!req.headers.authorization) {
          LOG._debug && LOG.debug('No authorization header provided, continuing with default user.')
          req.user = new cds.User.default()
          return next()
        }
        return next(new cds.error('Unauthorized', { statusCode: 401 }))
      }

      req.user = getUser(tokenInfo)
      req.tenant = tokenInfo.getZoneId()

      req.authInfo = securityContext //> compat req.authInfo

      next()
    })
  }
}
