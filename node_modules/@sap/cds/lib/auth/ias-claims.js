module.exports = Object.values({
  /*
   * JWT claims (https://datatracker.ietf.org/doc/html/rfc7519#section-4)
   */
  ISSUER: 'iss',
  SUBJECT: 'sub',
  AUDIENCE: 'aud',
  EXPIRATION_TIME: 'exp',
  NOT_BEFORE: 'nbf',
  ISSUED_AT: 'iat',
  JWT_ID: 'jti',
  /*
   * TokenClaims (com.sap.cloud.security.token.TokenClaims)
   */
  // ISSUER: "iss", //> already in JWT claims
  IAS_ISSUER: 'ias_iss',
  // EXPIRATION: "exp", //> already in JWT claims
  // AUDIENCE: "aud", //> already in JWT claims
  // NOT_BEFORE: "nbf", //> already in JWT claims
  // SUBJECT: "sub", //> already in JWT claims
  // USER_NAME: 'user_name', //> do not exclude
  // GIVEN_NAME: 'given_name', //> do not exclude
  // FAMILY_NAME: 'family_name', //> do not exclude
  // EMAIL: 'email', //> do not exclude
  SAP_GLOBAL_SCIM_ID: 'scim_id',
  SAP_GLOBAL_USER_ID: 'user_uuid', //> exclude for now
  SAP_GLOBAL_ZONE_ID: 'zone_uuid',
  // GROUPS: 'groups', //> do not exclude
  AUTHORIZATION_PARTY: 'azp',
  CNF: 'cnf',
  CNF_X5T: 'x5t#S256',
  // own
  APP_TENANT_ID: 'app_tid'
})
