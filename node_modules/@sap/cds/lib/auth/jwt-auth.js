const cds = require('../')
const LOG = cds.log('auth')

// _require for better error message
const _require = require('../../libx/_runtime/common/utils/require')

let xssec = _require('@sap/xssec')
let xssec4 = false
// use v3 compat api
if (xssec.v3) {
  xssec = xssec.v3
  xssec4 = true
}

// getter function extracted to show deprecation warning only once
const _getTokenInfo = tokenInfo => tokenInfo

module.exports = function jwt_auth(config) {
  const { kind, credentials } = config

  if (!credentials) {
    let msg = `Authentication kind "${kind}" configured, but no XSUAA instance bound to application.`
    msg += ' Either bind an XSUAA instance, or switch to an authentication kind that does not require a binding.'
    throw new Error(msg)
  }

  function getUser(tokenInfo) {
    const payload = tokenInfo.getPayload()

    let id = payload.user_name

    // Roles = scope names w/o xsappname
    const xsappname = new RegExp(`^${credentials.xsappname}\\.`)
    let roles = payload.scope.map(s => s.replace(xsappname, ''))

    // Disallow setting system roles from external
    roles = roles.filter(r => !(r in { 'internal-user': 1, 'system-user': 1 }))

    if (payload.grant_type in { client_credentials: 1, client_x509: 1 }) {
      id = 'system'
      roles.push('system-user')
      if (tokenInfo.getClientId() === credentials.clientid) roles.push('internal-user')
    }

    const attr = Object.assign({}, payload['xs.user.attributes'])
    if (kind === 'xsuaa') {
      attr.logonName = payload.user_name
      attr.givenName = payload.given_name
      attr.familyName = payload.family_name
      attr.email = payload.email
    }

    return new cds.User({ id, roles, attr, tokenInfo })
  }

  return (req, _, next) => {
    const token = req.headers.authorization?.split(/^bearer /i)[1]

    if (!token && xssec4) {
      LOG._debug && LOG.debug('No authorization header provided, continuing with default user.')
      req.user = new cds.User.default()
      return next()
    }

    xssec.createSecurityContext(token, credentials, function (err, securityContext, tokenInfo) {
      if (err && !tokenInfo) {
        // here, there is a general problem, .e.g., bad credentials -> throw the error
        return next(err)
      }

      if (err && LOG._debug) LOG.debug('User could not be authenticated due to error:', err)

      // if no general problem, tokenInfo object is always available -> add to req via getter for compat reasons
      Object.defineProperty(req, 'tokenInfo', {
        get() {
          return cds.utils.deprecated(_getTokenInfo, {kind: 'Property', old: 'req.tokenInfo'})(tokenInfo)
        }
      })

      if (!securityContext) {
        if (!req.headers.authorization) {
          LOG._debug && LOG.debug('No authorization header provided, continuing with default user.')
          req.user = new cds.User.default()
          return next()
        }
        return next(new cds.error('Unauthorized', { statusCode: 401 }))
      }

      req.user = getUser(tokenInfo)
      req.tenant = tokenInfo.getZoneId()

      req.authInfo = securityContext //> compat req.authInfo

      next()
    })
  }
}
