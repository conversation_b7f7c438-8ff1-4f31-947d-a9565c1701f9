const cds = require('../..'), {env} = cds
const DEBUG = cds.debug('alpha|_localized')
const _locales_4sql = {
	sqlite : env.i18n.for_sqlite || env.i18n.for_sql || [],
	plain  : env.i18n.for_sql || [],
}

const { _texts_entries, _localized_entries } = env.cdsc.cv2 || {}
const _been_here = Symbol('is _localized')



/**
 * In case of old SQLite service, for each localized_<view> we get from the
 * compiler, create additional views localized_<locale>_<views>
 */
function unfold_ddl (ddl, csn, o={}) { // NOSONAR
	if (o.betterSqliteSessionVariables) return ddl
	if (o.fewerLocalizedViews) return ddl
	const _locales = _locales_4sql[o.dialect]; if (!_locales)  return ddl
	const localized_views = ddl.filter (each => each.startsWith('CREATE VIEW localized_') || each.startsWith('DROP VIEW localized_'))
	// REVISIT: analyze ddl statements. Problem is with s<PERSON><PERSON> calling this function containing only drops
	for (const localized_view of localized_views) {
		for (const locale of _locales) ddl.push (localized_view
			.replace (/localized_/g, `localized_${locale}_`)
			.replace (/\.locale = 'en'/g, `.locale = '${locale}'`)
		)
	}
	DEBUG && localized_views.length && DEBUG ('Added localized views to DDL for', csn.$sources)
	return ddl
}



/**
 * Add localized. entries and localized.<locale> entries (as in compiler v1) to reflect what
 * For each localized.<view> we get from the compiler, ensure there's a
 * corresponding localized.<locale>. entry in the model to support reflection.
 * In addition
 */
function unfold_csn (m) { // NOSONAR

	// only do that once per model
	if (!m || m[_been_here]) return m
	// eslint-disable-next-line no-console
	DEBUG && DEBUG ('unfolding csn...')
	const pass2 = []

	// REVISIT: Fixing $localized obtained from compiler when fewerLocalizedViews is set
	const o = cds.compiler._options.for.sql()
	if (o.fewerLocalizedViews) for (let each in m.definitions) {
		const d = m.definitions [each]
		if (d.$localized && d.elements && !d.elements.localized) delete d.$localized
	}

	const _conf = env.requires.db || env.requires.sql || env.requires.kinds.sql
	const _on_sqlite = _conf.kind === 'sqlite' || _conf.dialect === 'sqlite'
	const _locales = !o.fewerLocalizedViews && !o.betterSqliteSessionVariables && _on_sqlite && _locales_4sql.sqlite

	// Pass 1 - add localized.<locale> entities and views
	for (const each in cds.linked(m).definitions) {
		const d = m.definitions [each]
		// Add <entry>_texts proxies for all <entry>.texts entities
		if (_texts_entries !== false && each.endsWith('.texts')) {
			_add_proxy4 (d, each.slice(0,-6)+'_texts')
		}
		// Add localized.<entry> for all entities having localized views in db
		if (_localized_entries !== false && _is_localized(d)) {
			_add_proxy4 (d,`localized.${each}`, x => pass2.push([x]))
			// if running on sqlite add additional localized.<locale>. views
			if (_locales) for (const locale of _locales) {
				_add_proxy4 (d,`localized.${locale}.${each}`, x => pass2.push([x,locale]))
			}
		}
	}

	// Pass 2 - redirect associations/compositions in elements to localized.<locale> targets
	for (const [x,locale] of pass2) {
		let overlayed = null
		for (const each in x.elements) {
			const e = x.elements [each]
			if (e._target && _is_localized(e._target)) {
				const elements = overlayed ??= x.elements = {__proto__:x.elements}
				const target = locale ? `localized.${locale}.${e.target}` : `localized.${e.target}`
				const _target = m.definitions[target]
				if (_target) {
					elements[each] = Object.defineProperty ({__proto__:e,target},'_target',{value:_target})
					DEBUG && DEBUG ('overriding:', each, ':', elements[each], 'in', { entity: x.name })
				}
				else DEBUG && DEBUG ('NOT!! overriding:', each, ':', elements[each], 'in', { entity: x.name })
			}
		}
	}

	// done
	DEBUG && pass2.length && DEBUG ('Added localized views for sqlite to csn for', m.$sources)
	return Object.defineProperty (m, _been_here, {value:true})

	function _add_proxy4 (d, name, callback) {
		if (name in m.definitions) return DEBUG && DEBUG ('NOT overriding existing:', name)
		const x = {__proto__:d, name };   DEBUG && DEBUG ('adding proxy:', x)
		Object.defineProperty (m.definitions, name, {value:x,writable:true,configurable:true})
		if (callback) callback(x)
	}
}


const _is_localized = d => d.own('$localized') // as set by compiler in compile.for.odata

// feature-toggled exports
module.exports = { unfold_csn, unfold_ddl }
if (!env.features.localized) Object.assign (module.exports, { unfold_csn: x=>x, unfold_ddl: x=>x })
