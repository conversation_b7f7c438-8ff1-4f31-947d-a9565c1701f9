const { unfold_csn } = require ('../etc/_localized')
const cds = require ('../../index')
const TRACE = cds.debug('trace')

module.exports = function cds_compile_for_nodejs (csn,o) {
  if ('_4nodejs' in csn) return csn._4nodejs
  TRACE?.time('cds.compile 4nodejs'.padEnd(22))
  let dsn = csn // cds.minify (csn)
  dsn = cds.compile.for.odata (csn,o) //> creates a partial copy -> avoid any cds.linked() before
  dsn = unfold_csn (dsn)
  dsn = cds.linked (dsn)
  if (cds.env.fiori.lean_draft) cds.compile.for.lean_drafts(dsn, o)
  Object.defineProperty (csn, '_4nodejs', {value:dsn})
  Object.defineProperty (dsn, '_4nodejs', {value:dsn})
  TRACE?.timeEnd('cds.compile 4nodejs'.padEnd(22))
  return dsn
}
