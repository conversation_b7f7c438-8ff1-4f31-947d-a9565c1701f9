const { Worker, parentPort, isMainThread, workerData } = require('node:worker_threads')
const cds = require('../../index.js'), { path, local, read, exists } = cds.utils
const TRACE = cds.debug('trace')
const LOG = cds.log('cds|edmx')
const OUT = process.env.cds_test_temp || path.join (cds.root,'_out')

// -----------------------------------------------------------------------
//
//   Main Thread Part
//

if (isMainThread) {

  module.exports = exports = (csn, tenant, features) => {
    const defs = Object.entries(csn.definitions), protocols = cds.service.protocols
    const services = defs.filter(([,d]) => d.kind === 'service' && 'odata' in protocols.for(d)).map(([k]) => k)
    if (!services.length) return LOG.debug (`No service definitions found in given model(s).`)
    let dir = path.join (OUT, tenant||'', features||'')
    LOG.info ('generating edmx files to', { dir: local(dir) }, '\n')
    return GENERATE ({ csn, dir, services })
  }

  const GENERATE = _generate_using_workers // for running in worker threads
  // const GENERATE = _generate_edmxs      // for running in main thread

  // eslint-disable-next-line no-inner-declarations
  async function _generate_using_workers (workerData) {
    await new Promise((resolve, reject) => new Worker (__filename, { workerData })
    .on('error', reject)
    .on('message', msg => {
      if (msg.error) return reject (new cds.error(msg.error))
      if (msg === 'done') return resolve()
      else LOG.debug (msg)
    }))
    exports.get = _read_generated_edmx4
  }

  // eslint-disable-next-line no-inner-declarations
  function _read_generated_edmx4 (srv, kind='edmx', { tenant, features }={}) {
    let dir = path.join (OUT, tenant||'', features||'')
    let file = path.join (dir, srv.definition.name+'.'+kind)
    if (!exists(file)) throw new Error (`No generated edm(x) file found at: ${file}`)
    return read (file)
  }

  exports.dir = OUT
}



// -----------------------------------------------------------------------
//
//   Worker Thread Part
//


if (!isMainThread) _generate_edmxs (workerData)
.catch (e => parentPort.postMessage({ error: e }))
.then (() => parentPort.postMessage('done'))


async function _generate_edmxs ({ csn, dir, services }) {

  const { mkdir, writeFile } = cds.utils.fs.promises
  await mkdir (dir, { recursive: true })
  const cdsc = cds.compiler
  const promises = []

  TRACE?.time(`cdsc.generate edmxs`.padEnd(22))

  // call cdsc.to.odata to generate edm/xs
  let odataVersion = cds.env.odata.version
  let suffixes = { edmx: '.edmx' }
  let compile = cdsc.to.edmx // default is edmx only
  if (!cds.env.features.odata_new_adapter) {
    compile = cdsc.to.odata // edmx and edm.json
    suffixes.edm = '.edm.json'
  }

  let result = compile.all (csn, { serviceNames: services, messages:[] })

  // write edmx files to disk
  for (let [name,x] of Object.entries(result[odataVersion])) {
    for (let suffix in suffixes) {
      let content = suffix === 'edmx'? x[suffix] : JSON.stringify (x[suffix], minify);
      let file = path.join (dir, name + suffixes[suffix])
      let p = writeFile (file, content)
        .then (() => parentPort?.postMessage ({ generated: local(file) }))
      promises.push(p)
    }
  }
  await Promise.all (promises)
  TRACE?.timeEnd(`cdsc.generate edmxs`.padEnd(22))
  return true
}


function minify (k,v) {
  if (k === '$ReferentialConstraint') return
  if (k === '$Reference') return
  if (k === '$OnDelete') return
  if (k === '$MaxLength') return
  if (k === '$Annotations') {
    const v2 = {}
    for (let a in v) {
      const a1=v[a], a2 = {}
      for (let p in a1) {
        if (!/^@(UI|Common|Analytics|Core.Description|Core.Operation|Capabilities.Navigation|Measures|Validation)/.test(p)) a2[p] = a1[p]
      }
      if (Object.keys(a2).length) v2[a] = a2
    }
    return v2
  }
  // if (k === '$Type' && v === 'Edm.Guid') return
  else return v
}
