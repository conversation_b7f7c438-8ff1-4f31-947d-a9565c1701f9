const cds = require ('../..')
const LOG = cds.log()
// eslint-disable-next-line cds/no-missing-dependencies -- needs to be added by app dev
const { SchemaGenerator } = require('@cap-js/graphql/lib/schema')

// REVISIT: remove module with cds^8
function cds_compile_to_gql (csn) {
  LOG._warn && LOG.warn('WARNING: cds.compile.to.gql and .to.graphql will require @cap-js/graphql >= 0.9.0 with an upcoming release. Please update your dependency.')

  const m = cds.linked(csn)
  const services = Object.fromEntries(m.services.map(s => [s.name, new cds.ApplicationService(s.name, m)]))
  return new SchemaGenerator().generate(services).printSchema()
}

module.exports = cds_compile_to_gql
