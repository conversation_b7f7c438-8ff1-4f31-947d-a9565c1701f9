const cds = require ('../..')
const cdsc = require ('../cdsc')
const sqliteKeywords = cdsc.to.sql.sqlite.keywords
const postgresKeywords = cdsc.to.sql.postgres?.keywords // requires @sap/cds-compiler >= 4.8.0
const { unfold_ddl } = require ('../etc/_localized')
const TRACE = cds.debug('trace')

function cds_compile_to_sql (csn,_o) {
  TRACE?.time('cdsc.compile 2sql'.padEnd(22))
  csn = cds.minify(csn)
  const o = cdsc._options.for.sql(_o) //> used twice below...
  const all = cdsc.to.sql(csn,o) .map (each => each.replace(/^-- .+\n/,''))  //> strip comments
  const sql = unfold_ddl(all, csn, o)
  TRACE?.timeEnd('cdsc.compile 2sql'.padEnd(22))
  if (o.as === 'str') return `\n${sql.join('\n\n')}\n`
  return sql
}


function cds_compile_to_hdbtable (csn,o) {
  const all = cdsc.to.hdi (cds.minify(csn),o)
  return _2many(all)
}

function cds_compile_to_deltaSql (csn, o, beforeCsn) {
  const options = cdsc._options.for.sql(o)
  if (typeof beforeCsn === 'string') beforeCsn = JSON.parse(beforeCsn)
  const { afterImage, drops, createsAndAlters } = cdsc.to.deltaSql (csn, options, beforeCsn || {definitions: {}, $version: '2.0'} ); // FIXME: As default value in compiler API?
  return {
    afterImage,
    drops: unfold_ddl(drops.map (each => each.replace(/^-- .+\n/,'')), csn, options),
    createsAndAlters: unfold_ddl(createsAndAlters.map (each => each.replace(/^-- .+\n/,'')), csn, options)
  };
}

function cds_compile_to_hdbcds (csn,o) {
  const all = cdsc.to.hdbcds (cds.minify(csn),o)
  const constructFileName = (fileName) => {
    const identifier = fileName.split('.');
    const suffix = identifier.pop();
    return identifier.join('_').concat(`.${suffix}`);
  }
  return _2many(all, constructFileName);
}

function* _2many (all,_file=f=>f) {
  for (let file in all) yield [
    all[file].replace(/^(\/\/|--) generated by .+\n/,''),
    { file:_file(file) }
  ]
}

module.exports = Object.assign (cds_compile_to_sql, {
  hdbcds: cds_compile_to_hdbcds,
  hdbtable: cds_compile_to_hdbtable,
  delta: cds_compile_to_deltaSql,
  sqlite: { keywords: sqliteKeywords },
  postgres: { keywords: postgresKeywords },
})
