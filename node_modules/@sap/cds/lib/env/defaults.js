const production = process.env.NODE_ENV === 'production'

const defaults = module.exports = {

  production,

  requires: require('./cds-requires'),

  server: {
    force_exit_timeout: 1111,
    port: 4004,
  },

  protocols: {
    'odata-v4' : { path: '/odata/v4' },
    'odata-v2' : { path: '/odata/v2' },
    'rest' : { path: '/rest' },
    'hcql' : { path: '/hcql' },
  },

  features: {
    folders: 'fts/*', // where to find feature toggles -> switch on by default when released
    pre_compile_edmxs: false,
    odata_new_adapter: false, // switch default with cds^8
    live_reload: !production,
    in_memory_db: !production,
    test_data: !production,
    test_mocks: !production,
    with_mocks: !production,
    mocked_bindings: !production,
    // skip_unused: 'all',
    skip_unused: true,
    deploy_data_onconflict: 'insert',
    one_model: true,
    localized: true,
    assert_integrity: false,
    cds_tx_inheritance: true,
    serve_on_root: false,
    precise_timestamps: false,
    string_decimals: false, // switch default with cds^8
  },

  fiori: {
    preview: !production,
    routes: !production,
    lean_draft: true,
    wrap_multiple_errors: true, // switch default with cds^8
    draft_lock_timeout: true,
    draft_deletion_timeout: false, // switch default with cds^8
    draft_compat: undefined,
    '[better-sqlite]': { lean_draft: true },
    '[better-hana]': { lean_draft: true },
    '[lean-draft]': { lean_draft: true },
    '[draft-compat]': { draft_compat: true },
  },

  ql: {
    quirks_mode: true, // IMPORTANT: Remove that for cds^7 !!
  },

  log: {
    Logger: undefined, //> use default
    '[development]': { format: 'plain' },
    '[production]': { format: 'json' },
    levels: {
      compile: 'warn',
      cli:    'warn'
    },
    service: false,
    // the rest is only applicable for the json formatter
    user: false,
    mask_headers: ['/authorization/i', '/cookie/i', '/cert/i', '/ssl/i'],
    aspects: ['./aspects/cf', './aspects/als'], //> EXPERIMENTAL!!!
    // adds custom fields in kibana's error rendering (unknown fields are ignored); key: index
    // note: custom fields are a feature of Application Logging Service (ALS) and not Kibana per se
    als_custom_fields: {
      // sql
      query: 0,
      // generic validations
      target: 1, details: 2
    }
  },

  folders: { // IMPORTANT: order is significant for cds.load('*')
    db: 'db/',
    srv: 'srv/',
    app: 'app/',
  },

  i18n: {
    file: 'i18n', // file basename w/o extension
    folders: ['_i18n', 'i18n', 'assets/i18n'],
    for_sqlite: ['de', 'fr'],
    for_sql: ['de', 'fr'],
    languages: 'all', // or array.  'all': whatever language files are found next to models
    default_language: 'en',
    fallback_bundle: '',
    preserved_locales: [
      // IMPORTANT: Never, never modify this list, as that would break existing projects !!!!
      // Projects can and have to override if they want something different.
      'en_GB',
      'es_CO',
      'es_MX',
      'fr_CA',
      'pt_PT',
      'zh_CN',
      'zh_HK',
      'zh_TW'
    ]
  },

  odata: {
    flavors: {
      v2: {
        version: 'v2',
        // containment:false,
        // structs:false,
        // refs:false, //> proxies:false,
      },
      v4: {
        version: 'v4',
        // containment:false,
        // structs:false,
        // refs:false, //> proxies:false,
      },
      w4: { // for ODM with Fiori clients
        version: 'v4',
        containment:true,
        structs:true,
        refs:false, //> proxies:false,
        xrefs:false,
      },
      x4: { // for A2X APIs
        version: 'v4',
        containment:true,
        structs:true,
        refs:true, //> proxies:true,
        xrefs:true,
      },
    },
    version: 'v4', // following is to support code completion only...
    structs: undefined,
    refs: undefined,
    proxies: undefined,
    containment: undefined,
  },

  sql: {
    /**
     * Allows to skip generating transitive localized views for entities which don't have own localized elements, but only associations to such.
     * - `undefined` → skipped for new db services.
     * - `false` → always skipped.
     * - `true` → never skipped.
     */
    transitive_localized_views: true,
    '[java]': {
      transitive_localized_views: false
    },
    native_hana_associations: true,
    names: 'plain', // or 'quoted', or 'hdbcds'
    dialect: 'sqlite' // or 'plain' or 'hana'
  },

  hana: {
    'deploy-format': 'hdbtable',
    journal:  {
      'change-mode': 'alter'
    }
  },

  build: {
    target: 'gen',
    '[java]': {
      target: '.'
    }
  },

  mtx: {
    api: {
      model: true,
      provisioning: true,
      metadata: true,
      diagnose: true
    },
    domain: '__default__'
  },

  cdsc: {
    moduleLookupDirectories: ['node_modules/'],
    '[java]': {
      betterSqliteSessionVariables: true,
      moduleLookupDirectories: ['node_modules/', 'target/cds/'],
    }
    // cv2: {
    //   _localized_entries: true,
    //   _texts_entries: true,
    // }
    // toSql: { associations: 'joins' },
    // newCsn: true,
  },

  query: {
    limit: {
      max: 1000
    }
  },

}

require('./plugins')(defaults)
