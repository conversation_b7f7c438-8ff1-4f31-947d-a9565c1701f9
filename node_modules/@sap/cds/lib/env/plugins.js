// REVISIT: we should have a real modular plugin technique for cds.env
module.exports = function add_mtx_env (env) {

  const mtx_env = require('@sap/cds-mtxs/env') // eslint-disable-line cds/no-missing-dependencies
  if (mtx_env) {
    const {requires} = env, {kinds} = requires
    Object.assign (env, mtx_env, {requires})
    Object.assign (requires, mtx_env.requires, {kinds})
    Object.assign (kinds, mtx_env.requires?.kinds)
  }

  function require (id) {
    try { return module.require(id) }
    catch(e) { if (e.code !== 'MODULE_NOT_FOUND') throw e }
  }

  return env
}
