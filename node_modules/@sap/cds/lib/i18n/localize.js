const cds = require('..')
const {existsSync, readdirSync} = require ('fs')
const {join,dirname,resolve,parse,sep} = require ('path')

const DEBUG = cds.debug('i18n')
const _node_modules = cds.env.cdsc.moduleLookupDirectories.map(d => sep+d.slice(0, -1))

module.exports = Object.assign (localize, {
  localize, lookup, bundles4, folders4, folder4, bundle4, files4, allLocales4
})

/**
 * Can be used like that:
 * @example cds.i18n.lookup('CreatedAt','de')
 */
function lookup (key, locale, model = cds.context?.model || cds.model) {
  let bundle = bundle4 (model, locale)
  return bundle?.[key]
}

/**
 * Can be used like that:
 * @example cds.localize(edmx,'de')
 */
function localize (aString, locale, model = cds.context?.model || cds.model, ext={}) {

  // support for legacy signature
  if (typeof aString === 'object') [ aString, model ] = [ model, aString ]

  // in case of multiple locales, return a generator
  if (Array.isArray(locale)) return (function*(){
    let localized, bundles = bundles4 (model, locale)
    if (bundles?.[Symbol.iterator]) for (let [lang,each] of bundles) {
      localized = _localize_with (each)
      yield [ localized, {lang} ]
    }
    if (!localized) yield [ aString, {lang:''} ]
  })()

  // otherwise return a single localized string
  let bundle = bundle4 (model, locale)
  return _localize_with (bundle)

  function _localize_with (bundle) {
    if (!bundle || !aString) return aString
    if (typeof aString === 'object') aString = JSON.stringify(aString)
    const escape = aString.startsWith('<?xml') ? escapeXmlAttr : /^[{[]/.test(aString) ? escapeJson : v=>v
    return aString.replace (/{i18n>([^}]+)}/g, (_, key) => {
      const val = ext[key] || bundle[key]
      return !val ? key : escape(val)
    })
  }
}



/**
 * Returns all property bundles, i.e. one for each available translation language,
 * for the given model.
 */
function bundles4 (model, locales = cds.env.i18n.languages) {

  const folders = folders4 (model); if (folders.length === 0) return
  const {i18n} = cds.env

  if (locales.split) locales = locales.split(',')
  if (locales[0] === '*' || locales[0] === 'all') {
    locales = allLocales4 (folders); if (!locales) return {}
    if (!locales.includes(i18n.fallback_bundle))  locales.push (i18n.fallback_bundle)
  }

  DEBUG?.('Languages:', locales)

  return (function*(){
    for (let each of locales) {
      let bundle = bundle4 (model, each); if(!bundle) continue
      DEBUG?.(bundle)
      yield [ each, bundle ]
    }
  })()
}


function files4 (folders_or_model) {
  const {i18n} = cds.env
  const folders = folders_or_model.map ? folders_or_model : folders4(folders_or_model)
  const files = folders.map (folder => readdirSync(folder)
    .filter (e => e.startsWith(i18n.file))
    .map(i18nFile => join (folder, i18nFile))
  ).flat()
  if (files.length === 0) {
    DEBUG?.('No languages for folders:', folders)
    return null
  }
  return files
}

/**
 * Return locales for all bundles found in given folders derived from .json, .properties or .csv files.
 *
 * TODO - .csv file handling seems to be questionable - do we need to check all .csv files additionally for locales ???
 */
function allLocales4 (folders_or_model) {
  const files = files4(folders_or_model)
  if (!files) return null
  if (files[0].endsWith('.csv')) {
    return cds.load.csv (files[0])[0].slice(1)
  } else {
    const locales = new Set()
    files.forEach(file => {
      const parsed = parse(file)
      if (parsed.ext === '.json') {
        Object.keys(require(file)).forEach(locale => locales.add(locale))
      }
      else if (parsed.ext === '.properties') {
        const match = /_(\w+)$/.exec(parsed.name)
        if (match)  locales.add(match[1])
      }
    })
    return Array.from(locales)
  }
}

/**
 * Returns the effective bundle stack for the given language and model folders.
 * Expected bundle stack for languages en and '' + 2 model layers:
    [en]   model/_i18n
      []   model/_i18n
        [en]   model/node_modules/reuse-model/_i18n
          []   model/node_modules/reuse-model/_i18n
 */
function bundle4 (model, locale) {

  if (typeof model === 'string') [ model, locale ] = [ cds.context?.model || cds.model, model ]

  const bundles = model.texts || Object.defineProperty (model,'texts',{value:{}}).texts
  if (locale in bundles) return bundles[locale]

  const folders = folders4(model)
  if (!folders.length) return bundles[locale] = {}

  const {i18n} = cds.env
  let bundle = Object.create(null)
  bundle.toJSON = jsonWithAllProps // allows JSON.stringify with all inherited props

  let locales = (
    locale === i18n.fallback_bundle ? [ i18n.fallback_bundle ] :
    locale === i18n.default_language ? [ i18n.fallback_bundle, i18n.default_language ] :
    [ i18n.fallback_bundle, i18n.default_language, locale ]
  )
  for (let each of locales) {
    const b = bundle = Object.create(bundle)
    for (let folder of folders) {
      const file = join (folder, i18n.file), suffix = each ? '_' + each : ''
      const next = bundle4[file + suffix] ??= (
        loadFromJSON (file, each)  ||
        cds.load.properties (file + suffix.replace('-','_')) ||  // e.g. en-UK --> en_UK
        cds.load.properties (file + suffix.match(/\w+/)) ||  // e.g. en_UK --> en
        loadFromCSV (file, each)
      )
      Object.assign (b, next)
    }
  }

  return bundles[locale] = bundle
}

/**
 * Returns an array of all existing _i18n folders for the models
 * that are merged into the given one..
 */
function folders4 (model) {
  if (model._i18nfolders)  return model._i18nfolders
  // Order of model.$sources is expected to be sorted along usage levels, e.g.
  //   foo/bar.cds
  //   foo/node_modules/reuse-level-1/model.cds
  //   foo/node_modules/reuse-level-2/model.cds
  const folders = [] // using an array here to not screw up the folder order
  const srcFolders = new Set ((model.$sources||[]).map(dirname))
  srcFolders.forEach(src => {
    const folder = folder4 (src)
    if (folder && !folders.includes(folder)) {
      folders.push(folder)
    }
  })
  Object.defineProperty (model, '_i18nfolders', {value:folders.reverse()})
  return folders
}

/**
 * Returns the location of an existing _i18n folder next to or in the
 * folder hierarchy above the given path, if any.
 */
function folder4 (loc) {
  // already cached from a former lookup?
  if (loc in folder4)  return folder4[loc]
  // check whether a <loc>/_i18n exists
  const {i18n} = cds.env
  for (let each of i18n.folders) {
    const f = join (loc, each)
    if (existsSync(f)) return folder4[loc] = f
  }
  //> no --> search up the folder hierarchy up to cds.root, cds.home, or some .../node_modules/<package>
  let next = dirname(loc)
  if (_node_modules.some(m => next.includes(m))) {
    if (_node_modules.some(m => next.endsWith(m))) return folder4[loc] = null
  } else {
    if (!(
      next.startsWith(cds.root) ||
      next.startsWith(cds.home) ||
      i18n.root && next.startsWith(i18n.root)
    )) return folder4[loc] = null
  }
  if (!next || next === loc) return folder4[loc] = null
  // console.debug(next)
  return folder4[loc] = folder4(next)
}


function loadFromJSON (res, lang=cds.env.i18n.default_language) {
  let cached = loadFromJSON[res]
  if (!cached) try {
    cached = loadFromJSON[res] = require (resolve (cds.root,res+'.json'))
  } catch(e) {
    if (e.code !== 'MODULE_NOT_FOUND') throw e
    else cached = loadFromJSON[res] = {}
  }
  return cached[lang] || cached[lang.match(/\w+/)?.[0]]
}

function loadFromCSV (res, lang=cds.env.i18n.default_language) {
  let csv = cds.load.csv(res+'.csv'); if (!csv) return
  let [header, ...rows] = csv
  if (lang === '*') return header.slice(1).reduce ((all,lang,i) => {
    all[lang] = _bundle(i); return all
  },{})
  let col = header.indexOf (lang)
  if (col < 0)  col = header.indexOf ((lang.match(/\w+/)||[])[0])
  if (col > 0) return _bundle (col)
  function _bundle (col) {
    const b={}; for (let row of rows) if (row[col])  b[row[0]] = row[col]
    return Object.defineProperty (b, '_source', {value:res+'.csv'+'#'+lang})
  }
}

// TODO use compiler API for XML escaping
function escapeXmlAttr (str) {
  // first regex: replace & if not followed by apos; or quot; or gt; or lt; or amp; or #
  // Do not always escape > as it is a marker for {i18n>...} translated string values
  let result = str;
  if (typeof str === 'string') {
    result = str.replace(/&(?!(?:apos|quot|[gl]t|amp);|#)/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/"/g, '&quot;')
      .replace(/\r\n|\n/g, '&#xa;');
    if (!result.startsWith('{i18n>')) result = result.replace(/>/g, '&gt;')
  }
  return result;
}

const escapeJson = str => str.replace(/"/g, '\\"')

function jsonWithAllProps() {
  const res = {}
  for (let key in this) {
    if (typeof this[key] !== 'function') {
      res[key] = this[key]
    }
  }
  return res
}
