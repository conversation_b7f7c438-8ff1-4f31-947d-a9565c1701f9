if (process.env.CDS_STRICT_NODE_VERSION !== 'false') require ('./utils/check-version')
!(global.__cds_loaded_from ??= new Set).add(__filename) // track from where we loaded cds

/** @typedef { import('../apis/linked.js').LinkedCSN } Linked */
/** @typedef { import('./srv/srv-api') } Service */

const { EventEmitter } = require('node:events')
const { extend, lazify } = require('./lazy')

const cds = module.exports = global.cds = new class cds extends EventEmitter {

  async emit (eve, ...args) {
    if (eve === 'served') for (let l of this.listeners(eve)) await l.call(this,...args)
    else return super.emit (eve, ...args)
  }

  /** CLI args */ cli = { command:'', options:{}, argv:[] }
  /** Working dir */ root = process.cwd()
  /** @type Linked */ model = undefined
  /** @type Service */ db = undefined

  // Configuration & Information
  get requires() { return super.requires = this.env.requires._resolved() }
  get plugins()  { return super.plugins = require('./plugins').activate() }
  get version()  { return super.version = require('../package.json').version }
  get env()      { return super.env = require('./env/cds-env').for('cds',this.root) }
  get home()     { return super.home = __dirname.slice(0,-4) }
  get schema()   { return super.schema = require('./env/schemas') } // REVISIT: Better move that to cds-dk?

  // Loading and Compiling Models
  get compiler() { return super.compiler = require('./compile/cdsc') }
  get compile()  { return super.compile = require('./compile/cds-compile') }
  get resolve()  { return super.resolve = require('./compile/resolve') }
  get load()     { return super.load = require('./compile/load') }
  get get()      { return super.get = this.load.parsed }
  get parse()    { return super.parse = require('./compile/parse') }
  get minify()   { return super.minify = require('./compile/minify') }
  get extend()   { return super.extend = require('./compile/extend') }
  get deploy()   { return super.deploy = require('./dbs/cds-deploy') }
  get localize() { return super.localize = require('./i18n/localize') }

  // Model Reflection, Builtin types and classes
  get entities()    { return this.db?.entities || this.model?.entities }
  get reflect()     { return super.reflect = this.linked }
  get linked()      { return super.linked = require('./linked/models') }
  get infer()       { return super.infer = require('./ql/infer') }
  get builtin()     { return super.builtin = require('./linked/types') }
  get Association() { return super.Association = this.builtin.classes.Association }
  get Composition() { return super.Composition = this.builtin.classes.Composition }
  get entity()      { return super.entity = this.builtin.classes.entity }
  get event()       { return super.event = this.builtin.classes.event }
  get type()        { return super.type = this.builtin.classes.type }
  get array()       { return super.array = this.builtin.classes.array }
  get struct()      { return super.struct = this.builtin.classes.struct }
  get service()     { return super.service = extend (this.builtin.classes.service) .with ({
    /** @param {( this:Service, srv:Service )} fn */ impl: fn => fn,
    /** @type Service[] */ providers: []
  })}

  // Providing and Consuming Services
  /** @type { Record<string,Service> } */ services = Object.defineProperties (
    new class { *[Symbol.iterator](){ for (let e in this) yield this[e] } },
    { _pending: {value:{}} }
  )
  get server() { return super.server = require('../server') }
  get serve() { return super.serve = require('./srv/cds-serve') }
  get connect() { return super.connect = require('./srv/cds-connect') }
  get outboxed() { return super.outboxed = require('../libx/outbox').outboxed }
  get unboxed() { return super.unboxed = require('../libx/outbox').unboxed }
  get middlewares() { return super.middlewares = require('./srv/middlewares') }
  get odata() { return super.odata = require('../libx/odata') }
  get auth() { return super.auth = require('./auth') }

  // Core Services API
  get Service() { return super.Service = require('./srv/srv-api') }
  get EventContext() { return super.EventContext = require('./req/context') }
  get Request() { return super.Request = require('./req/request') }
  get Event() { return super.Event = require('./req/event') }
  get User() { return super.User = require('./req/user') }

  // Services, Protocols and Periphery
  get ApplicationService() { return super.ApplicationService = require('../libx/_runtime/common/Service.js') }
  get MessagingService() { return super.MessagingService = require('../libx/_runtime/messaging/service.js') }
  get DatabaseService() { return super.DatabaseService = require('../libx/_runtime/db/Service.js') }
  get RemoteService() { return super.RemoteService = require('../libx/_runtime/remote/Service.js') }

  // Contexts and Transactions
  get _context() { return super._context = require('./req/cds-context') }
  get context()  { return this._context._for(this) }
  set context(_) { this._context._for(this,_) }
  get spawn()    { return super.spawn = this._context.spawn }

  // Helpers
  get utils() { return super.utils = require('./utils/cds-utils') }
  get error() { return super.error = require('./log/cds-error') }
  get exec() { return super.exec = require('../bin/cds-serve') }
  get test() { return super.test = require('./utils/cds-test') }
  get log() { return super.log = require('./log/cds-log') }
  get debug() { return super.debug = this.log.debug }
  get lazify() { return lazify }
  get lazified() { return lazify }
  clone(x) { return structuredClone(x) }
  exit(code){ return cds.shutdown ? cds.shutdown() : process.exit(code) }

  // Querying and Databases
  get txs()  { return super.txs = new this.Service('cds.tx') }
  get ql()   { return super.ql = require('./ql/cds-ql') }
  tx         (..._) { return (this.db || this.txs).tx(..._) }
  run        (..._) { return (this.db || typeof _[0] === 'function' && this.txs || this.error._no_primary_db).run(..._) }
  foreach    (..._) { return (this.db || this.error._no_primary_db).foreach(..._) }
  stream     (..._) { return (this.db || this.error._no_primary_db).stream(..._) }
  read       (..._) { return (this.db || this.error._no_primary_db).read(..._) }
  create     (..._) { return (this.db || this.error._no_primary_db).create(..._) }
  insert     (..._) { return (this.db || this.error._no_primary_db).insert(..._) }
  update     (..._) { return (this.db || this.error._no_primary_db).update(..._) }
  upsert     (..._) { return (this.db || this.error._no_primary_db).upsert(..._) }
  delete     (..._) { return (this.db || this.error._no_primary_db).delete(..._) }
  disconnect (..._) { return (this.db || this.error._no_primary_db).disconnect(..._) }

  // legacy and to be moved stuff -> hidden for tools in cds.__proto__
  /** @deprecated */ transaction (..._) { return (this.db||this.error._no_primary_db).transaction(..._) }
  /** @deprecated */ get build() { return super.build = this.error._outdated_dk }
  /** @deprecated */ get in() { return super.in = cwd => !cwd ? this : {__proto__:this, cwd, env: this.env.for('cds',cwd) } }
}

// add global cds.ql commands
extend (global) .with (class {
  static get SELECT() { return cds.ql.SELECT }
  static get INSERT() { return cds.ql.INSERT }
  static get UPSERT() { return cds.ql.UPSERT }
  static get UPDATE() { return cds.ql.UPDATE }
  static get DELETE() { return cds.ql.DELETE }
  static get CREATE() { return cds.ql.CREATE }
  static get DROP() { return cds.ql.DROP }
  static get CDL() { return cds.parse.CDL }
  static get CQL() { return cds.parse.CQL }
  static get CXL() { return cds.parse.CXL }
})

// install jest util if jest is defined
if (process.env.CDS_JEST_MEM_FIX && typeof jest !== 'undefined') require('./utils/jest.js')

// Allow for import cds from '@sap/cds' without esModuleInterop
// FIXME: remove this flag in the next release. Only serves as fallback switch if people report issues with value:cds
// Setting it to module.exports lead to issues with vitest while setting it to cds apparently works fine.
if (process.env.CDS_ESM_INTEROP_DEFAULT) {
  Object.defineProperties(module.exports, { default: {value:module.exports}, __esModule: {value:true} })
} else {
  Object.defineProperties(module.exports, { default: {value:cds}, __esModule: {value:true} })
}
