const { format } = require('util'), _formatted = v => format(v)


/**
 * This is the implementation of cds.error().
 * Usage variants:
 *
 *       cds.error `Message with formatted: ${{foo:'bar'}}`
 *       cds.error ({ message, code, ... })
 *       cds.error (message, { code, ... })
 *       let e = new cds.error(...) //> will not throw
 *
 * Calling `cds.error()` with `new` returns the newly created Error,
 * while calling it without `new` it throws immediately. The latter is
 * useful for usages like that:
 *
 *     let x = y || cds.error `Argument 'y' must not be null`
 */
const error = exports = module.exports = function cds_error ( message, details, caller ) {
  let e
  if (message.raw) [ message, details, caller ] = [ error.message(...arguments) ]
  if (typeof message === 'string') {
    e = new Error(message)
  } else {
    e = message.stack ? message : Object.assign(new Error,message)
    ;[ caller, details ] = [ details ]
  }
  Error.captureStackTrace (e,caller||error)
  if (details) Object.assign (e,details)
  if (new.target) return e; else throw e
}


/**
 * Constructs a message from a tagged template string. In contrast to usual
 * template strings embedded values are formatted using `util.format`
 * not just `toString()`.
 *
 *     let x = `A sample message with ${'a string'}, ${{an:'object'}}, and ${[1,2,3]}`
 *     let y = cds.error.message`with ${'a string'}, ${{an:'object'}}, and ${[1,2,3]}`
 *     //> x = A sample message with a string and [object Object], and 1,2,3
 *     //> y = with a string, { an: 'object' }, and [ 1, 2, 3 ]
 */
exports.message = (strings,...values) => String.raw(strings,...values.map(_formatted))


/**
 * Use that to construct and throw errors from a tagged template string
 * in validations of function arguments.
 * Use it like that:
 *
 *     let x = {foo:'bar'}
 *     typeof x === 'string' || cds.error.expected `${{x}} to be a string`
 *     //> Error: Expected argument 'x' to be a string, but got: { foo: 'bar' }
 */
const expected = exports.expected = ([,type], arg) => {
  const [ name, value ] = Object.entries(arg)[0]
  return error (`Expected argument '${name}'${type}, but got: ${require('util').inspect(value,{depth:11})}`, undefined, expected)
}


//
// Private helpers ...
//

exports._duplicate_cds = (...locations) => {
  const { local } = require('../utils/cds-utils')
  throw error `Duplicate @sap/cds/common!

  There are duplicate versions of @sap/cds loaded from these locations:

    ${locations.map(local).join('\n    ')}

  To fix this, check all dependencies to "@sap/cds" in your package.json and
  those of reused packages and ensure they allow deduped use of @sap/cds.
  `
}

exports._no_primary_db = new Proxy ({},{ get: function fn(_,p) { error (`Not connected to primary datasource!

  Attempt to use 'cds.${p}' without prior connect to primary datasource,
  i.e. cds.connect.to('db').
  ${ process.argv[1].endsWith('cds') && process.argv[2] in {run:1,serve:1} ? `
  Please configure one thru 'cds.requires.db' or use in-memory db:
  cds ${process.argv[2]} --in-memory` : ''}`

,{},fn) }})


exports._outdated_dk = () => error `
  This application uses @sap/cds version >= 7, which is not compatible with the installed @sap/cds-dk version 6.
  Either update @sap/cds-dk to version 7 or downgrade @sap/cds to version 6 instead.
`
