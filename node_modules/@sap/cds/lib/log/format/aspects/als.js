const cds = require('../../..')

const $remove = Symbol('remove')

const _is_custom_fields = (arg, custom_fields) => {
  if (!Object.keys(arg).length) return false
  for (const k in arg) if (!(k in custom_fields)) return false
  return true
}

const _is_categories = arg => arg.categories && Array.isArray(arg.categories) && Object.keys(arg).length === 1

function als_aspect(module, level, args, toLog) {
  // REVISIT: kibana_custom_fields for backward compatibility. remove in cds^8.
  const { als_custom_fields, kibana_custom_fields } = cds.env.log
  this._CUSTOM_FIELDS ??= kibana_custom_fields ? { ...kibana_custom_fields } : { ...als_custom_fields }
  this._HAS_CUSTOM_FIELDS ??= Object.keys(this._CUSTOM_FIELDS).length > 0

  // extract custom fields and categories from remaining args (while avoiding as many loops/ iterations as possible)
  if (args.length) {
    let filter4removed = false
    for (let i = 0; i < args.length; i++) {
      const arg = args[i]
      if (typeof arg !== 'object') continue
      if ((this._HAS_CUSTOM_FIELDS && _is_custom_fields(arg, this._CUSTOM_FIELDS)) || _is_categories(arg)) {
        Object.assign(toLog, arg)
        args[i] = $remove
        filter4removed = true
      }
    }
    if (filter4removed) args.sort((a, b) => (b === $remove) * -1).splice(args.lastIndexOf($remove))
  }

  // ALS custom fields
  if (this._HAS_CUSTOM_FIELDS) {
    const cf = []
    for (const k in this._CUSTOM_FIELDS) if (toLog[k]) cf.push({ k, v: toLog[k], i: this._CUSTOM_FIELDS[k] })
    if (cf.length) toLog['#cf'] = { string: cf }
  }
}

module.exports = process.env.VCAP_SERVICES?.match(/"label":\s*"application-logs"/) ? als_aspect : () => {}
