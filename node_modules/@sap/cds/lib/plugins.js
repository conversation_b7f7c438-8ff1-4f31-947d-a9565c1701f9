
const cds = require('.')

exports.require = require

/**
 * Fetch cds-plugins from project's package dependencies.
 * Used in and made available through cds.env.plugins.
 */
exports.fetch = function (DEV = process.env.NODE_ENV !== 'production') {
  const plugins = {}
  fetch_plugins_in (cds.home, false)
  fetch_plugins_in (cds.root, DEV)
  function fetch_plugins_in (root, dev) {
    let pkg; try { pkg = exports.require(root + '/package.json') } catch { return }
    let deps = { ...pkg.dependencies, ...dev && pkg.devDependencies }
    for (let each in deps) try {
      let impl = exports.require.resolve(each + '/cds-plugin', { paths: [root] })
      const packageJson = exports.require.resolve(each + '/package.json', { paths: [root] })
      plugins[each] = { impl, packageJson }
    } catch { /* no cds-plugin.js */ }
  }
  return plugins
}

/**
 * Load and activate cds-plugins from project's package dependencies.
 * Used in and made available through cds.plugins.
 */
exports.activate = async function () {
  const DEBUG = cds.debug ('plugins', {label:'cds'})
  DEBUG && console.time ('[cds] - loaded plugins in')
  const { plugins } = cds.env, { local } = cds.utils
  await Promise.all (Object.entries(plugins) .map (async ([ plugin, conf ]) => {
    DEBUG?.(`loading plugin ${plugin}:`, { impl: local(conf.impl) })
    // TODO: support ESM plugins. But see cap/cds/pull/1838#issuecomment-1177200 !
    const p = require (conf.impl)
    if(p.activate) {
      cds.log('plugins').warn(`WARNING: \n
  The @sap/cds plugin ${conf.impl} contains an 'activate' function, which is deprecated and won't be
  supported in future releases. Please rewrite the plugin to return a Promise within 'module.exports'.
  `)
      await p.activate(conf)
    }
    return p
  }))
  DEBUG && console.timeEnd ('[cds] - loaded plugins in')
  return plugins
}
