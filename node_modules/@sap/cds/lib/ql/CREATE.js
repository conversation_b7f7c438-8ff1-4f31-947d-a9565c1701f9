module.exports = class Query extends require('./Query') {

  static _api() {
    return Object.assign((..._) => (new this).entity(..._), {
      entity: (..._) => (new this).entity(..._),
    })
  }

  entity (e, elements) {
    if (elements)
      this.CREATE.entity = { elements: elements, kind: 'entity', name:e }
    else
      this.CREATE.entity = e && e.elements ? e : this._target4(e)
    return this
  }

  as (query) {
    if (!query || !query.SELECT) this._expected `${{query}} to be a CQN {SELECT} object`
    this.CREATE.as = query
    return this
  }

  get _target_ref(){ return this.CREATE.entity }
}
