const Whereable = require('./Whereable')

module.exports = class Query extends Whereable {

  static _api() {
    return Object.assign ((..._) => (new this).from(..._), {
      from: (..._) => (new this).from(..._),
    })
  }

  from(entity, key) {
    this.DELETE.from = this._target4 (...arguments) // supporting tts
    if (key !== undefined) this.byKey(key)
    return this
  }

  valueOf() {
    return super.valueOf('DELETE FROM')
  }

  get _target_ref(){ return this.DELETE.from }
}
