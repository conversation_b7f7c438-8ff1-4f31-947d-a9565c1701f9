module.exports = class Query extends require('./Query') {

  static _api() {
    return Object.assign ((..._) => (new this).entries(..._), {
      into: (..._) => (new this).into(..._),
    })
  }

  into (entity, ...data) {
    this[this.cmd].into = this._target4 (...arguments) // supporting tts
    if (data.length) this.entries(...data)
    return this
  }

  entries (...x) {
    if (!x.length) return this
    if (x[0].SELECT) return this.from(x[0])
    this[this.cmd].entries = is_array(x[0]) ? x[0] : x
    return this
  }
  columns (...x) {
    this[this.cmd].columns = is_array(x[0]) ? x[0] : x
    return this
  }
  values (...x) {
    this[this.cmd].values = is_array(x[0]) ? x[0] : x
    return this
  }
  rows (...rows) {
    if (is_array(rows[0]) && is_array(rows[0][0])) rows = rows[0]
    if (!is_array(rows[0])) this._expected `Arguments ${{rows}} to be an array of arrays`
    this[this.cmd].rows = rows
    return this
  }

  from (query) {
    if (!query) return this
    if (!query.SELECT) {
      if (query.name || typeof query === 'string') query = SELECT.from(query)
      else this._expected `${{query}} to be a CQN {SELECT} query object`
    }
    this[this.cmd].as = query // REVISIT: should we also change CSN spec, and adopt db service impls?
    return this
  }

  /** @deprecated */ as (query) {
    INSERT_as_SELECT ??= require('../utils').deprecated(()=>{},{old:'INSERT.as(SELECT)', use:'INSERT.entries(SELECT)'})
    INSERT_as_SELECT()
    return this.from(query)
  }

  valueOf() {
    return super.valueOf('INSERT INTO')
  }

  get _target_ref(){ return this.INSERT.into }
}

const is_array = Array.isArray
let INSERT_as_SELECT