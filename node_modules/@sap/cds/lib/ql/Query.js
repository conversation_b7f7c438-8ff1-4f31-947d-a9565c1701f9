const { AsyncResource } = require('async_hooks')
const cds = require('../index')

class Query {

  constructor(_={}) { this[this.cmd] = _ }

  /**
   * Note to self: We can't use .as (alias) as that would conflict with
   * sub selects in columns, which in turn may has aliases in .as.
   */
  alias (a) {
    this._target_ref.as = a
    return this
  }

  /** Creates a derived instance that initially inherits all properties. */
  clone (_) {
    const cmd = this.cmd || Object.keys(this)[0]
    return {__proto__:this, [cmd]: {__proto__:this[cmd],..._} }
  }

  flat (q=this) {
    let x = q.cmd || Object.keys(q)[0], y = q[x]
    let protos = [y]; for (let o=y; o.__proto__;) protos.push (o = o.__proto__)
    q[x] = Object.assign ({}, ...protos.reverse())
    if (y.columns) for (let c of y.columns) if (c.SELECT) (this||Query.prototype).flat(c)
    return q
  }

  /** Binds this query to be executed with the given service */
  bind (srv) {
    return Object.defineProperty (this,'_srv',{ value:srv, configurable:true, writable:true })
  }

  /** Turns all queries into Thenables which execute with primary db by default */
  get then() {
    const srv = this._srv || cds.db || cds.error `Can't execute query as no primary database is connected.`
    const q = new AsyncResource('await cds.query')
    // Temporary solution for cds.stream in .then. Remove with the next major release.
    return (r,e) => q.runInAsyncScope (srv.run, srv, this) .then(rt => { rt = this._stream && rt ? Object.values(rt)[0] : rt; return r(rt) }, e)
  }

  _target4 (...args) {
    return this._target_ref4 (...args)
  }

  _target_ref4 (target, arg2) {

    // If called with a linked entity -> use it as this.target
    if (target instanceof cds.entity) this.target = target

    // REVISIT: this._target is not reliable !!!
    this._set ('_target', function _target(t) { return t && (
      typeof t === 'string' ? { name: t } :
      t.ref ? { name: t.ref[0] } :
      t.raw ? _target(arg2) :
      t.SELECT ? _target(t.SELECT.from) :
      t //> default is assumed to be a csn definition or a look-alike or a SELECT
    )}(target))

    // Determine from.ref or from.SELECT
    const from = target && (
      target.name   ? {ref:[target.name]} :
      typeof target === 'string' ? cds.parse.path(target) :
      target.raw    ? cds.parse.path(...arguments) :
      target.ref    ? target :
      target.SELECT ? target :
      target.SET    ? target : 0
    )
    return from || this._expected `${{target}} to be an entity path string, a CSN definition, a {ref}, a {SELECT}, or a {SET}`
  }

  _expected (...args) {
    return cds.error.expected (...args)
  }

  _add (property, values) {
    const _ = this[this.cmd], pd = Reflect.getOwnPropertyDescriptor (_,property)
    _[property] = !pd || !pd.value ? values : [ ...pd.value, ...values ]
    return this
  }

  _set (property, value) {
    Reflect.defineProperty (this, property, { value, configurable:true, writable:true })
    return value
  }

  valueOf (cmd=this.cmd) {
    return `${cmd} ${_name(this._target.name)} `
  }

  get _target_ref() {
    throw cds.error `Query subclass ${this.constructor.name} must implement '_target_ref'`
  }

  /**
   * Returns the inferred query's source, which is the entity referred
   * to in SELECT.from, INSERT.into, UPDATE.entity, or DELETE.from,
   * or a sub query specified in SELECT.from, INSERT.into,
   */
  get source() {
    const m = this._srv?.model || cds.context?.model || cds.model
    return cds.infer (this, m?.definitions, 'get source')
  }
  set source(t) { this._set('source',t) }

  /**
   * Returns the inferred query's target, which is the entity referred
   * to in SELECT.from, INSERT.into, UPDATE.entity, or DELETE.from.
   * In case of a sub query specified in SELECT.from, INSERT.into,
   * returns the target of the sub query, recursively.
   */
  get target() {
    const m = this._srv?.model || cds.context?.model || cds.model
    return cds.infer (this, m?.definitions)
  }
  set target(t) { this._set('target',t) }
}

const _name = cds.env.sql.names === 'quoted' ? n =>`"${n}"` : n => n.replace(/[.:]/g,'_')

if (cds.env.ql.quirks_mode) Object.defineProperty (Query.prototype, '_target4', {
  value: function (...args) {
    const { ref, as } = this._target_ref4 (...args)
    return ref.length === 1 && typeof ref[0] === 'string' && !as ? ref[0] : as ? {ref, as} : {ref}
  }
})

module.exports = Query
