const { error } = require ('../index')
const cds = require('../index')
const parse = require('./parse')

class Query extends require('./Query') {

  where(...x) { return this._where (x,'and','where') }
  and(...x) {  return this._where (x,'and') }
  or(...x) { return this._where (x,'or') }
  _where (args, and_or, _where) {
    if (!args[0]) return this
    let pred = predicate4(args, _where)
    if (pred && pred.length > 0) {
      let _ = this[this.cmd]
      const clause = _where ?? (
        _.having ? 'having' :
        _.where ? 'where' :
        _.from?.on ? 'on' :
        error (`Invalid attempt to call '${this.cmd}.${and_or}()' before a prior call to '${this.cmd}.where()'`)
      )
      if (clause === 'on') _ = _.from
      let left = _[clause]
      if (!left) { //> .where() called first time
        // SELECT.from `X` .where `x or y` .and `z` -> SELECT from X where (x or y) and z
        if (pred.includes('or')) this._left_has_or = true
        _[clause] = pred
      } else { //> .where(), .and(), .or() called successively
        if (_where) {
          // SELECT.from `X` .where `x` .or `y` .where `z` -> SELECT from X where (x or y) and z
          if (left.includes('or')) left = [{xpr:left}]
        } else if (and_or === 'and') {
          // SELECT.from `X` .where `x` .or `y` .and `z` -> SELECT from X where x or y and z
          if (this._left_has_or) { left = [{xpr:left}]; delete this._left_has_or }
        }
        // SELECT.from `X` .where `x` .and `y or z` -> SELECT from X where x and (y or z)
        if (pred.includes('or')) pred = [{xpr:pred}]
        _[clause] = [ ...left, and_or, ...pred ]
      }
    }
    return this
  }

  byKey(key) {
    if (typeof key !== 'object' || key === null) key = { [Object.keys(this._target.keys||{ID:1})[0]]: key }
    if (this.SELECT) this.SELECT.one = true
    if (cds.env.features.keys_into_where) return this.where(key)
    if (this.UPDATE) { this.UPDATE.entity = { ref: [{ id: cds.env.ql.quirks_mode ? this.UPDATE.entity : this.UPDATE.entity.ref.at(-1), where: predicate4([key]) }] }; return this }
    if (this.SELECT) { this.SELECT.from.ref[this.SELECT.from.ref.length-1] = { id: this.SELECT.from.ref.at(-1), where: predicate4([key]) }; return this }
    if (this.DELETE) { this.DELETE.from = { ref: [{ id: cds.env.ql.quirks_mode ? this.DELETE.from : this.DELETE.from.ref.at(-1), where: predicate4([key]) }] }; return this }
    return this.where(key)
  }
}

const predicate4 = (args, _clause) => {
  if (args.length === 0) return; /* else */ const x = args[0]
  if (x.raw) {
    let cxn = parse.CXL(...args)
    return cxn.xpr ?? [cxn] //> the fallback is for single-item exprs like `1` or `ref`
  }
  if (args.length === 1 && typeof x === 'object') {
    if (is_array(x)) return x
    if (is_cqn(x)) return args
    else return _object_predicate(args,_clause)
  }
  else return _fluid_predicate(args)
}

const _object_predicate = ([arg], _clause) => { // e.g. .where ({ID:4711, stock: {'>=':1})
  const pred = []
  for (const k in arg) {
    const x = arg[k]
    if (k === 'and') {
      if (x.or) pred.push('and', {xpr:predicate4([x],_clause)})
      else pred.push('and', ...predicate4([x],_clause))
      continue
    }
    if (k === 'or') {
      pred.push('or', ...predicate4([x],_clause))
      continue
    }
    if (k === 'not') {
      pred.push('not', {xpr:predicate4([x],_clause)})
      continue
    }
    if (k === 'exists') {
      pred.push('and', 'exists', typeof x === 'object' ? x : { ref: x.split('.') })
      continue
    }
    if (k === 'not exists') {
      pred.push('and', 'not', 'exists', typeof x === 'object' ? x : { ref: x.split('.') })
      continue
    }
    else pred.push('and', parse.expr(k))
    if (!x || x==='*') pred.push('=', {val:x})
    else if (x.SELECT || x.list) pred.push('in', x)
    else if (is_array(x)) pred.push('in', {list:x.map(val)})
    else if (is_cqn(x)) pred.push('=', x)
    else if (x instanceof Buffer) pred.push('=', {val:x})
    else if (x instanceof RegExp) pred.push('like', {val:x})
    else if (typeof x === 'object') for (let op in x) pred.push(op, val(x[op]))
    else if (_clause === 'on' && typeof x === 'string') pred.push('=', { ref: x.split('.') })
    else pred.push('=', {val:x})
  }
  return pred[0] === 'and' ? pred.slice(1) : pred
}

const _fluid_predicate = (args) => { // e.g. .where ('ID=',4711, 'and stock >=',1)
  if (args.length === 3 && args[1] in operators) return [ ref(args[0]), args[1], val(args[2]) ] // REVISIT: Legacy!
  if (args.length % 2 === 0) args.push('')
  const expr = args.filter((_, i) => i % 2 === 0).join(' ? ')
  const vals = args.filter((_, i) => i % 2 === 1)
  const {xpr} = parse.expr(expr)
  ;(function _fill_in_vals_into (xpr) { xpr.forEach ((x,i) => {
    if (x.xpr) _fill_in_vals_into (x.xpr)
    if (x.param) xpr[i] = val(vals.shift())
  })})(xpr)
  return xpr
}

const ref = x => is_cqn(x) ? x : {ref:x.split('.')}
const val = x => !x ? {val:x} : is_array(x) ? {list:x.map(val)} : is_cqn(x) ? x : {val:x}
const is_cqn = x => x.val !== undefined || x.xpr || x.ref || x.list || x.func || x.SELECT
const is_array = Array.isArray
const operators = { '=':1, '<':2, '<=':2, '>':2, '>=':2, '!=':3, '<>':3, in:4, like:4, IN:4, LIKE:4 }

module.exports = Object.assign (Query, { predicate4, parse })
