const Query = require('./Query')
require = path => { // eslint-disable-line no-global-assign
  const clazz = module.require (path); if (!clazz._api) return clazz
  const kind = path.match(/\w+$/)[0]
  Object.defineProperties (clazz.prototype, {
    kind: { value: kind },
    cmd: { value: kind }
  })
  const api = clazz._api()
  return Object.assign (function (...args) {
    if (new.target) return new clazz (...args) // allows: new SELECT
    return api (...args)                      // allows: SELECT(...).from()
  }, { class: clazz }, api)
}

module.exports = exports = {
  Query,  
  SELECT: require('./SELECT'),
  INSERT: require('./INSERT'),
  UPSERT: require('./UPSERT'),
  UPDATE: require('./UPDATE'),
  DELETE: require('./DELETE'),
  CREATE: require('./CREATE'),
  DROP:   require('./DROP'),
}

exports.clone = function (q,_) {
  // q = this.query(q)
  return Query.prototype.clone.call(q,_)
}

exports.query = function (q) {
  if (q instanceof Query) return q
  let kind = Object.keys(q)[0]
  let clazz = exports[kind]
  return !clazz ? q : new clazz (q[kind])
},

exports._reset = ()=>{ // for strange tests only
  const cds = require('../index')
  const _name = cds.env.sql.names === 'quoted' ? n =>`"${n}"` : n => n.replace(/[.:]/g,'_')
  Object.defineProperty (Query.prototype,'valueOf',{ configurable:1, value: function(cmd=this.cmd) {
    return `${cmd} ${_name(this._target.name)} `
  }})
  return this
}
