const cds = require('../index')

/**
 * Infers the target entity of a query
 */
function infer (q, defs, _get_source) {
  if (q._target instanceof cds.entity) return q._target
  let from = q._target_ref
    || q.SELECT?.from
    || q.INSERT?.into
    || q.UPSERT?.into
    || q.UPDATE?.entity
    || q.DELETE?.from
  let source = infer_from (from, defs)
  let target = source?.SELECT ? infer(source, defs) : source

  Object.defineProperties (q, {
    _target: { value: target, configurable:true, writable:true },
    target: { value: target, configurable:true, writable:true },
    source: { value: source, configurable:true, writable:true },
  })
  return _get_source ? source : target

  function infer_from (from, defs={}) {
    if (!from) return undefined
    if (from.ref) return infer_ref (from.ref, defs) || _unresolved(from.ref[0])
    if (from.SELECT) return from
    if (from.SET || from.args) return undefined //> UNIONs and JOINs are not supported
    return defs[from] || _unresolved(from) //> from is a string in quirks mode
  }

  function infer_ref (ref, defs) {
    let target = {elements:defs}
    for (let r of ref) {
      const e = target.elements?.[r.id||r]; if (!e) return
      target = (
        e._target ||       //> for already linked associations
        defs[e.target] ||  //> for not yet linked associations
        e                  //> for structs
      )
    }
    return target
  }

  function _unresolved (x) {
    return { name: x.id || x, __proto__: cds.entity.prototype, _unresolved:true }
  }
}


/**
 * Infers the elements according to a query's columns.
 * @param {Array} columns - the query's columns, or a nested .expand or .inline columns
 * @param {Object} source - the query's source entity or sub select
 */
function elements4 (columns, source) {

  // SELECT from Books; SELECT * from Books
  if (!columns || columns.length === 1 && columns[0] === '*') return source?.elements

  const elements = {}; columns.forEach (c => {

    // 1) SELECT *, ... from Books
    if (c === '*') {
      return Object.assign (elements, source.elements)
    }

    const ref = c.ref?.map(r => r.id??r)
    const as = c.as || ref?.join('_') || c.func || c.val || cds.error `Alias required for column expressions ${c}`
    let d = source, is2many

    // 2) SELECT ... : String from Books
    if (c.cast) {
      return elements[as] = builtin [c.cast.type]
    }

    // 3) SELECT title, author.name from Books
    if (c.ref && d?.elements) {
      for (let r of ref) d = (d.SELECT ? d : d._target||d).elements?.[r]
        || cds.error `Couldn't resolve element "${ref.join('/')}" in ${source.kind} ${source.name||''} ${Object.keys(source.elements)}`
      if (d._target) { is2many = d.is2many; c.expand || c.inline ? d = d._target : d }
      // ... d is further processed in steps 5,6,7 below
    }

    // 4) SELECT 1, 2+3, count(*) from Books; SELECT type, name from sqlite.schema
    else if (!c.expand) {
      return elements[as] = _typeof(c) // { ..._typeof(c), name: as }
    }

    // 5) SELECT author.books { title } from Books
    if (c.expand) {
      if (d.items) { d = d.items; is2many = true }
      d = new cds.struct ({ elements: elements4 (c.expand, d) }) //> { a, b, c } as x
      return elements[as] = is2many ? new cds.array ({ items: d }) : d
    }

    // 6) SELECT author.books.{ title } from Books
    if (c.inline) {
      const nested = elements4 (c.inline, d)
      for (let n in nested) elements[as+'_'+n] = nested[n]
    }

    // 7) SELECT title, author.name from Books
    else return elements[as] = d // NOTE: the else is neccessary after step 5 above
  })
  return elements

  function _typeof (c) {
    if (c.val !== undefined) return builtin [typeof c.val] || builtin [ Number.isInteger(c.val) ? 'Integer' : 'Decimal' ]
    if (c.func === 'count') return builtin.Integer
    if (c.xpr?.length === 1) return _typeof(c.xpr[0])
    return unknown
  }
}

const unknown = Object.freeze (new cds.type ({ _unresolved:true }))
const builtin = function _init (){
  const bi={}, bt = cds.builtin.types
  for (let t of Object.keys(bt)) bi[t] = bi[t.slice(4)] = { type:t, __proto__: bt[t] }
  bi.boolean = bi['cds.Boolean']
  bi.string = bi['cds.String']
  return bi
}()

module.exports = exports = Object.assign (infer, {
  elements4, unknown
})
