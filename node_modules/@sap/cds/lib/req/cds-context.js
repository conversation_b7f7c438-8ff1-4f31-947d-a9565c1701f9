const { AsyncLocalStorage } = require ('async_hooks')
const { EventEmitter } = require('events')
const EventContext = require('./context')

module.exports = new class extends AsyncLocalStorage {

  run(v,fn,...args) { return super.run (this._context4(v),fn,...args) }
  enterWith(v) { return super.enterWith (this._context4(v)) }

  _context4(v) {
    if (v instanceof EventContext || typeof v !== 'object') return v
    if (v.context) return v.context
    return EventContext.for(v)
  }

  /** @returns {EventContext} */
  _for (cds,v) {
    Reflect.defineProperty (cds,'context', { enumerable:1, ... {
      set:(v) => this.enterWith(v),
      get:()=> this.getStore(),
    }})
    return cds.context = v // IMPORTANT: we need to set it initially, to get it all wired up correctly
  }

  spawn (o,fn, /** @type {import('../index')} cds */ cds=this) {
    if (typeof o === 'function') [fn,o] = [o,fn] //> for compatibility
    if (o instanceof EventContext) throw cds.error `The passed options must not be an instance of cds.EventContext.`
    const fx = ()=>{
      const tx = cds.tx({...o}) // create a new detached transaction for each run of the background job
      return cds._context.run (tx, async ()=> {
        // REVISIT: The model must be set _after_ run to make sure that cds.context.tenant is correctly set.
        //          Otherwise, `model4` could query the wrong database to check for extensions.
        if (cds.model && (cds.env.requires.extensibility || cds.env.requires.toggles)) {
          const ctx = cds.context
          const ExtendedModels = require('../srv/srv-models') // the sentinel is automatically started when required
          cds.context.model = await ExtendedModels.model4(ctx.tenant, ctx.features)
          tx.model = cds.context.model
        }
        return Promise.resolve(fn(tx))
        .then (tx.commit, e => tx.rollback(_error(e, cds)))
        .then (res => Promise.all(em.listeners('succeeded').map(each => each(res))))
        .catch (err => Promise.all(em.listeners('failed').map(each => each(err))))
        .finally (() => Promise.all(em.listeners('done').map(each => each())))
      })
    }
    const em = new EventEmitter
    const { every, after } = o || {}
    if (every) {
      em.timer = setInterval(fx, every)
      cds.on('shutdown', () => clearInterval(em.timer))
    } else {
      em.timer = (after ? setTimeout(fx, after) : setImmediate(fx)).unref()
    }
    return em
  }
}

const _error = (err, cds) => { cds.log().error(`ERROR occurred in background job:`, err); return err }
