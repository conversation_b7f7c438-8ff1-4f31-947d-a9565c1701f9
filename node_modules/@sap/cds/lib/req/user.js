class User {

  constructor (_) {
    if (_ === undefined) {
      if (new.target === Privileged) return
      if (new.target === Anonymous) return
      else return new User.default
    }
    else if (typeof _ === 'string') this.id = _
    else Object.assign(this,_)
  }

  get attr() { return super.attr = {} }
  set attr(a) { super.attr = a }

  get roles() { return super.roles = {} }
  set roles(r) {
    super.roles = Array.isArray(r) ? r.reduce((p, n) => { p[n]=1; return p }, {}) : r
  }

  is (role) {
    return (
      role === 'authenticated-user' ||
      role === 'identified-user' ||
      role === 'any' ||
      !!this.roles[role]
    )
  }
  valueOf() { return this.id }

  // compatibility
  get _roles(){ return this.roles }
  set _roles(r){ this.roles = r }
}

/**
 * Subclass representing non-identified unauthenticated users.
 */
class Anonymous extends User { is(role) { return role === 'any' }}
Object.defineProperties (Anonymous.prototype, {
  id: {value:'anonymous',writable:true},
  roles: {value:{}},
  attr: {value:{}},
  _is_anonymous: {value:true},
})

/**
 * Subclass for executing code with superuser privileges.
 */
class Privileged extends User { is(){ return true }}
Object.defineProperties (Privileged.prototype, {
  id: {value:'privileged',writable:true},
  roles: {value:{},writable:true},
  attr: {value:{},writable:true},
  _is_privileged: {value:true},
})


// exports -----------------
module.exports = exports = Object.assign (User, {
  Anonymous,  anonymous  : Object.seal (new Anonymous),
  Privileged, privileged : Object.seal (new Privileged),
  default: Anonymous,
})
