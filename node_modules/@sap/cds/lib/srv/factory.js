const cds = require('..'), { path, isfile, _redacted } = cds.utils
const paths = Array.from (new Set ([ cds.root, ...require.resolve.paths('x') ]))
const DEBUG = cds.debug('cds.service.factory',); DEBUG && DEBUG ({ 'cds.root':cds.root, paths })

/** @typedef {import('./srv-api')} Service @type { (()=>Service) & (new()=>Service) } */
const ServiceFactory = function (name, model, options) { //NOSONAR

  const o = { ...options } // avoid changing shared options
  const conf = cds.requires[name]
  const serve = !(conf && conf.external && (!o.mocked || conf.credentials))
  const defs = !model ? {[name]:{}} : model.definitions || cds.error `Invalid argument for 'model': ${model}`
  const def = !name || name === 'db' ? {} : defs[name] || {}
  DEBUG?. ({ name, definition:def, options:_redacted(o) })

  let it /* eslint-disable no-cond-assign */
  if (it = o.with)                 return _use (it) // from cds.serve (<options>)
  if (it = serve && def['@impl'])  return _use (it) // from service definition
  if (it = serve && sibling(def))  return _use (it) // next to <service>.cds
  if (it = o.impl)                 return _use (it) // from cds.connect (<options>)
  return _use (_required())

  async function _use (it) {
    if (it._is_service_class)     return new it (name,model,o)
    if (it._is_service_instance)  return it
    if (typeof it === 'function') return _use (await _required(), /*with:*/ o.impl = _function(it)) // NOSONAR
    if (typeof it === 'object')   return _use (it[name] || it.default || await _required())
    if (typeof it === 'string')   return Object.assign (await _use (await _require(it,def)), {_source:it})
    throw cds.error `Invalid service implementation for ${name}: ${it}`
  }

  function _required() {
    const kind = o.kind = serve && def['@kind'] || o.kind || 'app-service'
    if (_require[kind]) return _require[kind]
    const {impl} = cds.requires[kind] || cds.requires.kinds[kind] || cds.error `No configuration found for 'cds.requires.${kind}'`
    DEBUG && DEBUG ('requires',{kind,impl})
    return _require[kind] = _require (impl || cds.error `No 'impl' configured for 'cds.requires.${kind}'`)
  }
}

const _require = (it,d) => {
  DEBUG && d && DEBUG ('requires',{ service: d.name, source:_source(d), impl:it })
  if (it.startsWith('@sap/cds/')) it = cds.home + it.slice(8)  //> for local tests in @sap/cds dev
  if (it.startsWith('./')) it = _relative (d,it.slice(2))     //> relative to <service>.cds
  try { var resolved = require.resolve(it,{paths}) } catch(e) {
    try { resolved = require.resolve(path.join(cds.root,it)) } catch(e) { // compatibility
      throw cds.error `Failed loading service implementation from '${it}' ${{ Reason:e, paths, 'cds.root':cds.root }}`
    }
  }
  DEBUG && DEBUG({resolved})
  return cds.utils._import(resolved)
}

const _function = (impl) => !_is_class(impl) ? impl : (srv) => {
  const instance = new impl, skip = {constructor:1,prototype:1}
  for (let each of Reflect.ownKeys (impl.prototype)) {
    each in skip || srv.on (each, (...args) => instance[each](...args))
  }
}

const sibling = (d) => {
  const { dir, name } = path.parse(_source(d)), TS = process.env.CDS_TYPESCRIPT
  for (let subdir of ['', './lib', './handlers']) {
    let found = TS && _resolve(dir,subdir,name+'.ts') || _resolve(dir,subdir,name+'.js') || _resolve(dir,subdir,name+'.mjs')
    if (found)  return found //> equiv to '.'+found.slice(home.length)
  }
}

// Note: @source has precedence over $location for csn.json cases
const _source = (d) => d['@source'] || (d['@source'] = d.$location && d.$location.file.replace(/\\/g, '/') || '.')
const _relative = (d,x,cwd=cds.root) => typeof x !== 'string' ? x : path.resolve (cwd, _source(d),'..',x)
const _resolve = (...args) => {
  const f = path.join(...args)
  try { return require.resolve(f) }
  catch(e) { if (e.code === 'MODULE_NOT_FOUND') return isfile(path.resolve(cds.root,f)); else throw e }
}
const _is_class = (impl) => typeof impl === 'function' && impl.prototype && /^class\b/.test(impl)

module.exports = Object.assign (ServiceFactory, { Service: ServiceFactory, resolve:_relative })
