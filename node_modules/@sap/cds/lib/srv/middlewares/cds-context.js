module.exports = ()=> {

  const cds = require ('../../index')

  /** @type { import('express').Handler } */
  async function cds_context_provider (req, res, next) {
    const ctx = {}
    ctx.http = { req, res }
    ctx.id = _id4(req)
    cds._context.run (ctx, next)
  }

  const { uuid } = cds.utils
  const _id4 = (req) => {
    let id = req.headers['x-correlation-id'] = (
      req.headers['x-correlation-id'] ||
      req.headers['x-correlationid'] ||
      req.headers['x-request-id'] ||
      req.headers['x-vcap-request-id'] ||
      uuid()
    )
    req.res.set('x-correlation-id', id)
    return id
  }

  return cds_context_provider
}
