const cds = require('../../index')
cds.env.features.serve_on_root = true

const protocols = require('.'), { serve } = protocols
protocols['odata-v4'] = { ...protocols['odata-v4'], get impl() { return libx.to.odata_v4 } }
protocols['odata'] = { ...protocols['odata'], get impl() { return libx.to.odata_v4 } }
protocols['rest'] = { ...protocols['rest'], get impl() { return libx.to.rest } }

const cds_context_model = require('../srv-models')
const cds_context = require('../middlewares/cds-context')()
const ctx_auth = require('../../auth')._ctx_auth
const libx = require('../../../libx/_runtime')

Object.defineProperty (protocols, 'serve', {
  value: function _serve_legacy_adapter4 (srv, app) {
    const endpoints = this.endpoints4 (srv)
    if (endpoints.length > 1)
      throw cds.error `Cannot serve multiple endpoints if cds.requires.middlewares is set to false`
    return serve (srv, app, {
      before: [
        cds_context,
        libx.perf,
        libx.auth(srv), ctx_auth,
        cap_req_logger,
        cds_context_model.middleware4(srv)
      ],
      after:[]
    })
  }.bind(protocols)
})

const LOG = cds.log(), DEBUG = cds.debug()
function cap_req_logger (req,_,next) {
  let url = req.originalUrl
  try { url = decodeURI(req.originalUrl) } catch (e) { /* decodeURI throws error for invalid urls */ }
  LOG && LOG (req.method, url, req.body||'')
  if (/\$batch/.test(req.url))  req.on ('dispatch', (req) => {
    let path = req._path
    try { path = decodeURI(req._path) } catch (e) { /* decodeURI throws error for invalid urls */ }
    LOG && LOG ('>', req.event, path, req._query||'')
    if (DEBUG && req.query) DEBUG (req.query)
  })
  next()
}
