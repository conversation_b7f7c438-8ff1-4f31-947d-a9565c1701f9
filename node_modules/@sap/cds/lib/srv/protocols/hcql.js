const express = require('express') // eslint-disable-line cds/no-missing-dependencies
const cds = require('../../index')
const { inspect } = require('util')

class HCQLAdapter extends require('./http') {

  schema4 (srv) {
    return cds.minify (cds.model, { service: srv.name })
  }

  router4 (srv) { return super.router4 (srv)

    /** Return CSN schema in response to /<srv>/$csn requests */
    .get('/\\$csn', (_, res) => res.json (this.schema4(srv)))

    .use(express.json()) //> for application/json -> cqn
    .use(express.text()) //> for text/plain -> cql -> cqn

    /**
     * Convenience route for REST-style request formats like that:
     * GET /browse/Books { ID, title, author.name as author } where stock < 100
     * GET /browse/Books/201 { ID, title, author.name as author }
     */
    .get('/:entity/:id?(%20:tail)?', (req, _, next) => {
      let { entity, id, tail } = req.params, q = SELECT.from(entity, id)
      if (is_string(req.body)) tail = req.body
      else if (is_array(req.body)) q.columns(req.body)
      else Object.assign(q.SELECT, req.body)
      if (tail) q = { SELECT: { ...CQL(`SELECT from _ ${tail}`).SELECT, ...q.SELECT } }
      req.body = q; next() // delegating to main handler
    })

    /**
     * The actual protocol adapter, handling all requests.
     */
    .use((req, res, next) => {
      let q = this.query4(req)
      this.logger.info (req.method, decodeURIComponent(req.originalUrl), inspect(q,{colors:true,depth:11}))
      return srv.run(q).then(r => res.json(r)).catch(next)
    })
  }

  query4 (req) {
    if (typeof req.body === 'string') return cds.parse.cql (req.body)
    return req.body //> a plain CQN object
  }
}

const is_string = x => typeof x === 'string'
const is_array = Array.isArray

module.exports = HCQLAdapter
