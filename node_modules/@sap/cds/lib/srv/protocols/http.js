const express = require('express') // eslint-disable-line cds/no-missing-dependencies
const cds = require('../../index')


module.exports = class HttpAdapter {

  constructor (srv) {
    this.kind = this.constructor.name.replace(/Adapter$/,'').toLowerCase()
    this.logger = cds.log (this.kind)
    return this.router4 (srv)
  }

  router4 (srv) {
    return express.Router()
    .use(function req_logger(req,res,next) {
      // this.log expected to be implemented by subclass
      this?.log?.(req)
      next()
    }.bind(this))
    .use(this.early_access_check4(srv))
  }

  /** Does early checks on required roles to reject early */
  early_access_check4 (srv) {

    // Resolve required roles statically once....
    const d = srv.definition
    const roles = d['@requires'] || d['@restrict']?.map(r => r.to).flat()
    || cds.env.requires.auth?.restrict_all_services !== false && process.env.NODE_ENV === 'production' && ['authenticated-user']

    // ... and return a handler function accordingly -> PROBLEM: Extensibility
    if (!roles) return (req, res, next) => next() //> no handlers required

    const required_roles = Array.isArray(roles) ? roles : [roles]
    return function early_access_check (req, res, next) {
      let u = req.user; if (!u?.is) u = new cds.User(u) // revisit
      if (required_roles.some(r => u.is(r))) return next()
      // Following demonstrates how to directly send responses from here...
      // However, in order to allow others to plug in error handlers throwing errors is better.
      // For example, also for ourselves to obfucscate error details in production mode.
      // throw cds.error ({ status: 403, code: 'REQUIRES_AUTH_USER', details: `Requires any of [ ${roles} ]` })
      if (!u._is_anonymous) return res.status(403).send(`User '${u.id}' is lacking required roles: [ ${roles} ]`)
      else if (!req._login) return res.status(401).send('Requires authenticated user')
      else return req._login()
    }
  }
}

/*
function MTXRouter (srv) {
  this.routers = {
    t1_fta: 'new HCQLAdapter(srv.for(t1))',
    t1_fta_ftb: 'new HCQLAdapter(srv.for(t1))',
    t2: 'new HCQLAdapter(srv.for(t2))',
  }
  return express.Router().use((req,res,next) => {
    return this.routers[req.tenant].handle(req,res,next)
  })
}
*/
