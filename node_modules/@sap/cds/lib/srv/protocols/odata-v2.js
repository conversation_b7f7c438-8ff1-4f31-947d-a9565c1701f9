const cds = require('../../index'), { decodeURIComponent } = cds.utils
const LOG = cds.log('odata-v2')
const logger = function cap_legacy_req_logger (req,_,next) {
  if (/\$batch$/.test(req.url)) {
    const prefix = decodeURIComponent(req.originalUrl).replace('$batch','')
    req.on ('dispatch', (req) => {
      LOG && LOG (req.event, prefix+decodeURIComponent(req._path), req._query||'')
      if (LOG._debug && req.query) LOG.debug (req.query)
    })
  } else {
    LOG && LOG (req.method, decodeURIComponent(req.originalUrl), req.body||'')
  }
  next()
}

const ODataV2Proxy = require('./odata-v2-proxy') // ('@sap/cds-odata-v2-adapter-proxy')
module.exports = function ODataV2Adapter (srv) {
  const proxy = new ODataV2Proxy ({
    sourcePath: srv.path,
    targetPath: '/odata/v4',
    target: 'auto', // to detect server url + port dynamically
    logLevel: 'warn',
    ...srv.options, path:""
  })
  return [ logger, proxy ]
}
