const cds = require('../../index'),
  { User } = cds,
  { decodeURI } = cds.utils
const express = require('express') // eslint-disable-line cds/no-missing-dependencies

if (cds.env.features.odata_new_adapter) {
  cds.log().info('using new OData adapter')

  const { isStream, stream } = require('../../../libx/odata/middleware/stream')
  const BaseProtocolAdapter = require('./http')

  module.exports = class ODataAdapter extends BaseProtocolAdapter {
    log(req) {
      let u = req.user
      req.user = u instanceof User ? u : new User(u)

      // REVISIT: how to handle logging of batch subrequests? (note: service is missing from path)
      let url = decodeURI(req.originalUrl)
      this.logger.info(req.method, url, req.body || '')
      if (/\$batch/.test(req.url))
        req.on('dispatch', req => {
          let path = decodeURI(req._path)
          this.logger.info('>', req.event, path, req._query || '')
          if (this.logger._debug && req.query) this.logger.debug(req.query)
        })
    }

    router4(srv) {
      const router = super.router4(srv)
      const jsonBodyParser = express.json()

      return (
        router
          // REVISIT: add middleware for negative cases?
          // service root
          .use(/^\/$/, require('../../../libx/odata/middleware/service-document')(srv))
          .use('/\\$metadata', require('../../../libx/odata/middleware/metadata')(srv))
          // parse
          .use(require('../../../libx/odata/middleware/parse')(srv))
          // REVISIT do we want to build our own body parser logic?
          .use((req, res, next) => {
            // REVISIT: body of batch subrequests are already deserialized
            if (typeof req.body === 'object') return next()
            if (req.method === 'PUT' && isStream(req._query)) {
              req.body = { value: req }
              return next()
            }
            // TODO: check if the raw body still exists, then we can remove deepCopy() in the handlers
            return jsonBodyParser(req, res, next)
          })
          // batch
          .post('/\\$batch', require('../../../libx/odata/middleware/batch')(srv, router))
          // handle
          // REVISIT: with old adapter, we return 405 for HEAD requests -> check OData spec
          .head('*', (_, res) => res.sendStatus(405))
          .post('*', require('../../../libx/odata/middleware/operation')(srv)) //> action
          .get('*', require('../../../libx/odata/middleware/operation')(srv)) //> function
          .post('*', require('../../../libx/odata/middleware/create')(srv))
          .get('*', stream(srv))
          .get('*', require('../../../libx/odata/middleware/read')(srv))
          .put('*', require('../../../libx/odata/middleware/update')(srv, router))
          .patch('*', require('../../../libx/odata/middleware/update')(srv, router))
          .delete('*', require('../../../libx/odata/middleware/delete')(srv))
          // error
          .use(require('../../../libx/odata/middleware/error')(srv))
      )
    }
  }
} else {
  const libx = require('../../../libx/_runtime')
  const LOG = cds.log('odata')
  module.exports = function ODataAdapter(srv) {
    const router = express.Router()

    router.use((req, _, next) => {
      let u = req.user
      req.user = u instanceof User ? u : new User(u)

      let url = decodeURI(req.originalUrl)
      LOG && LOG(req.method, url, req.body || '')
      if (/\$batch/.test(req.url))
        req.on('dispatch', req => {
          let path = decodeURI(req._path)
          LOG && LOG('>', req.event, path, req._query || '')
          if (LOG._debug && req.query) LOG.debug(req.query)
        })

      next()
    })
    router.use(libx.to.odata_v4(srv))
    return router
  }
}
