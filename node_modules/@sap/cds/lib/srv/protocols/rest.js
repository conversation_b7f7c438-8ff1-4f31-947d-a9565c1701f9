const cds = require('../../index'), { User } = cds, { decodeURIComponent } = cds.utils
const libx = require('../../../libx/_runtime')
const LOG = cds.log('rest')

module.exports = function RestAdapter (srv) { return [
  (req, _, next) => {
    let u = req.user
    req.user = u instanceof User ? u : new User(u)
    LOG (req.method, decodeURIComponent(req.originalUrl), req.body||'')
    next()
  },
  libx.to.rest (srv)
]}
