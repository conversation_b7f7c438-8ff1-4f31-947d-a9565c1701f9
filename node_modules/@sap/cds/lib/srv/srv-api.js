//////////////////////////////////////////////////////////////
//
//  PLEASE DO NOT RUN prettier ON THIS FILE
//
//////////////////////////////////////////////////////////////

const cds = require('..'), { Event, Request } = cds
const add_methods_to = require ('./srv-methods')

class Service extends require('./srv-handlers') {

  constructor (name, model, o) {
    if (is_object(name)) {
      [ model, o ] = [ name, model ]
      let srv = cds.linked(model).services[0] || cds.error.expected `${{model}} passed as first argument to be a CSN with a single service definition`
      name = srv.name
    }
    super (name || new.target.name) .options = o || (o={})
    if (o.kind) this.kind = o.kind // shortcut
    if (model) this.model = model
  }

  /**
   * Subclasses may override this to prepare the given model appropriately
   */
  set model (csn) {
    if (csn) {
      let {definitions:defs={}} = super.model = cds.compile.for.nodejs(csn)
      super.definition = defs[this.options?.service] || defs[this.name]
      add_methods_to (this)
    } else {
      super.model = undefined
    }
  }

  /**
   * Messaging API to emit asynchronous event messages, i.e. instances of `cds.Event`.
   */
  emit (event, data, headers) {
    const eve = event instanceof Event ? event : new Event (
      is_object(event) ? event
      : { event, data, headers }
    )
    return this.dispatch (eve)
  }

  /**
   * REST-style API to send synchronous requests...
   */
  send (method, path, data, headers) {
    const req = method instanceof Request ? method : new Request (
      is_object(method) ? method :
      is_object(path) ? { method, data:path, headers:data }
      : { method, path, data, headers }
    )
    return this.dispatch (req)
  }
  get    (...args) { return is_rest(args[0]) ? this.send('GET',   ...args) : this.read   (...args) }
  put    (...args) { return is_rest(args[0]) ? this.send('PUT',   ...args) : this.update (...args) }
  post   (...args) { return is_rest(args[0]) ? this.send('POST',  ...args) : this.create (...args) }
  patch  (...args) { return is_rest(args[0]) ? this.send('PATCH', ...args) : this.update (...args) }
  delete (...args) { return is_rest(args[0]) ? this.send('DELETE',...args) : DELETE.from (...args).bind(this) }

  /**
   * Querying API to send synchronous requests...
   */
  run (query, data) {
    if (typeof query === 'function') {
      const fn = query; if (this.context) return fn(this)     // if this is a tx -> run fn with this
      const ctx = cds.context, tx = ctx?.tx                   // is there an (open) outer tx? ...
      if (!tx || tx._done === 'committed') return this.tx(fn) // no -> run fn with root tx
      if (tx._done !== 'rolled back') return fn(this.tx(ctx)) // yes -> run fn with nested tx
      else throw this.tx._is_done (tx._done)                  // throw if outer tx was rolled back
    }
    const req = new Request ({ query, data })
    return this.dispatch (req)
  }
  read   (...args) { return is_query(args[0]) ? this.run(...args) : SELECT(...args).bind(this) }
  insert (...args) { return INSERT(...args).bind(this) }
  create (...args) { return INSERT.into(...args).bind(this) }
  update (...args) { return UPDATE.entity(...args).bind(this) }
  upsert (...args) { return UPSERT(...args).bind(this) }
  exists (...args) { return SELECT.one([1]).from(...args).bind(this) }

  /**
   * Streaming API variant of .run(). Subclasses should override this to support real streaming.
   * The default implementation doesn't stream, but simply invokes the callback on each row.
   * The callback function is invoked with (row, index).
   */
  foreach (query, data, callback) {
    if (!callback)  [ data, callback ] = [ undefined, data ]
    return this.run (query, data) .then (rows => rows.forEach(callback) || rows)
  }

  /**
   * Model Reflection API...
   */
  get namespace()  {
    return super.namespace  = this.definition?.name
    || this.model?.namespace
    || !this.isDatabaseService && !/\W/.test(this.name) && this.name
    || undefined
  }
  get entities()   { return super.entities   = _reflect (this, d => d.kind === 'entity') }
  get events()     { return super.events     = _reflect (this, d => d.kind === 'event') }
  get types()      { return super.types      = _reflect (this, d => !d.kind || d.kind === 'type') }
  get actions()    { return super.actions    = _reflect (this, d => d.kind === 'action' || d.kind === 'function') }
  get operations() { return super.operations = _reflect (this, d => d.kind === 'action' || d.kind === 'function') }

  /**
   * Flag to control whether this service is extensible.
   * Can be overridden by subclasses.
   */
  get isExtensible() {
    return this.model === cds.model && !this.name?.startsWith('cds.xt.') // REVISIT cds.xt name check should move to respective services
  }

  /**
   * Subclasses may override this to free resources when
   * tenants offboard or the service is disposed.
   */
  disconnect (tenant) { // eslint-disable-line no-unused-vars
    // if (this === cds.db) { //> REVISIT: should go into DatabaseService
    //   if (!tenant) cds.db = undefined
    //   else if (this.dbcs) this.dbcs.delete[tenant] // This code is obviously wrong and never tested -> is that required ?!?
    // }
    // delete cds.services[this.name] // REVISIT: this is in contrast to some tests
  }

  get path() { return super.path = cds.service.protocols.path4(this) }
  set path(p) { super.path = p }

  get endpoints() { return super.endpoints = cds.service.protocols.endpoints4(this) }
  set endpoints(p) { super.endpoints = p }
}

const { dispatch, handle } = require('./srv-dispatch')
Service.prototype.tx = require('./srv-tx')
Service.prototype.handle = handle
Service.prototype.dispatch = dispatch
Service.prototype.transaction = Service.prototype.tx
Service.prototype._implicit_next = cds.env.features.implicit_next
Service.prototype._is_service_instance = Service._is_service_class = true //> for factory
module.exports = Service

// Helpers...
const _reflect = (srv,filter) => !srv.model ? [] : srv.model.childrenOf (srv.namespace,filter)
const is_rest = x => x && typeof x === 'string' && x[0] === '/'
const is_query = x => x && x.bind || is_array(x) && !x.raw
const is_array = (x) => Array.isArray(x) && !x.raw
const is_object = (x) => typeof x === 'object'

// Deprecated
/** @deprecated: To be removed with the next major release */
Service.prototype.stream = cds.utils.deprecated (function (...args) {
  if (is_query(args[0])) {
    Object.defineProperty(args[0], '_stream', { value: true, enumerable: false })
    if (cds.env.features.stream_compat)
      Object.defineProperty(args[0], '_streaming', { value: true, enumerable: false })
    return this.run(...args).then(result => {
      if (!result) return result
      return Array.isArray(result) ? Object.values(result[0])[0] : Object.values(result)[0]})
  }
  const q = (args ? SELECT.one.columns(args) : SELECT.one).bind(this)
  Object.defineProperty(q, '_stream', { value: true, enumerable: false })
  if (cds.env.features.stream_compat) Object.defineProperty(q, '_streaming', { value: true, enumerable: false })
  return q
}, {
  old: 'srv.stream()',
  use: 'srv.read()'
})
