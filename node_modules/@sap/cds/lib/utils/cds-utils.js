const cwd = process.env._original_cwd || process.cwd()
const cds = require('../index')

module.exports = exports = new class {
  get inflect() { return super.inflect = require('./inflect') }
  get inspect() { return super.inspect = require('util').inspect }
  get uuid() { return super.uuid = require('crypto').randomUUID }
  get yaml() { return super.yaml = require('@sap/cds-foss').yaml }
  get pool() { return super.pool = require('@sap/cds-foss').pool }
  get tar() { return super.tar = require('./tar') }
}

const path = exports.path = require('path'), { dirname, extname, join, resolve, relative } = path
const fs = exports.fs = Object.assign (exports,require('fs')) //> for compatibility


/**
 * Variant of `Object.keys()` which includes all keys inherited from the
 * given object's prototypes.
 */
exports.Object_keys = o => ({
  [Symbol.iterator]: function*(){ for (let k in o) yield k },
  forEach(f){ let i=0; for (let k in o) f(k,i++,o) },
  filter(f){ let i=0, r=[]; for (let k in o) f(k,i++,o) && r.push(k); return r },
  map(f){ let i=0, r=[]; for (let k in o) r.push(f(k,i++,o)); return r },
  some(f){ for (let k in o) if (f(k)) return true },
  find(f){ for (let k in o) if (f(k)) return k },
})


/**
 * Simple helper to always access results as arrays.
 */
exports.results = oa => {
  return Array.isArray(oa) ? oa : oa != null ? [oa] : []
}


/**
 * Should be used in data providers, i.e., db services to return single
 * rows in response to SELECT.one queries.
 */
exports.chimera = oa => {
  return Array.isArray(oa) ? oa : Object.defineProperties(oa,chimera)
}
const chimera = Object.getOwnPropertyDescriptors (class Chimera {
  *[Symbol.iterator] (){ yield this }
  forEach(f){ f(this,0,this) }
  filter(f){ return f(this,0,this) ? [this] : [] }
  map(f){ return [f(this,0,this)] }
  some(f){ return f(this,0,this) }
  find(f){ if (f(this,0,this)) return this }
}.prototype)


exports.decodeURIComponent = s => { try { return decodeURIComponent(s) } catch { return s } }
exports.decodeURI = s => { try { return decodeURI(s) } catch { return s } }

exports.local = (file) => relative(cwd,file)

exports.exists = function exists (x) {
  if (x) {
    const y = resolve (cds.root,x)
    return fs.existsSync(y)
  }
}

// REVISIT naming: doesn't return boolean
exports.isdir = function isdir (...args) {
  if (args.length) try {
    const y = resolve (cds.root,...args)
    const ls = fs.lstatSync(y)
    if (ls.isDirectory()) return y
    if (ls.isSymbolicLink()) return isdir (join (dirname(y), fs.readlinkSync(y)))
  } catch(e){/* ignore */}
}

// REVISIT naming: doesn't return boolean
exports.isfile = function isfile (...args) {
  if (args.length) try {
    const y = resolve (cds.root,...args)
    const ls = fs.lstatSync(y)
    if (ls.isFile()) return y
    if (ls.isSymbolicLink()) return isfile (join (dirname(y), fs.readlinkSync(y)))
  } catch(e){/* ignore */}
}

exports.stat = async function (x) {
  const d = resolve (cds.root,x)
  return fs.promises.stat(d)
}

exports.readdir = async function (x) {
  const d = resolve (cds.root,x)
  return fs.promises.readdir(d)
}

exports.read = async function read (file, _encoding) {
  const f = resolve (cds.root,file)
  const src = await fs.promises.readFile (f, _encoding !== 'json' && _encoding || 'utf8')
  if (_encoding === 'json' || !_encoding && f.endsWith('.json')) try {
    return JSON.parse(src)
  } catch(e) {
    throw new Error (`Failed to parse JSON in ${f}: ${e.message}`)
  }
  else return src
}

exports.write = function write (file, data, o) {
  if (arguments.length === 1) return {to:(...path) => write(join(...path),file)}
  if (typeof data === 'object' && !Buffer.isBuffer(data))
    data = JSON.stringify(data, null, ' '.repeat(o && o.spaces)) + '\n'
  const f = resolve (cds.root,file)
  return fs.mkdirp (dirname(f)).then (()=> fs.promises.writeFile (f,data,o))
}

exports.copy = function copy (x,y) {
  if (arguments.length === 1) return {to:(...path) => copy(x,join(...path))}
  const src = resolve (cds.root,x)
  const dst = resolve (cds.root,y)
  if (fs.promises.cp) return fs.promises.cp (src,dst,{recursive:true})
  return fs.mkdirp (dirname(dst)) .then (async ()=>{
    if (fs.isdir(src)) {
      const entries = await fs.promises.readdir(src)
      return Promise.all (entries.map (async each => {
        const e = join (src,each)
        const f = join (dst,each)
        return copy (e,f)
      }))
    } else {
      return fs.promises.copyFile (src,dst)
    }
  })
}

exports.mkdirp = async function (...path) {
  const d = resolve (cds.root,...path)
  await fs.promises.mkdir (d,{recursive:true})
  return d
}

exports.rmdir = async function (...path) {
  const d = resolve (cds.root,...path)
  return fs.promises.rm (d, {recursive:true})
}

exports.rimraf = async function (...path) {
  const d = resolve (cds.root,...path)
  return fs.promises.rm (d, {recursive:true,force:true})
}

exports.rm = async function rm (x) {
  const y = resolve (cds.root,x)
  return fs.promises.rm(y)
}

exports.find = function find (base, patterns='*', filter=()=>true) {
  const files=[];  base = resolve (cds.root,base)
  if (typeof patterns === 'string')  patterns = patterns.split(',')
  if (typeof filter === 'string')  filter = this[filter]
  patterns.forEach (pattern => {
    const star = pattern.indexOf('*')
    if (star >= 0) {
      const head = pattern.slice(0,star).replace(/[^/\\]*$/,'')
      const dir = join (base,head)
      try {
        const ls = fs.lstatSync(dir)
        if (ls.isDirectory()) {
          const [,suffix,tail] = /([^/\\]*)?(?:.(.*))?/.exec (pattern.slice(star+1))
          const prefix = pattern.slice(head.length,star)
          let entries = fs.readdirSync(dir) //.filter (_filter)
          if (prefix)  entries = entries.filter (e => e.startsWith(prefix));  if (!entries.length) return
          if (suffix)  entries = entries.filter (e => e.endsWith(suffix));  if (!entries.length) return
          let paths = entries.map (e=>join(dir,e))
          if (filter)  paths = paths.filter (filter);  if (!paths.length) return
          if (tail)  for (let _files of paths.map (e=>find (e,tail,filter)))  files.push (..._files)
          else  files.push (...paths)
        }
      } catch(e) {/* ignore */}
    } else {
      const file = join (base, pattern)
      if (fs.existsSync(file))  files.push (file)
    }
  })
  return files
}

exports.deprecated = (fn, { kind = 'Method', old = fn.name+'()', use } = {}) => {
  const yellow = '\x1b[33m'
  const reset = '\x1b[0m'
  // use cds.log in production for custom logger
  const log = cds.env.production ? cds.log().warn: console.log
  if(typeof fn !== 'function') {
    if (cds.env.features.deprecated === 'off') return
    [kind,old,use] = [fn.kind || 'Configuration',fn.old,fn.use]
    log (
      yellow,
      '\n------------------------------------------------------------------------------',
      '\nDEPRECATED:', old, '\n',
      '\n  ', (kind ? `${kind} ${old}` : old), 'is deprecated and will be removed in upcoming releases!',
      use ? `\n   => Please use ${use} instead.` : '', '\n',
      '\n------------------------------------------------------------------------------\n',
      reset
    )
  } else return function() {
    if (cds.env.features.deprecated !== 'off' && !fn.warned) {
      let o={}; Error.captureStackTrace(o)
      log (
        yellow,
        '\n------------------------------------------------------------------------------',
        '\nDEPRECATED:', old, '\n',
        '\n  ', (kind ? `${kind} ${old}` : old), 'is deprecated and will be removed in upcoming releases!',
        use ? `\n   => Please use ${use} instead.` : '', '\n',
        o.stack.replace(/^Error:\s*at.*\n/,'\n'), '\n',
        '\n------------------------------------------------------------------------------\n',
        reset
      )
      if (cds.env.features.deprecated !== 'show all') fn.warned = true
    }
    return fn.apply (this, arguments)
  }
}

exports.csv = require('./csv-reader')

/**
 * Internal utility to load a file through ESM or CommonJs.  TODO find a better place.
 */
exports._import = id => require(id)
if (process.env.JEST_WORKER_ID === undefined) {  // jest's ESM support is experimental: https://jestjs.io/docs/ecmascript-modules
  const { pathToFileURL } = require('url')
  exports._import = id => {
    if (extname(id) === '.ts') {
      try {
        return require(id) // ts-node w/ ESM not working ootb (cap/issues#14463), so try CommonJS first
      } catch (err) {
        if (err.code !== 'ERR_REQUIRE_ESM')  throw err
        // else: this means ts-node is configured w/ an ESM loader, so fall through and try w/ ESM
      }
    }
    return import (pathToFileURL(id).href) // must use a file: URL, esp. on Windows for C:\... paths
  }
}

/**
 * Masks password-like strings, also reducing clutter in output
 */
const SECRETS = /(password)|(certificate)|(ca)|(clientsecret)|(secret)|(key)|(clientcert)/i
exports._redacted = function _redacted(cred) {
  if (!cred) return cred
  if (Array.isArray(cred)) return cred.map(_redacted)
  if (typeof cred === 'object') {
    const newCred = Object.assign({}, cred)
    Object.keys(newCred).forEach(k => (typeof newCred[k] === 'string' && SECRETS.test(k)) ? (newCred[k] = '...') : (newCred[k] = _redacted(newCred[k])))
    return newCred
  }
  return cred
}
