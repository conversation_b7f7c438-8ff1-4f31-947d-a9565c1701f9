const required = _major_minor(
  require('../../package.json').engines.node.match(/>=(.*)/)[1]
)
const given = _major_minor(
  process.version.match(/^v(\d+\.\d+)/)[1]
)

if (given.major < required.major || given.major === required.major && given.minor < required.minor) process.exit (process.stderr.write (`
  Node.js v${required.version} or higher is required for @sap/cds.
  Current v${given.version} does not satisfy this.
  \n`) || 1)

if (given.major < 18) process.stderr.write (`WARNING: \n
  Node.js v${given.major} has reached end of life. Please upgrade to v18 or higher.
  \n`)

function _major_minor (version) {
  let [ major, minor ] = version.split('.').map(x => +x)
  return { version, major, minor }
}
