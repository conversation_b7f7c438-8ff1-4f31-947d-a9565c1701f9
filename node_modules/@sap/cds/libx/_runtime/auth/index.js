const cds = require('../cds')
const LOG = cds.log()

const _require = require('../common/utils/require')
const { containsAnyRestrictions } = require('../common/utils/restrictions')
const { ODATA_UNAUTHORIZED } = require('../common/error/constants')

let passport, logged

// strategy initializers for lazy loading of dependencies
const _initializers = {
  // REVISIT: support basic authentication?
  basic: ({ credentials, users }) => {
    const BasicStrategy = require('./strategies/basic')
    passport.use(new BasicStrategy(credentials || users))
  },
  dummy: () => {
    const DummyStrategy = require('./strategies/dummy')
    passport.use(new DummyStrategy())
  },
  jwt: ({ credentials, uaa }) => {
    const JWTStrategy = require('./strategies/JWT')
    if (credentials) {
      passport.use(new JWTStrategy(credentials))
    } else if (uaa) {
      // REVISIT: compat, remove with cds^6
      passport.use(new JWTStrategy(uaa.credentials))
    } else {
      throw Object.assign(new Error('No or malformed credentials for auth kind "jwt-auth"'), { credentials })
    }
  },
  mock: ({ users }, srvName) => {
    const MockStrategy = require('./strategies/mock')
    passport.use(new MockStrategy(users, `mock_${srvName}`))
  },
  xsuaa: ({ credentials, uaa }) => {
    const XSUAAStrategy = require('./strategies/xsuaa')
    if (credentials) {
      passport.use(new XSUAAStrategy(credentials))
    } else if (uaa) {
      // REVISIT: compat, remove with cds^6
      passport.use(new XSUAAStrategy(uaa.credentials))
    } else {
      throw Object.assign(
        new Error('No or malformed credentials for auth kind "xsuaa". Make sure to bind the app to an "xsuaa" service'),
        { credentials }
      )
    }
  }
}

// map for initialized authenticators
const _authenticators = {}

const _log = (req, challenges) => {
  if (!LOG._debug) return
  const challengesLog = challenges ? ['User challenges:', challenges] : []
  LOG.debug(`User "${req.user.id}" request URL`, req.url, '\n', ...challengesLog)
}

const cap_auth_callback = (req, res, next, internalError, user, arg) => {
  // An internal error occurs during the authentication process
  if (internalError) {
    return res.status(401).json({ error: ODATA_UNAUTHORIZED }) // no details to client
  }

  let challenges
  if (arg) {
    if (!user) {
      // > challenges
      if (Array.isArray(arg)) {
        challenges = arg.filter(ele => ele)
        challenges = challenges.length ? challenges : undefined
      } else {
        challenges = [arg]
      }
    } else {
      // REVISIT: req._.req.authInfo compat
      if (arg.verifyToken) req.authInfo = arg
    }
  }
  req.user = user || Object.defineProperty(new cds.User(), '_challenges', { enumerable: false, value: challenges })
  _log(req, challenges)

  next()
}

const _mountCustomAuth = (srv, app, config) => {
  const impl = cds.resolve(config.impl)
  app.use(_require(impl))
}

const _mountMockAuth = (srv, app, strategy, config) => {
  const impl =
    strategy === 'dummy'
      ? new (require('./strategies/dummy'))()
      : new (require('./strategies/mock'))(config, `mock_${srv.name}`)

  app.use(function cap_auth(req, res, next) {
    let user, challenge
    impl.success = arg => (user = arg)
    impl.fail = arg => (challenge = arg)
    impl.authenticate(req)
    cap_auth_callback(req, res, next, undefined, user, [challenge])
  })
}

const _mountPassportAuth = (srv, app, strategy, config) => {
  if (strategy in { jwt: 1, xsuaa: 1 } && !config.credentials) {
    let msg = `Authentication kind "${config.kind}" configured, but no XSUAA instance bound to application.`
    msg += ' Either bind an XSUAA instance, or switch to an authentication kind that does not require a binding.'
    throw new Error(msg)
  }

  if (!passport) passport = _require('passport')

  // initialize strategy
  if (!_authenticators[strategy] || process.env.NODE_ENV === 'test') {
    _initializers[strategy](config, srv.name)
    _authenticators[strategy] = true
  }

  // authenticate
  app.use(passport.initialize())
  app.use((req, res, next) => {
    const options = { session: false, failWithError: true }
    const callback = cap_auth_callback.bind(undefined, req, res, next)
    passport.authenticate(strategy === 'jwt' ? 'JWT' : strategy, options, callback)(req, res, next)
  })
}

/*
 * export authentication middleware
 */
// eslint-disable-next-line complexity
module.exports = (srv, options = srv.options) => {
  const handlers = [],
    app = { use: h => handlers.push(h) }

  // NOTE: options.auth is not an official API
  let config = 'auth' in options ? options.auth : cds.env.requires.auth
  if (!config) {
    // REVISIT: can config be falsy? req.user would be undefined!
    if (cds.requires.db && cds.requires.multitenancy) {
      process.exitCode = 1 // REVISIT: why exitCode needed?
      throw new Error('Authentication required for multitenancy')
    }
    if (containsAnyRestrictions(srv)) {
      process.exitCode = 1 // REVISIT: why exitCode needed?
      throw new Error('Authentication required for authorization checks')
    }
    if (process.env.NODE_ENV !== 'production' && !logged) {
      LOG._warn && LOG.warn(`No authentication configured. This is not recommended in production.`)
    }
    // no auth wanted > return
    return handlers
  }

  // cds.env.requires.auth = { kind: 'xsuaa-auth' } was briefly documented on capire -> also support
  if (config.kind === 'xsuaa-auth' && !config.credentials) config = cds.env.requires.xsuaa

  // mount authentication middleware or strategy
  if (!logged) LOG._debug && LOG.debug(`Using authentication`, { kind: config.kind })

  if (config.impl) {
    // mount custom authentication middleware
    _mountCustomAuth(srv, app, config)
  } else if (config.kind === 'ias' || config.kind === 'ias-auth') {
    // ias-auth follows the new implementation pattern for auth middlewares
    const iasAuth = require('./strategies/ias-auth')(config)
    if (iasAuth) app.use(iasAuth)
  } else {
    // mount our authentication strategies (legacy style)
    const strategy = _strategy4(config)
    if (strategy in { dummy: 1, mock: 1 }) {
      _mountMockAuth(srv, app, strategy, config)
    } else {
      _mountPassportAuth(srv, app, strategy, config)
    }
  }

  // so we don't log the same stuff multiple times
  logged = true

  return handlers
}

const _strategy4 = config => {
  const strategy = config.kind.replace('mocked', 'mock').replace(/-auth$/, '')
  if (strategy in _initializers) return strategy
  process.exitCode = 1 // REVISIT: why exitCode needed?
  throw new Error(`Authentication kind "${config.kind}" is not supported`)
}
