const cds = require('../../cds')
const CHALLENGE = 'Basic realm="Users"'

class MockStrategy {
  constructor({ users, tenants }, name = 'mock') {
    this.name = name
    this.users = _init_users(users || cds.env.requires.auth.users || {}, tenants)
  }

  authenticate(req) {
    const { authorization } = req.headers
    if (!authorization) return this.fail(CHALLENGE)

    const [scheme, base64] = authorization.split(' ')
    if (!scheme || scheme.toLowerCase() !== 'basic') return this.fail(CHALLENGE)
    if (!base64) return this.fail(CHALLENGE)

    const [id, password] = Buffer.from(base64, 'base64').toString().split(':')
    const user = this.users[id] || (this.users['*'] && { id })
    if (!user) return this.fail(CHALLENGE)
    if (user.password && user.password !== password) return this.fail(CHALLENGE)

    const { features } = req.headers
    this.success(new cds.User(features ? { ...user, features } : user))
  }
}

// eslint-disable-next-line complexity
const _init_users = (users, tenants = {}) => {
  if (!users._initialized) {
    Object.defineProperty(users, '_initialized', { value: true })
    for (let [id, user] of Object.entries(users)) {
      if (id !== '*') user.id = user.ID || user.id || id // fill in user ids
      if (id !== user.id) users[user.id] = user
      if (user.jwt) {
        let scopes = user.jwt.scope || user.jwt.scopes
        if (scopes) {
          const aud = user.jwt.aud
          if (aud)
            scopes = scopes.map(s => {
              for (const each of aud) s = s.replace(`${each}.`, '')
              return s
            })
          Array.isArray(user.roles) ? user.roles.push(...scopes) : (user.roles = scopes)
        }
        if (user.jwt.grant_type === 'client_credentials' || user.jwt.grant_type === 'client_x509') {
          Array.isArray(user.roles) ? user.roles.push('system-user') : (user.roles = ['system-user'])
        }
        if (!user.tenant && user.jwt.zid) user.tenant = user.jwt.zid
      }

      // optimization:
      if (user.roles)
        user.roles = user.roles.reduce((p, n) => {
          p[n] = 1
          return p
        }, {})

      // apply tenant info if any
      if (!user.features) {
        const features = tenants[user.tenant]?.features
        if (features) user.features = features
      }

      const attr = Object.assign(
        {},
        user.userAttributes,
        user.jwt && user.jwt.userInfo,
        user.jwt && user.jwt.attributes
      )
      if (Object.keys(attr).length > 0) user.attr = attr
    }
  }
  return users
}

module.exports = MockStrategy
