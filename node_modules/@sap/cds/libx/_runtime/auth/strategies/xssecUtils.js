const CLIENT = { client_credentials: 1, client_x509: 1 }

const getUserId = (user, info) => {
  // fallback for grant_type=client_credentials (xssec v3)
  return user.id || (info && info.getClientId && info.getClientId())
}

const addRolesFromGrantType = (user, info, credentials) => {
  const grantType = info && (info.grantType || (info.getGrantType && info.getGrantType()))
  if (grantType) {
    // > not "weak"
    user.roles['authenticated-user'] = true
    if (grantType in CLIENT) {
      user.roles['system-user'] = true
      if (info.getClientId() === credentials.clientid) user.roles['internal-user'] = true
    }
  }
}

const getRoles = (roles, info) => {
  // convert to object
  roles = Object.assign(...roles.map(ele => ({ [ele]: true })))

  if (info && info.checkLocalScope && typeof info.checkLocalScope === 'function') {
    // > xssec v3
    roles = new Proxy(roles, {
      get: function (t, role) {
        return role in t ? t[role] : info.checkLocalScope(role)
      }
    })
  }

  return roles
}

const getAttrForJWT = info => {
  if (!info) {
    return {}
  }

  if (info.getAttribute && typeof info.getAttribute === 'function') {
    // > xssec v3
    return new Proxy(
      {},
      {
        get: function (_, attr) {
          return info.getAttribute(attr)
        }
      }
    )
  }

  return {}
}

// xssec v3 only
const getAttrForXSSEC = info => {
  if (!info) return {}

  return new Proxy(
    {},
    {
      get: function (_, attr) {
        // try to get saml attribute via API (getEmail, getFamilyName, etc.)
        try {
          const getter = `get${attr[0].toUpperCase()}${attr.slice(1)}`
          if (info[getter] && typeof info[getter] === 'function') {
            return info[getter]()
          }
        } catch (e) {
          // ignore
        }

        // default to getAttribute
        return info.getAttribute(attr)
      }
    }
  )
}

const getTenant = info => {
  // xssec v3
  return info && info.getZoneId && info.getZoneId()
}

module.exports = {
  getUserId,
  getRoles,
  getAttrForJWT,
  getAttrForXSSEC,
  getTenant,
  addRolesFromGrantType
}
