const cds = require('../../cds')

const _require = require('../../common/utils/require')
const xssecUtils = require('./xssecUtils')

// use _require for a better error message
const { JWTStrategy: JS } = _require('@sap/xssec')

class XSUAAStrategy extends JS {
  constructor(credentials) {
    super(credentials)
    this.credentials = credentials
    this.name = 'xsuaa'
  }

  authenticate(req, options) {
    const credentials = this.credentials

    // monkey patch success
    const _success = this.success
    this.success = (user, info) => {
      // create cds.User
      user = new cds.User({
        id: xssecUtils.getUserId(user, info),
        roles: xssecUtils.getRoles(['any', 'identified-user'], info, credentials),
        attr: xssecUtils.getAttrForXSSEC(info)
      })
      xssecUtils.addRolesFromGrantType(user, info, credentials)
      const tenant = xssecUtils.getTenant(info)
      if (tenant) user.tenant = tenant
      // call "super.success"
      _success(user, info)
    }
    super.authenticate(req, options)
  }
}

module.exports = XSUAAStrategy
