const cds = require('../../../../cds')

const ODataRequest = require('../ODataRequest')

const {
  Components: { ACTION_EXECUTE_HANDLER }
} = require('../okra/odata-server')

const { getSapMessages } = require('../../../../common/error/frontend')
const { getActionOrFunctionReturnType, isReturnMinimal } = require('../utils/handlerUtils')
const { validateResourcePath } = require('../utils/request')
const { toODataResult, postProcess } = require('../utils/result')
const { DRAFT_EVENTS } = require('../../../../common/constants/events')
const { readAfterWrite } = require('../utils/readAfterWrite')
const { toBase64url } = require('../../../../common/utils/binary')

const _postProcess = async (req, odataReq, odataRes, tx, result) => {
  const returnType = getActionOrFunctionReturnType(odataReq.getUriInfo().getPathSegments(), tx.model.definitions)
  // as of spec meeting: no generic support of $select/$expand for custom actions/functions
  if (returnType && returnType.kind === 'entity' && (req.event in DRAFT_EVENTS || req.event === 'EDIT')) {
    result = await readAfterWrite(req, tx, { operation: { result, returnType } })
    if (result && !('IsActiveEntity' in result)) result.IsActiveEntity = req.event === 'draftActivate'
  }
  const _req = Object.setPrototypeOf({ target: returnType || req.target }, req)
  postProcess(_req, odataRes, tx, result)
  return result
}

/**
 * The handler that will be registered with odata-v4.
 *
 * @param {import('../../../../common/Service')} service
 * @returns {function}
 */
const action = service => {
  return async (odataReq, odataRes, next) => {
    let req

    try {
      validateResourcePath(odataReq, service)
      req = new ODataRequest(ACTION_EXECUTE_HANDLER, service, odataReq, odataRes)
    } catch (e) {
      return next(e)
    }

    const changeset = odataReq.getAtomicityGroupId()
    const tx = changeset ? odataReq.getBatchApplicationData().txs[changeset] : service.tx(req)
    cds.context = tx

    let result, err

    try {
      result = await tx.dispatch(req)

      result = await _postProcess(req, odataReq, odataRes, tx, result)

      if (changeset) {
        // for passing into commit
        odataReq.getBatchApplicationData().results[changeset].push({ result, req })
      } else {
        await tx.commit(result)
      }

      if (result == null || isReturnMinimal(req)) odataRes.setStatusCode(204, { overwrite: true })
      else if (req.event === 'draftActivate' || req.event === 'EDIT') {
        const keys = Object.keys(req.target.keys).filter(k => {
          return k !== 'IsActiveEntity' && !req.target.keys[k]._isAssociationStrict
        })
        const keysString = keys
          .map(key => {
            let val = result[key]
            if (Buffer.isBuffer(val)) {
              val = toBase64url(result[key])
            }
            return `${key}=${val}`
          })
          .join(',')
        odataRes.setHeader(
          'location',
          `../${req.target.name.replace(`${service.name}.`, '')}(${keysString},IsActiveEntity=${
            req.event === 'draftActivate'
          })`
        )
      }
    } catch (e) {
      err = e

      if (!changeset) {
        // REVISIT: rollback needed if an error occurred before commit attempted -> how to distinguish?
        await tx.rollback(e).catch(() => {})
      }
    } finally {
      req.messages?.length && odataRes.setHeader('sap-messages', getSapMessages(req.messages, req.http.req))

      if (err) next(err)
      else next(null, toODataResult(result, req))
    }
  }
}

module.exports = action
