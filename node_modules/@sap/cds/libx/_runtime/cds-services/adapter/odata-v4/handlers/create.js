const cds = require('../../../../cds')
const ODataRequest = require('../ODataRequest')

const {
  Components: { DATA_CREATE_HANDLER }
} = require('../okra/odata-server')

const { validateResourcePath } = require('../utils/request')
const { isReturnMinimal } = require('../utils/handlerUtils')
const { readAfterWrite } = require('../utils/readAfterWrite')
const { toODataResult, postProcess, postProcessMinimal } = require('../utils/result')

const { getSapMessages } = require('../../../../common/error/frontend')

/**
 * The handler that will be registered with odata-v4.
 *
 * @param {import('../../../../common/Service')} service
 * @returns {function}
 */
const create = service => {
  return async (odataReq, odataRes, next) => {
    let req

    try {
      validateResourcePath(odataReq, service)
      req = new ODataRequest(DATA_CREATE_HANDLER, service, odataReq, odataRes)
    } catch (e) {
      return next(e)
    }

    const changeset = odataReq.getAtomicityGroupId()
    const tx = changeset ? odataReq.getBatchApplicationData().txs[changeset] : service.tx(req)
    cds.context = tx

    let result, err

    try {
      result = await tx.dispatch(req)

      // REVISIT:
      // Performance: For `isReturnMinimal` it's enough to just read the etag.
      // Note: Without read access, one cannot return the etag.
      if (req._.readAfterWrite) {
        result = await readAfterWrite(req, service, { operation: { result } })
      }
      if (isReturnMinimal(req)) {
        result = postProcessMinimal(req, service, result)
      } else {
        postProcess(req, odataRes, service, result)
      }

      if (changeset) {
        // for passing into commit
        odataReq.getBatchApplicationData().results[changeset].push({ result, req })
      } else {
        await tx.commit(result)
      }

      if (result == null || isReturnMinimal(req)) {
        odataRes.setStatusCode(204, { overwrite: true })
      }
    } catch (e) {
      err = e

      if (!changeset) {
        // REVISIT: rollback needed if an error occurred before commit attempted -> how to distinguish?
        await tx.rollback(e).catch(() => {})
      }
    } finally {
      req.messages?.length && odataRes.setHeader('sap-messages', getSapMessages(req.messages, req.http.req))

      if (err) next(err)
      else next(null, toODataResult(result, req))
    }
  }
}

module.exports = create
