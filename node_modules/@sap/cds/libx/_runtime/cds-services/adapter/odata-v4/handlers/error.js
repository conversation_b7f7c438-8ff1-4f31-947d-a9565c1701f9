const cds = require('../../../../cds')
const { isStandardError } = require('../../../../common/error/standardError')

const { StatusCodes: HttpStatusCodes } = require('../okra/odata-commons/http/HttpStatusCode')

const ERROR_TO_HTTP_CODE = {
  // copied from okra tests
  UriSemanticError: HttpStatusCodes.NOT_FOUND,
  UriQueryOptionSemanticError: HttpStatusCodes.BAD_REQUEST,
  UriSyntaxError: HttpStatusCodes.BAD_REQUEST,
  NotImplementedError: HttpStatusCodes.NOT_IMPLEMENTED,
  MethodNotAllowedError: HttpStatusCodes.METHOD_NOT_ALLOWED,
  NotAcceptableError: HttpStatusCodes.NOT_ACCEPTABLE,
  NotAuthorizedError: HttpStatusCodes.UNAUTHORIZED,
  PreconditionFailedError: HttpStatusCodes.PRECONDITION_FAILED,
  PreconditionRequiredError: HttpStatusCodes.PRECONDITION_REQUIRED,
  ConflictError: HttpStatusCodes.CONFLICT,
  NotFoundError: HttpStatusCodes.NOT_FOUND,
  BadRequestError: HttpStatusCodes.BAD_REQUEST,
  // custom
  DeserializationError: HttpStatusCodes.BAD_REQUEST
}

const { normalizeError } = require('../../../../common/error/frontend')

const _beautifyMessage = msg => (msg.endsWith('.') ? msg : msg + '.')

const _buildRootCauseMessage = (message, rootCause) => {
  if (rootCause) {
    message = _beautifyMessage(message) + ' ' + _beautifyMessage(rootCause.message)

    if (typeof rootCause.getRootCause === 'function') {
      message = _buildRootCauseMessage(message, rootCause.getRootCause())
    }
  }

  return message
}

const _betterOkraError = err => {
  const statusCode = ERROR_TO_HTTP_CODE[err.name]
  if (statusCode && !('code' in err)) err.code = String(statusCode)
  if (statusCode && !err.statusCode) err.statusCode = statusCode

  err.message = _buildRootCauseMessage(err.message, err.getRootCause())

  if (err.name === 'DeserializationError') {
    err.message = err.message.replace('Error while deserializing payload.', 'Deserialization Error:')
    err.message = err.message.replace(' An error occurred during deserialization of the entity.', '')

    // add parsed data to error for req.data
    let e = err
    let depth = 0
    while (e._rootCause && depth < 10) {
      const rootCause = e._rootCause
      if (rootCause._data) {
        err._data = rootCause._data
        break
      }
      e = rootCause
      depth++
    }
  } else if (err.name === 'SerializationError') {
    err.message = err.message.replace('An error occurred during serialization of the entity.', 'Serialization Error:')
    err.message = err.message.replace(
      'An error occurred during serialization of the entity collection. An error occurred during serialization of',
      'Serialization Error for'
    )
  } else if (err.name === 'PreconditionFailedError') {
    // REVISIT use cds.error here once it is official API?
    const { error } = normalizeError(new cds.Request().error(412, '412'), { headers: {}, query: {} })
    return error
  }

  return err
}

/**
 * Custom error handler.
 * Crashes the node instance, if not deactivated.
 *
 * @param {boolean} crashOnError
 * @returns {Function}
 */
const getErrorHandler = (crashOnError = true, srv) => {
  // eslint-disable-next-line complexity
  return async (odataReq, odataRes, next, err) => {
    // REVISIT: crashOnError
    if (isStandardError(err) && crashOnError) {
      // first rollback in case of atomicity groups
      const changeset = odataReq.getAtomicityGroupId()
      if (changeset) {
        const tx = odataReq.getBatchApplicationData().txs[changeset]
        await tx.rollback().catch(() => {})
      }

      throw err
    }

    // get req for i18n
    let req
    const isBatch = odataReq.getBatchApplicationData() !== null
    if (isBatch) {
      req = odataReq.getBatchApplicationData().req
    } else {
      req = odataReq.getIncomingRequest()
    }

    if (typeof err.getRootCause === 'function') {
      // > an OKRA error
      err = _betterOkraError(err)
      // add req.data for use in custom error handler in case of okra deserialization error
      if ('_data' in err && !('data' in req)) req.data = err._data
    }

    // REVISIT: invoking service.on('error') handlers needs a cleanup with new protocol adapters!!!
    // invoke srv.on('error', function (err, req) { ... }) here in special situations
    // REVISIT: if for compat reasons, remove once cds^5.1
    if (srv._handlers._error.length) {
      // REVISIT: move to error middleware
      let ctx = cds.context
      if (!ctx) {
        // FIXME: this implementation only worked because Okra's BufferedWriter.on('finish')
        // lost cds.context -> as we fixed that we don't get into this if branch anymore,
        // but then the ctx in the else branch below isn't the ODataRequest anymore
        // > error before req was dispatched
        const creq = new cds.Request({ req, res: req.res, user: req.user || new cds.User.default() })
        for (const each of srv._handlers._error) each.handler.call(srv, err, creq)
      } else if (ctx._tx?._done !== 'rolled back') {
        // > error after req was dispatched, e.g., serialization error in okra
        const creq = /* odataReq.req || */ new cds.Request({ req, res: req.res, user: ctx.user, tenant: ctx.tenant })
        for (const each of srv._handlers._error) each.handler.call(srv, err, creq)
      }
    }

    // add content id if not generated by okra ("~...")
    const contentId = odataReq.getOdataRequestId()
    if (contentId && !contentId.match(/^~/)) {
      err['@Core.ContentID'] = contentId
      // UI5 expects us to add the content ID to each detail message
      if (err.details) {
        err.details.forEach(entry => {
          entry['@Core.ContentID'] = contentId
        })
      }
    }

    const { error, statusCode } = normalizeError(err, req)
    // REVISIT: We should also pass stack traces in development
    // if (!cds.env.production) error.stack = err.stack

    if (cds.env.fiori.wrap_multiple_errors === false) {
      // According to the Fiori Elements Failed Message specification, the format must be:
      // Root level: First error, Details: Other errors
      if (error.details) {
        const [firstDetail, ...restDetails] = error.details
        Object.assign(error, firstDetail)
        if (restDetails.length) error.details = restDetails
        else delete error.details
      }
    }

    next(null, Object.assign(error, { statusCode }))
  }
}

module.exports = getErrorHandler
