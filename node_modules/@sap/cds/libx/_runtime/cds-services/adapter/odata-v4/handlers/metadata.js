const cds = require('../../../../cds')
const LOG = cds.log('odata')

const { toODataResult } = require('../utils/result')
const { normalizeError } = require('../../../../common/error/frontend')
const getError = require('../../../../common/error')

const mpSupportsEmptyLocale = () => {
  const pkg = require(require.resolve('@sap/cds-mtxs/package.json'))
  const [major, minor] = pkg.version.split('.').map(Number)
  return major > 1 || (major === 1 && minor >= 12)
}

/**
 * Provide localized metadata handler.
 *
 * @param {object} service
 * @returns {Function}
 */
const metadata = service => {
  return async (odataReq, odataRes, next) => {
    const req = odataReq.getIncomingRequest()
    const tenant = req.tenant ?? req.user?.tenant
    // REVISIT: can we take locale from user, or is there some odata special wrt metadata?
    const locale = odataRes.getContract().getLocale()

    try {
      const { 'cds.xt.ModelProviderService': mps } = cds.services
      let edmx
      if (mps) {
        // REVISIT: remove check later
        if (mpSupportsEmptyLocale()) {
          // If no extensibility nor fts, do not provide model to mtxs
          const modelNeeded = cds.env.requires.extensibility || cds.context.features?.given
          edmx = await mps.getEdmx({ tenant, model: modelNeeded && service.model, service: service.definition.name })
          const extBundle = cds.env.requires.extensibility && (await mps.getI18n({ tenant, locale }))
          edmx = cds.localize(service.model, locale, edmx, extBundle)
        } else {
          edmx = await mps.getEdmx({ tenant, model: service.model, service: service.definition.name, locale })
        }
      } else {
        edmx = await cds.compile.to.edmx.files.get(service)
        edmx ??= cds.compile.to.edmx(service.model, { service: service.definition.name })
        edmx = cds.localize(service.model, locale, edmx)
      }

      return next(null, toODataResult(edmx))
    } catch (e) {
      if (LOG._error) {
        e.message = 'Unable to get EDMX for tenant ' + tenant + ' due to error: ' + e.message
        LOG.error(e)
      }
      // return 503 to client
      const err = getError(Object.assign(e, { statusCode: 503 }))
      const { error, statusCode } = normalizeError(err, req)
      return next(Object.assign(error, { statusCode }))
    }
  }
}

module.exports = metadata
