const cds = require('../../../../cds')
const LOG = cds.log('odata')
const ODataRequest = require('../ODataRequest')

const {
  QueryOptions,
  Components: { DATA_READ_HANDLER },
  uri: {
    UriResource: {
      ResourceKind: { BOUND_FUNCTION, COUNT, FUNCTION_IMPORT, NAVIGATION_TO_ONE, VALUE, SINGLETON, PRIMITIVE_PROPERTY }
    }
  }
} = require('../okra/odata-server')
const FUNCTION = { [BOUND_FUNCTION]: 1, [FUNCTION_IMPORT]: 1 }

const { isCustomOperation } = require('../utils/request')
const { getActionOrFunctionReturnType } = require('../utils/handlerUtils')
const { validateResourcePath } = require('../utils/request')
const { toODataResult, postProcess, setStreamingHeaders } = require('../utils/result')
const { isStreaming } = require('../utils/stream')
const { resolveStructuredName } = require('../utils/handlerUtils')
const getError = require('../../../../common/error')
const { getSapMessages } = require('../../../../common/error/frontend')
const { getPageSize } = require('../../../../common/generic/paging')
const { getTransition } = require('../../../../common/utils/resolveView')
const { Readable } = require('stream')

/**
 * Checks whether a bound function or function import is invoked.
 *
 * @param {Array} segments - The uri path segments of the request.
 * @returns {boolean} - True if a function is invoked, else false.
 * @private
 */
const _isFunction = segments => segments[segments.length - 1].getKind() in FUNCTION

/**
 * Invoke a function.
 *
 * @param {object} tx
 * @param {object} req
 * @param odataReq
 * @returns {Promise}
 * @private
 */
const _invokeFunction = async (tx, req) => {
  let result = await tx.dispatch(req)

  return toODataResult(result, req)
}

/**
 * Checks whether a count of entities is requested
 * (not count embedded into collection).
 *
 * @param {Array} segments - The uri path segments of the request.
 * @returns {boolean} - True if a count of entities is requested, else false.
 * @private
 */
const _isCount = segments => segments[segments.length - 1].getKind() === COUNT

/**
 * Get the count by using the general READ CQN and alter it to a COUNT query.
 *
 * @param {object} tx
 * @param {object} readReq
 * @returns {Promise}
 * @private
 */
const _getCount = async (tx, readReq) => {
  // REVISIT once new parser is out of beta
  // New parser directly removes unapplicable query options from query, no copying of cqn needed
  if (cds.env.features.odata_new_parser) {
    // todo check limit
    const result = await tx.dispatch(readReq)

    const count = Array.isArray(result)
      ? result.reduce((acc, val) => {
          return acc + ((val && (val.$count || val._counted_)) || (val[0] && (val[0].$count || val[0]._counted_))) || 0
        }, 0)
      : result.$count || result._counted_ || 0
    // Transform into scalar result
    return toODataResult(count)
  } else {
    // REVISIT: this process appears to be rather clumsy
    // Copy CQN including from, where and search + changing columns
    const select = SELECT.from(readReq.query.SELECT.from)
    // { val: 1 } is used on purpose, as "numbers" are not used as param in prepared stmt
    select.SELECT.columns = [{ func: 'count', args: [{ val: 1 }], as: '$count' }]

    if (readReq.query.SELECT.where) select.SELECT.where = readReq.query.SELECT.where
    if (readReq.query.SELECT.search) select.SELECT.search = readReq.query.SELECT.search
    const req = readReq

    // preserve _target
    select._target = req.query._target

    // remove as Object.defineProperty would cause a conflict
    delete req.query

    // Define new CQN
    req.query = select
    // todo check limit
    const result = await tx.dispatch(req)

    const count = result.$count || result._counted_ || (result[0] && (result[0].$count || result[0]._counted_)) || 0

    // Transform into scalar result
    return toODataResult(count, req)
  }
}

/**
 * Checks whether a collection of entities or a single entity is requested.
 * Returns false in case of a custom operation.
 *
 * @param segments
 * @returns {boolean} - True if a collection of entities is requested, else false.
 * @private
 */
const _isCollection = segments => {
  const lastEntitySegment = Array.from(segments)
    .reverse()
    .find(segment => segment.getProperty() === null)
  const kind = lastEntitySegment.getKind()

  return (
    !isCustomOperation(segments) &&
    kind !== NAVIGATION_TO_ONE &&
    kind !== COUNT &&
    kind !== VALUE &&
    kind !== SINGLETON &&
    lastEntitySegment.getKeyPredicates().length === 0
  )
}

/**
 * Checks whether single entity via navigation-to-one is requested.
 *
 * @param segments
 * @returns {boolean}
 * @private
 */
const _isNavigationToOne = segments =>
  segments[segments.length - 1].getKind() === NAVIGATION_TO_ONE &&
  segments[segments.length - 1].getKeyPredicates().length === 0

const _getResult = (nameArr, result) => {
  if (nameArr.length === 0) return result
  return _getResult(nameArr.slice(1), result[nameArr[0]])
}

const validateIfNoneMatch = (req, result) => {
  if (req.target._etag && req.headers['if-none-match']) {
    let header = req.headers['if-none-match']
    if (header.startsWith('W/')) header = header.substring(2)
    if (header.startsWith('"') && header.endsWith('"')) header = header.substring(1, header.length - 1)
    if (header === '*') return true
    if (result[req.target._etag.name] === header) return true
  }
}

/**
 * Reads the entire entity or only property of it is alike.
 *
 * In case of an entity, odata-v4 wants the value an object structure, in case of a property as scalar.
 *
 * @param {import('../../../../common/Service')} tx
 * @param {import('../../../../cds-services/adapter/odata-v4/ODataRequest')} req
 * @param {Array<import('../okra/odata-commons/uri/UriResource')>} segments
 * @returns {Promise}
 * @private
 */
const _readEntityOrProperty = async (tx, req, segments) => {
  let result = await tx.dispatch(req)

  /*
   * OData spec:
   * - Requesting Individual Entities:
   *     If no entity exists with the key values specified in the request URL, the service responds with 404 Not Found.
   * - Requesting Individual Properties:
   *     If the property is single-valued and has the null value, the service responds with 204 No Content.
   *     If the property is not available, for example due to permissions, the service responds with 404 Not Found.
   * - Requesting Related Entities (to one):
   *     If no entity is related, the service returns 204 No Content.
   */
  if (result == null) {
    if (_isNavigationToOne(segments)) return toODataResult(null)
    throw getError(404)
  }

  if (validateIfNoneMatch(req, result)) {
    req._.odataRes.setStatusCode(304)
    return
  }

  if (!Array.isArray(result)) result = [result]

  if (result.length === 0 && _isNavigationToOne(segments)) return toODataResult(null)

  // Reading one entity or a property of it should yield only a result length of one.
  if (result.length !== 1) throw getError(404)

  const index = segments[segments.length - 1].getKind() === VALUE ? 2 : 1
  const propertyElement = segments[segments.length - index].getProperty()

  if (propertyElement === null) {
    return toODataResult(result[0], req)
  }

  result = _getResult(resolveStructuredName(segments, segments.length - 2), result[0])

  if (req.target._etag) result.$etag = result[req.target._etag.name]

  const odataResult = toODataResult(result, req)

  // property is read via a to one association and last segment is not $value
  if (index !== 2 && segments.length > 2 && segments[segments.length - 2].getKind() === NAVIGATION_TO_ONE) {
    // find keys in result
    const keys = Object.keys(result)
      .filter(k => segments[segments.length - index - 1].getEdmType().getOwnKeyPropertyRefs().has(k))
      .reduce((res, curr) => {
        res[curr] = result[curr]
        return res
      }, {})

    // prepare key map for Okra
    odataResult.keysForParam = new Map().set(segments[segments.length - index - 1], keys)
  }

  return odataResult
}

const _reliablePagingPossible = req => {
  if (req.target._isDraftEnabled) return false
  if (cds.context?.http.req.query.$apply) return false
  if (req.query.SELECT.limit.offset?.val ?? req.query.SELECT.limit.offset > 0) return false
  if (req.query.SELECT.orderBy?.some(o => !o.ref)) return false
  return (
    !req.query.SELECT.columns ||
    req.query.SELECT.columns.some(c => c === '*' || c.ref?.[0] === '*') ||
    req.query.SELECT.orderBy?.every(o => req.query.SELECT.columns?.some(c => o.ref[0] === c.ref?.[0]))
  )
}

/**
 * Read an entity collection without including the count of the total amount of entities.
 *
 * @param {object} tx
 * @param {object} req
 * @param odataReq
 * @returns {Promise}
 * @private
 */
// eslint-disable-next-line complexity
const _readCollection = async (tx, req, odataReq) => {
  const result = (await tx.dispatch(req)) || []

  if (req.http?.req?.headers?.accept?.match(/application\/pdf/)) {
    if (!result.$mediaContentType) result.$mediaContentType = 'application/pdf'
    setStreamingHeaders(result, req)
    return result
  }

  if (Array.isArray(req.query)) {
    const adjustedResult = []
    if (req.query[0].SELECT.count) adjustedResult.$count = 0
    adjustedResult.push(...result[0])
    adjustedResult.$count += result[0].$count ? result[0].$count : 0
    for (let i = 1; i < result.length; i++) {
      adjustedResult.push(...result[i])
      adjustedResult.$count += result[i].$count ? result[i].$count : 0
      //Add OData context, if it deviates from main context
      if (req._metaInfo.contextUrl !== req._metaInfo.additionalContextUrl[i - 1])
        result[i].forEach(entry => (entry['*@odata.context'] = req._metaInfo.additionalContextUrl[i - 1]))
    }
    result.splice(0, result.length, ...adjustedResult)
    if (req.query[0].SELECT.count) result.$count = adjustedResult.$count || 0
  } else if (req.query.SELECT.count && !('$count' in result)) result.$count = 0

  const limit = Array.isArray(req.query) ? getPageSize(req.query[0]._target).max : req.query.SELECT.limit?.rows?.val
  const top = odataReq.getUriInfo().getQueryOption(QueryOptions.TOP) || parseInt(odataReq._queryOptions?.$top)

  if (limit && limit === result.length && limit !== top && !('$nextLink' in result)) {
    const token = odataReq.getUriInfo().getQueryOption(QueryOptions.SKIPTOKEN) || odataReq._queryOptions?.$skiptoken
    if (cds.env.query.limit.reliablePaging && _reliablePagingPossible(req)) {
      const decoded = token && JSON.parse(Buffer.from(token, 'base64').toString())
      const skipToken = {
        r: (decoded?.r || 0) + limit,
        c: req.query.SELECT.orderBy.map(o => ({
          a: o.sort ? o.sort === 'asc' : true,
          k: o.ref[0],
          v: result[result.length - 1][o.ref[0]]
        }))
      }

      if (limit + (decoded?.r || 0) !== top) {
        result.$nextLink = Buffer.from(JSON.stringify(skipToken)).toString('base64')
      }
    } else {
      result.$nextLink = (token ? parseInt(token) : 0) + limit
    }
  }

  return toODataResult(result, req)
}

const _resolveContentProperty = (req, annotName, resolvedProp) => {
  if (req.target.elements[resolvedProp]) {
    return resolvedProp
  } else {
    LOG._warn &&
      LOG.warn(
        `"${annotName}" in entity "${req.target.name}" points to property "${resolvedProp}" which was renamed or is not part of the projection. You must update the annotation value.`
      )
    const mapping = getTransition(req.target, cds.db).mapping
    const key = [...mapping.entries()].find(({ 1: val }) => val.ref[0] === resolvedProp)
    return key?.length && key[0]
  }
}

const _addMetadataProperty = (req, property, annotName, odataName) => {
  if (typeof property[annotName] === 'object') {
    const contentProperty = _resolveContentProperty(req, annotName, property[annotName]['='].replaceAll(/\./g, '_'))
    req.target.elements[contentProperty]
      ? req.query.SELECT.columns.push({ ref: [contentProperty], as: odataName })
      : LOG._warn &&
        LOG.warn(`"${annotName.split('.')[1]}" ${contentProperty} not found in entity "${req.target.name}".`)
  } else {
    req.query.SELECT.columns.push({ val: property[annotName], as: odataName })
  }
}

const _addStreamMetadata = req => {
  // new odata parser sets streaming property in SELECT.from
  const ref = req.query.SELECT.columns?.[0].ref || req.query.SELECT.from.ref
  const propertyName = ref[ref.length - 1]
  let mediaTypeProperty
  for (let key in req.target.elements) {
    const val = req.target.elements[key]
    if (val['@Core.MediaType'] && val.name === propertyName) {
      mediaTypeProperty = val
      break
    }
  }

  _addMetadataProperty(req, mediaTypeProperty, '@Core.MediaType', '$mediaContentType')

  if (mediaTypeProperty['@Core.ContentDisposition.Filename']) {
    _addMetadataProperty(
      req,
      mediaTypeProperty,
      '@Core.ContentDisposition.Filename',
      '$mediaContentDispositionFilename'
    )
  }

  if (mediaTypeProperty['@Core.ContentDisposition.Type']) {
    req.query.SELECT.columns.push({
      val: mediaTypeProperty['@Core.ContentDisposition.Type'],
      as: '$mediaContentDispositionType'
    })
  }
}

/**
 * Reading the full entity or only a property of it is alike.
 * In case of an entity, odata-v4 wants the value an object structure,
 * in case of a property as scalar.
 *
 * @param {object} tx
 * @param {object} req
 * @returns {Promise}
 * @private
 */
const _readStream = async (tx, req) => {
  if (cds.env.features.stream_compat) req.query._streaming = true
  else if (!req.target['@cds.persistence.skip']) _addStreamMetadata(req)

  let result = await tx.dispatch(req)

  // REVISIT: compat, should actually be treated as object
  if (!Array.isArray(result)) result = [result]

  // Reading one entity or a property of it should yield only a result length of one.
  if (result.length === 0 || result[0] === undefined) {
    throw getError(404)
  }

  if (result.length > 1) throw getError(400)

  if (result[0] === null) return null

  result = result[0]

  if (validateIfNoneMatch(req, result)) {
    req._.odataRes.setStatusCode(304)
    return
  }

  const readable = cds.env.features.stream_compat
    ? result.value
    : Object.values(result).find(v => v instanceof Readable)
  if (readable) {
    readable.on('error', () => {
      readable.removeAllListeners('error')
      // readable.destroy() does not end stream in node 10 and 12
      readable.push(null)
    })
  }

  const headers = req._.odataReq.getHeaders()
  const contentType = result.$mediaContentType

  if (
    contentType &&
    headers &&
    headers.accept &&
    !headers.accept.includes('*/*') &&
    !headers.accept.includes(contentType) &&
    !headers.accept.includes(contentType.split('/')[0] + '/*')
  ) {
    req.reject(406, `Content type "${contentType}" not listed in accept header "${headers.accept}".`)
  }

  return toODataResult(result, req)
}

const _readSingleton = async (tx, req, lastSegment) => {
  let result = await tx.dispatch(req)
  if (result == null && !req.target['@odata.singleton.nullable']) throw getError(404)

  if (lastSegment.getKind() === PRIMITIVE_PROPERTY) {
    result = result[lastSegment.getProperty().getName()]
  }

  return toODataResult(result, req)
}

/**
 * Depending on the read request segments, create one ore more reading service request.
 *
 * @param {object} tx
 * @param {object} req
 * @param odataReq
 * @returns {Promise}
 * @private
 */
const _readAndTransform = (tx, req, odataReq) => {
  const segments = odataReq.getUriInfo().getPathSegments()

  if (_isFunction(segments)) {
    return _invokeFunction(tx, req, odataReq)
  }

  // Scalar count is requested
  if (_isCount(segments)) {
    return _getCount(tx, req)
  }

  if (_isCollection(segments)) {
    return _readCollection(tx, req, odataReq)
  }

  // REVISIT: move to afterburner
  if (segments[segments.length - 1]._isStreamByDollarValue) {
    for (const k in req.target.elements) {
      if (req.target.elements[k]['@Core.MediaType']) {
        req.query.SELECT.columns = [{ ref: [k] }]
        break
      }
    }

    return _readStream(tx, req)
  }

  if (isStreaming(segments)) {
    return _readStream(tx, req)
  }

  if (req.target._isSingleton) {
    return _readSingleton(tx, req, segments[segments.length - 1])
  }

  return _readEntityOrProperty(tx, req, segments)
}

const _postProcess = (odataReq, req, odataRes, service, result) => {
  const functionReturnType = getActionOrFunctionReturnType(
    odataReq.getUriInfo().getPathSegments(),
    service.model.definitions
  )
  const _req = Object.assign(req, { target: functionReturnType || req.target })
  postProcess(_req, odataRes, service, result)
}

const _removeKeysForParams = result => {
  let options

  if (result.keysForParam) {
    options = { keys: result.keysForParam }
    delete result.keysForParam
  }

  return options
}

const _ensureStream = result => {
  // temp workaround for url streaming
  const stream_ = new Readable()
  stream_.push(result.value)
  stream_.push(null)
  result.value = stream_
}

/**
 * The handler that will be registered with odata-v4.
 *
 * If an entity collection is read, it calls next with result as an Array with all entities of the collection.
 * If a count of the entities in the collection is requested, it uses number of the entities as a Number value.
 * If an single entity is read, it uses the entity as an object.
 * If a property of a single entity is requested (e.g. /Books(1)/name), it unwraps the property from the result.
 * If the single entity to be read does not exist, calls next with error to return a 404.
 * In all other failure cases it calls next with error to return a 500.
 *
 * @param {import('../../../../common/Service')} service
 * @returns {function}
 */
const read = service => {
  return async (odataReq, odataRes, next) => {
    let req

    try {
      validateResourcePath(odataReq, service)
      req = new ODataRequest(DATA_READ_HANDLER, service, odataReq, odataRes)
    } catch (e) {
      return next(e)
    }

    const changeset = odataReq.getAtomicityGroupId()
    const tx = changeset ? odataReq.getBatchApplicationData().txs[changeset] : service.tx(req)
    // REVISIT: can be removed when pluggable middlewares are active
    cds.context = tx

    let result, err
    let additional = {}

    try {
      // REVISIT: refactor _readAndTransform
      result = await _readAndTransform(tx, req, odataReq)

      if (result == null) {
        result = { value: null }
      } else {
        _postProcess(odataReq, req, odataRes, service, result)
        additional = _removeKeysForParams(result)
      }

      if (changeset) {
        // for passing into commit
        odataReq.getBatchApplicationData().results[changeset].push({ result, req })
      } else if (result.value && isStreaming(odataReq.getUriInfo().getPathSegments())) {
        if (odataRes._response.destroyed) {
          err = new Error('Response is closed while streaming')
          tx.rollback(err).catch(() => {})
        } else {
          // REVISIT: temp workaround for url streaming
          if (!(result.value instanceof Readable)) _ensureStream(result)
          result.value.on('end', () => tx.commit(result).catch(() => {}))
          result.value.once('error', err => tx.rollback(err).catch(() => {}))
          let finished = false
          odataRes._response.on('finish', () => {
            finished = true
          })
          odataRes._response.on('close', () => {
            if (!finished) {
              err = new Error('Response is closed while streaming')
              tx.rollback(err).catch(() => {})
            }
          })
        }
      } else {
        await tx.commit(result)
      }
    } catch (e) {
      err = e

      if (!changeset) {
        // REVISIT: rollback needed if an error occurred before commit attempted -> how to distinguish?
        await tx.rollback(e).catch(() => {})
      }
    } finally {
      req.messages?.length && odataRes.setHeader('sap-messages', getSapMessages(req.messages, req.http.req))

      if (err) next(err)
      else next(null, result, additional)
    }
  }
}

module.exports = read
