const cds = require('../../../../cds')

const { containsAnyRestrictions, getAccessRestrictions } = require('../../../../common/utils/restrictions')
const { ODATA_UNAUTHORIZED, ODATA_FORBIDDEN } = require('../../../../common/error/constants')

module.exports = srv => {
  const containsRestrictions = containsAnyRestrictions(srv)
  const accessRestrictions = getAccessRestrictions(srv)

  // eslint-disable-next-line complexity
  return function ODataRequestHandler(odataReq, odataRes, next) {
    const req = odataReq.getBatchApplicationData()
      ? odataReq.getBatchApplicationData().req
      : odataReq.getIncomingRequest()

    // ensure there always is a user going forward (not always the case with old or custom auth)
    if (!req.user) req.user = new cds.User.default()

    const { res, user, path, headers } = req

    const { protectMetadata } = cds.env.odata
    if (protectMetadata === false && (path === '/' || path.endsWith('/$metadata'))) {
      // > nothing to do
      return next()
    }

    // in case of $batch we need to challenge directly, as the header is not processed if in $batch response body
    if (containsRestrictions && path.endsWith('/$batch') && req.user._is_anonymous) {
      // NOTE: "return req._login()" would not invoke custom error handlers
      if (req._login) res.set('www-authenticate', `Basic realm="Users"`)
      else if (user._challenges) res.set('www-authenticate', user._challenges.join(';'))
      return next(ODATA_UNAUTHORIZED)
    }

    // check @restrict and @requires as soon as possible (DoS)
    if (!accessRestrictions.some(r => user.is(r))) {
      // > unauthorized or forbidden?
      if (req.user._is_anonymous) {
        // NOTE: "return req._login()" would not invoke custom error handlers
        if (req._login) res.set('www-authenticate', `Basic realm="Users"`)
        else if (user._challenges) res.set('www-authenticate', user._challenges.join(';'))
        return next(ODATA_UNAUTHORIZED)
      }
      return next(ODATA_FORBIDDEN)
    }

    /*
     * .on('request') is the only possibility to set a shared object,
     * that can be used in ATOMICITY_GROUP_START and ATOMICITY_GROUP_END
     */
    if (path.endsWith('/$batch')) {
      // ensure content type
      const ct = headers['content-type'] || ''
      if (!ct.match(/multipart\/mixed/) && !ct.match(/application\/json/)) {
        return next({
          statusCode: 400,
          code: '400',
          message: 'Batch requests must have content type multipart/mixed or application/json'
        })
      }

      odataReq.setApplicationData({ req, res })
    }

    next()
  }
}
