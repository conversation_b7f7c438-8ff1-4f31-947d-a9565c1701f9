const cds = require('../../../../cds')
const { SELECT, UPDATE } = cds.ql
const ODataRequest = require('../ODataRequest')

const {
  Components: { DATA_UPDATE_HANDLER, DATA_CREATE_HANDLER }
} = require('../okra/odata-server')

const { validateResourcePath } = require('../utils/request')
const { isReturnMinimal } = require('../utils/handlerUtils')
const { readAfterWrite } = require('../utils/readAfterWrite')
const { toODataResult, postProcess, postProcessMinimal } = require('../utils/result')
const { hasOmitValuesPreference } = require('../utils/omitValues')

const { getSapMessages } = require('../../../../common/error/frontend')

const _isUpsertAllowed = req => {
  return (
    !(cds.env.runtime && cds.env.runtime.allow_upsert === false) &&
    !(
      req.target &&
      req.target._isDraftEnabled &&
      (!cds.env.fiori.lean_draft || (!req.data.IsActiveEntity && req.event === 'PATCH'))
    )
  )
}

const _infoForeignKeyInParent = (req, tx) => {
  const info = {}
  // keys not in data
  if (req.target.keys && Object.keys(req.target.keys).some(key => key in req.data)) {
    return info
  }

  const nav = req.query.UPDATE.entity.ref && req.query.UPDATE.entity.ref.length !== 0 && req.query.UPDATE.entity.ref[1]
  const parent = req.query.UPDATE.entity.ref && req.query.UPDATE.entity.ref[0].id

  // not a navigation
  if (!parent || !nav) {
    return info
  }

  const navID = typeof nav === 'string' ? nav : nav.id
  const navElement = tx.model.definitions[parent].elements[navID]

  // not a containment
  if (!navElement._isContained) {
    return info
  }

  const where = req.query.UPDATE.entity.ref[0].where
  return { parent, navElement, where }
}

const _create = async (req, odataReq, odataRes, tx) => {
  let result

  const { parent, navElement, where } = _infoForeignKeyInParent(req, tx)
  if (parent && navElement && where) {
    const onKeys = navElement._foreignKeys
    const parentKeys = onKeys.filter(key => key.parentElement).map(key => key.parentElement.name)
    const parentKeyObj = await tx.run(SELECT.from(parent).columns(parentKeys).where(where))

    const parentUpdateObj = {}
    onKeys.forEach(key => {
      let parentKeyVal, parentUpdateRequired
      if (parentKeyObj.length !== 0 && parentKeyObj[0][key.parentElement.name] !== null) {
        parentKeyVal = parentKeyObj[0][key.parentElement.name]
      } else if (key.childElement.isUUID && key.childElement.key) {
        parentUpdateRequired = true
        parentKeyVal = cds.utils.uuid()
      } else {
        throw new Error('Only keys of type UUID can be generated: ' + key.childFieldName)
      }
      odataReq.getBody()[key.childElement.name] = parentKeyVal

      if (parentUpdateRequired) {
        parentUpdateObj[key.parentElement.name] = parentKeyVal
      }
    })

    odataRes.setStatusCode(201, { overwrite: true })
    req = new ODataRequest(DATA_CREATE_HANDLER, tx, odataReq, odataRes, true)
    result = await tx.dispatch(req)

    if (Object.keys(parentUpdateObj).length !== 0) {
      await tx.run(UPDATE(parent).set(parentUpdateObj).where(where))
    }
  } else {
    req = new ODataRequest(DATA_CREATE_HANDLER, tx, odataReq, odataRes, true)
    result = await tx.dispatch(req)
  }

  return [result, req]
}

const _updateThenCreate = async (req, odataReq, odataRes, tx) => {
  let result

  try {
    result = await tx.dispatch(req)
  } catch (e) {
    const is404 = e.code === 404 || e.status === 404 || e.statusCode === 404
    const isForcedInsert =
      (e.code === 412 || e.status === 412 || e.statusCode === 412) && req.headers['if-none-match'] === '*'
    if ((is404 || isForcedInsert) && _isUpsertAllowed(req)) {
      // PUT/ PATCH with if-match header means "only if already exists", i.e., no insert if not
      if (req.headers['if-match']) {
        throw Object.assign(new Error('412'), { statusCode: 412 })
      }
      // REVISIT: remove error (and child?) from tx.context? -> would require a unique req.id
      ;[result, req] = await _create(req, odataReq, odataRes, tx)
      odataRes.setStatusCode(201, { overwrite: true })
    } else {
      throw e
    }
  }

  return [result, req]
}

const _shouldReadPreviousResult = req =>
  req.event === 'UPDATE' &&
  !isReturnMinimal(req) &&
  (hasOmitValuesPreference(req.headers.prefer, 'defaults') || hasOmitValuesPreference(req.headers.prefer, 'nulls'))

const _hasEtag = target => {
  return target._etag
}

const _getStructValue = (prop, result, segments) => {
  const path = [prop]
  for (let i = segments.length - 2; i >= 0; i--) {
    const segment = segments[i]
    if (segment.getKind() === 'COMPLEX.PROPERTY') {
      path.unshift(segment.getProperty().getName())
    } else break
  }

  let res = result
  path.forEach(p => (res = res[p]))

  return { value: res }
}

/**
 * The handler that will be registered with odata-v4.
 *
 * In case of success it calls next with the number of updated entries as result.
 * In case of error it calls next with error.
 *
 * @param {import('../../../../common/Service')} service
 * @returns {function}
 */
const update = service => {
  return async (odataReq, odataRes, next) => {
    let req

    try {
      validateResourcePath(odataReq, service)
      req = new ODataRequest(DATA_UPDATE_HANDLER, service, odataReq, odataRes)
    } catch (e) {
      return next(e)
    }

    const changeset = odataReq.getAtomicityGroupId()
    const tx = changeset ? odataReq.getBatchApplicationData().txs[changeset] : service.tx(req)
    cds.context = tx

    // putting a property?
    const primitive = odataReq.getUriInfo().getLastSegment().getKind() === 'PRIMITIVE.PROPERTY'

    let result, err

    try {
      let previousResult

      if (_shouldReadPreviousResult(req)) {
        // this is not a read AFTER update, but BEFORE
        previousResult = await readAfterWrite(req, service, { isBefore: true })
      }

      // try UPDATE and, on 404 error, try CREATE
      ;[result, req] = await _updateThenCreate(req, odataReq, odataRes, tx)

      if (!(primitive && !_hasEtag(req.target)) && req._.readAfterWrite) {
        // REVISIT:
        // Performance: For `isReturnMinimal` it's enough to just read the etag.
        // Note: Without read access, one cannot return the etag.
        result = await readAfterWrite(req, service, { operation: { result } })
      }

      if (!isReturnMinimal(req)) {
        postProcess(req, odataRes, service, result, previousResult)
      } else {
        result = postProcessMinimal(req, service, result)
      }

      if (changeset) {
        // for passing into commit
        odataReq.getBatchApplicationData().results[changeset].push({ result, req })
      } else {
        await tx.commit(result)
      }

      if (result == null || isReturnMinimal(req)) {
        odataRes.setStatusCode(204, { overwrite: true })
      }
    } catch (e) {
      err = e

      if (!changeset) {
        // REVISIT: rollback needed if an error occurred before commit attempted -> how to distinguish?
        await tx.rollback(e).catch(() => {})
      }
    } finally {
      req.messages?.length && odataRes.setHeader('sap-messages', getSapMessages(req.messages, req.http.req))

      if (err) next(err)
      else if (primitive && result) {
        const prop = odataReq.getUriInfo().getLastSegment().getProperty().getName()
        const res = cds.env.effective.odata.structs
          ? _getStructValue(prop, result, odataReq.getUriInfo().getPathSegments())
          : { value: result[prop] }
        for (const k of Object.keys(result).filter(k => k.match(/^\*/))) res[k] = result[k]
        next(null, res)
      } else next(null, toODataResult(result, req))
    }
  }
}

module.exports = update
