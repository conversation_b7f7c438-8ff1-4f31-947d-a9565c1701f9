const cds = require('../../../../cds')

const odata = require('../okra/odata-server')
const ExpressionKind = odata.uri.Expression.ExpressionKind
const BinaryOperatorKind = odata.uri.BinaryExpression.OperatorKind
const UnaryOperatorKind = odata.uri.UnaryExpression.OperatorKind
const MethodKind = odata.uri.MethodExpression.MethodKind
const ResourceKind = odata.uri.UriResource.ResourceKind
const EdmPrimitiveTypeKind = odata.edm.EdmPrimitiveTypeKind
const { getFeatureNotSupportedError } = require('../../../util/errors')
const { getSegmentKeyValue } = require('./utils')
const normalizeTimestamp = require('../../../../common/utils/normalizeTimestamp')

const _binaryOperatorToCQN = new Map([
  [BinaryOperatorKind.EQ, '='],
  [BinaryOperatorKind.NE, '!='],
  [BinaryOperatorKind.GE, '>='],
  [BinaryOperatorKind.GT, '>'],
  [BinaryOperatorKind.LE, '<='],
  [BinaryOperatorKind.LT, '<']
])

const LAMBDA_EXPRESSION = { [ResourceKind.ANY_EXPRESSION]: 1, [ResourceKind.ALL_EXPRESSION]: 1 }

class ExpressionToCQN {
  constructor(entity, model, columns = []) {
    this._model = model
    this._entity = entity
    this._columns = columns
  }

  _convert(expression) {
    const type = expression.getType()
    const value = expression.getText()

    if (value === null) return { val: null }

    switch (type) {
      case EdmPrimitiveTypeKind.Boolean:
        return { val: value === true || value === 'true' }
      case EdmPrimitiveTypeKind.Byte:
      case EdmPrimitiveTypeKind.SByte:
      case EdmPrimitiveTypeKind.Int16:
      case EdmPrimitiveTypeKind.Int32:
        return { val: parseInt(value) }
      case EdmPrimitiveTypeKind.Int64:
        return { val: value.toString() }
      case EdmPrimitiveTypeKind.Decimal:
        return cds.env.features.compat_decimal ? { val: parseFloat(value) } : { val: value.toString() }
      case EdmPrimitiveTypeKind.Single:
      case EdmPrimitiveTypeKind.Double:
        return { val: parseFloat(value) }
      case EdmPrimitiveTypeKind.DateTimeOffset: {
        try {
          if (expression._cdsType === 'cds.DateTime')
            // cut off ms if cds.DateTime
            return { val: new Date(value).toISOString().replace(/\.\d\d\dZ$/, 'Z') }
          return { val: normalizeTimestamp(value) }
        } catch (e) {
          throw Object.assign(new Error(`The type Edm.DateTimeOffset is not compatible with "${value}"`), {
            status: 400
          })
        }
      }
      default:
        return { val: value }
    }
  }

  _lambda(pathSegments, operator) {
    // we don't care about the variable name
    if (pathSegments[0].getKind() === 'EXPRESSION.VARIABLE') pathSegments = pathSegments.slice(1)
    const nav =
      pathSegments.length > 2 ? pathSegments.slice(0, pathSegments.length - 2).map(this._segmentFromMember) : []
    const navName = this._segmentFromMember(pathSegments[pathSegments.length - 2])
    let condition = this._segmentFromMember(pathSegments[pathSegments.length - 1])

    // in case of functions, condition is an object
    if (condition && !Array.isArray(condition)) condition = [condition]

    if (pathSegments[pathSegments.length - 1].getKind() === 'ALL.EXPRESSION') {
      return [
        ...(operator === 'not' ? [] : ['not']),
        'exists',
        { ref: [...nav, { id: navName, where: ['not', { xpr: condition }] }] }
      ]
    }

    return [...(operator === 'not' ? ['not'] : []), 'exists', { ref: [...nav, { id: navName, where: condition }] }]
  }

  _segmentFromMember(segment) {
    switch (segment.getKind()) {
      case ResourceKind.PRIMITIVE_PROPERTY:
      case ResourceKind.COMPLEX_PROPERTY:
      case ResourceKind.PRIMITIVE_COLLECTION_PROPERTY:
      case ResourceKind.COMPLEX_COLLECTION_PROPERTY:
        return segment.getProperty().getName()
      case ResourceKind.NAVIGATION_TO_ONE:
      case ResourceKind.NAVIGATION_TO_MANY:
        return segment.getNavigationProperty().getName()
      case ResourceKind.ALL_EXPRESSION:
      case ResourceKind.ANY_EXPRESSION:
        return segment.getExpression() ? this.parse(segment.getExpression()) : undefined
      case ResourceKind.COUNT:
        return '$count'
      default:
        throw getFeatureNotSupportedError(`Segment kind "${segment.getKind()}" in $filter query option`)
    }
  }

  _getMemberRecursively(pathSegments) {
    const [segment, ...nextSegments] = pathSegments

    if (!segment) return []

    if (segment.getKind() === ResourceKind.NAVIGATION_TO_ONE) {
      const name = this._segmentFromMember(segment)
      const where =
        nextSegments &&
        nextSegments.length &&
        nextSegments[nextSegments.length - 1].getKind() === ResourceKind.COUNT &&
        segment.getKeyPredicates().reduce((prev, curr) => {
          if (prev.length > 0) prev.push('and')
          const { keyName, val } = getSegmentKeyValue(curr)
          const ref = keyName.includes('/') ? keyName.split('/') : [keyName]
          prev.push({ ref }, '=', { val })
          return prev
        }, [])

      return [where && where.length ? { id: name, where } : name, ...this._getMemberRecursively(nextSegments)]
    }

    if (segment.getKind() === ResourceKind.NAVIGATION_TO_MANY) {
      return [this._segmentFromMember(segment), ...this._getMemberRecursively(nextSegments)]
    }

    if (segment.getKind() === ResourceKind.EXPRESSION_VARIABLE) {
      return [...this._getMemberRecursively(nextSegments)]
    }

    if (segment.getKind() === ResourceKind.COUNT) {
      return [this._segmentFromMember(segment), ...this._getMemberRecursively(nextSegments)]
    }

    if (segment.getKind() === ResourceKind.COMPLEX_PROPERTY) {
      if (nextSegments.length) {
        return [this._segmentFromMember(segment), ...this._getMemberRecursively(nextSegments)]
      }

      return [this._segmentFromMember(segment)]
    }

    return [this._segmentFromMember(segment)]
  }

  _member(expression, operator) {
    const pathSegments = expression.getPathSegments()
    if (pathSegments.some(segment => segment.getKind() in LAMBDA_EXPRESSION)) {
      return this._lambda(pathSegments, operator)
    }

    const members = this._getMemberRecursively(pathSegments)
    for (const entry of this._columns) {
      // for having we need the func instead of alias / column name
      if (entry.func === members[0] || (entry.func && entry.as === members[0])) {
        return entry
      }
    }
    if (members.length > 1 && members[members.length - 1] === '$count') {
      return { func: 'count', args: [{ ref: members.slice(0, members.length - 1) }], as: '$count' }
    }

    return { ref: members }
  }

  _getParameters(expression) {
    return expression.getParameters().map(parameter => {
      return this.parse(parameter)
    })
  }

  _genericFn(methodName, args, operator) {
    if (methodName === 'contains') {
      // contains on collection?
      try {
        const ele = args.find(ele => ele.val)
        if (ele && ele.val.match(/^\["/)) {
          ele.list = JSON.parse(ele.val).map(ele => ({ val: ele }))
          delete ele.val
        }
      } catch (e) {
        // ignore
      }
    }
    return operator ? [operator, { func: `${methodName}`, args }] : { func: `${methodName}`, args }
  }

  /* eslint-disable complexity */
  /**
   * Evaluate an method expression, which in SQL would be 'column condition value'.
   * Can also be nested.
   *
   * @param {object} expression
   * @param {string} [operator] - Operator, that might be used to invert a method or similar
   * @throws Error - if method expression is not supported
   * @private
   * @returns {Array | object}
   */
  _method(expression, operator) {
    const parameters = this._getParameters(expression)
    const method = expression.getMethod()

    switch (method) {
      case MethodKind.NOW:
        return { val: new Date().toISOString() }
      case MethodKind.SUBSTRING:
      case MethodKind.CONTAINS:
      case MethodKind.ENDSWITH:
      case MethodKind.STARTSWITH:
      case MethodKind.INDEXOF:
      case MethodKind.TOUPPER:
      case MethodKind.TOLOWER:
      case MethodKind.DAY:
      case MethodKind.DATE:
      case MethodKind.TIME:
      case MethodKind.CEILING:
      case MethodKind.TRIM:
      case MethodKind.LENGTH:
      case MethodKind.CONCAT:
      case MethodKind.HOUR:
      case MethodKind.MINUTE:
      case MethodKind.SECOND:
      case MethodKind.MONTH:
      case MethodKind.YEAR:
      case MethodKind.FLOOR:
      case MethodKind.ROUND:
        return this._genericFn(method, parameters, operator)

      default:
        throw getFeatureNotSupportedError(`Method "${method}" in $filter or $orderby query options`)
    }
  }
  /* eslint-enable complexity */

  _ensureArr(something) {
    return Array.isArray(something) ? something : [something]
  }

  _compare(operator, left, right, unary) {
    return unary === 'not'
      ? [unary, { xpr: [left, _binaryOperatorToCQN.get(operator), right] }]
      : [left, _binaryOperatorToCQN.get(operator), right]
  }

  _binary(expression, unary) {
    const operator = expression.getOperator()
    const left = this.parse(expression.getLeftOperand())

    // add cds type to right operand for use in _convert
    if (left.ref && left.ref.length === 1 && this._entity && this._entity.elements[left.ref[0]]) {
      expression.getRightOperand()._cdsType = this._entity.elements[left.ref[0]]._type
    }

    const right = this.parse(expression.getRightOperand())

    switch (operator) {
      case BinaryOperatorKind.AND:
        return unary === 'not'
          ? [unary, { xpr: [...this._ensureArr(left), 'and', ...this._ensureArr(right)] }]
          : [...this._ensureArr(left), 'and', ...this._ensureArr(right)]

      case BinaryOperatorKind.OR:
        return [
          ...(unary === 'not' ? [unary] : []),
          { xpr: [...this._ensureArr(left), 'or', ...this._ensureArr(right)] }
        ]

      case BinaryOperatorKind.NE:
      case BinaryOperatorKind.EQ:
      case BinaryOperatorKind.GE:
      case BinaryOperatorKind.GT:
      case BinaryOperatorKind.LE:
      case BinaryOperatorKind.LT:
        return this._compare(operator, left, right, unary)

      default:
        throw getFeatureNotSupportedError(`Binary operator "${expression.getOperator()}" in $filter query option`)
    }
  }

  _unary(expression) {
    if (expression.getOperator() !== UnaryOperatorKind.NOT) {
      throw getFeatureNotSupportedError(`Unary operator "${expression.getOperator()}" in $filter query option`)
    }

    return this.parse(expression.getOperand(), UnaryOperatorKind.NOT)
  }

  /**
   * Convert a odata-v4 filter expression object into a CQN object.
   *
   * @param {Expression} expression - odata filter expression
   * @param {string} [operator] - Operator, that might be used to invert a method or similar
   * @throws Error - if expression object is not supported
   * @returns {Array | object}
   */
  parse(expression, operator) {
    switch (expression.getKind()) {
      case ExpressionKind.ALIAS:
        return this.parse(expression.getExpression())
      case ExpressionKind.BINARY:
        operator = operator || expression.getOperator()
        return this._binary(expression, operator)
      case ExpressionKind.LITERAL:
        return this._convert(expression)
      case ExpressionKind.MEMBER:
        return this._member(expression, operator)
      case ExpressionKind.METHOD:
        return this._method(expression, operator)
      case ExpressionKind.UNARY:
        return this._unary(expression)
      default:
        throw getFeatureNotSupportedError(`Expression "${expression.getKind()}" in $filter or $orderby query options`)
    }
  }
}

module.exports = ExpressionToCQN
