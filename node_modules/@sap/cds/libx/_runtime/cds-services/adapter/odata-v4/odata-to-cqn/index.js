const cds = require('../../../../cds')

const {
  Components: { DATA_CREATE_HANDLER, DATA_DELETE_HANDLER, DATA_READ_HANDLER, DATA_UPDATE_HANDLER }
} = require('../okra/odata-server')

const boundToCQN = require('./boundToCQN')
const readToCQN = require('./readToCQN')
const updateToCQN = require('./updateToCQN')
const createToCQN = require('./createToCQN')
const deleteToCQN = require('./deleteToCQN')

const parseToCqn = (component, service, target, data, odataReq, upsert) => {
  let query = cds.odata.parse(odataReq.getIncomingRequest().url, { service })

  // for concat
  if (component === 'READ' && Array.isArray(query)) return query

  const _target = query.SELECT && query.SELECT.from

  const {
    SELECT: { one }
  } = query

  switch (component) {
    case 'CREATE':
      // create
      // error in cases like `POST Books(1)` i.e. `POST` with navigation to single entity
      if (one && !upsert) cds.error('POST not allowed on entity', { code: 400 })
      return INSERT.into(_target).entries(data)
    case 'DELETE':
      if (!one) cds.error('DELETE not allowed on collection', { code: 400 })
      // eslint-disable-next-line no-case-declarations
      const last = query._propertyAccess || (_target.ref && _target.ref[_target.ref.length - 1])
      if (target.elements[last] || target.elements[query._propertyAccess]) {
        // delete simple property
        const ref = { ref: query._propertyAccess ? _target.ref : _target.ref.slice(0, -1) }
        return UPDATE(ref).data({ [last]: null })
      } else {
        return DELETE.from(_target)
      }
    case 'UPDATE':
      // eslint-disable-next-line no-throw-literal
      if (!one) throw { statusCode: 400, code: '400', message: `INVALID_${odataReq.getMethod()}` }
      return UPDATE(_target).data(data)
    default:
      return query
  }
}

/**
 * This method transforms an odata request into a CQN object.
 *
 * @param {string} component - Component name
 * @param {object} service - Service, which will process this request
 * @param {object} target - The target entity
 * @param {object | Array} data - A copy of the request payload
 * @param {object} odataReq - OKRA's req
 * @param {boolean} upsert - CREATE on PUT/PATCH
 * @returns {object} - The CQN object
 */
module.exports = (component, service, target, data, odataReq, upsert) => {
  if (cds.env.features.odata_new_parser) {
    return parseToCqn(component, service, target, data, odataReq, upsert)
  }

  switch (component) {
    case DATA_CREATE_HANDLER:
      return createToCQN(service, target, data, odataReq, upsert)
    case DATA_DELETE_HANDLER:
      return deleteToCQN(service, odataReq)
    case DATA_READ_HANDLER:
      return readToCQN(service, target, odataReq)
    case DATA_UPDATE_HANDLER:
      return updateToCQN(service, data, odataReq)
    case 'BOUND.ACTION':
    case 'BOUND.FUNCTION':
      return boundToCQN(service, odataReq)
    default:
      return {}
  }
}
