const ExpressionToCQN = require('./ExpressionToCQN')
const { getFeatureNotSupportedError } = require('../../../util/errors')
const odata = require('../okra/odata-server')
const { ResourceKind } = require('../okra/odata-commons/uri/UriResource')
const {
  QueryOptions: { ORDERBY }
} = odata
const ExpressionKind = odata.uri.Expression.ExpressionKind
const getError = require('../../../../common/error')

const _buildNavRef = pathSegment => {
  return pathSegment.getProperty() ? pathSegment.getProperty().getName() : pathSegment.getNavigationProperty().getName()
}

const _orderExpression = order => {
  if (order.getExpression().getKind() === ExpressionKind.MEMBER) {
    const ref = []
    const lastSegment = order.getExpression().getPathSegments()[order.getExpression().getPathSegments().length - 1]
    if (
      lastSegment.getKind() === ResourceKind.ANY_EXPRESSION ||
      lastSegment.getKind() === ResourceKind.ALL_EXPRESSION
    ) {
      throw getError(501, '"$orderby" does not support lambda')
    }
    for (let i = 0; i < order.getExpression().getPathSegments().length; i++) {
      ref.push(_buildNavRef(order.getExpression().getPathSegments()[i]))
    }

    return {
      ref,
      sort: order.isDescending() ? 'desc' : 'asc'
    }
  }

  if (order.getExpression().getKind() === ExpressionKind.METHOD) {
    const ref = new ExpressionToCQN().parse(order.getExpression())
    ref.sort = order.isDescending() ? 'desc' : 'asc'
    return ref
  }

  throw getFeatureNotSupportedError(`Query option "${ORDERBY}" with kind "${order.getExpression().getKind()}"`)
}

const orderbyToCQN = (cqnPartial, orderBy) => {
  cqnPartial.orderBy = cqnPartial.orderBy || []
  for (const order of orderBy) {
    cqnPartial.orderBy.push(_orderExpression(order))
  }
}

module.exports = orderbyToCQN
