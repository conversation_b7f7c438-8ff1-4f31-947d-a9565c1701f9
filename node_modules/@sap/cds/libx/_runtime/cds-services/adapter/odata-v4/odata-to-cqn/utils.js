const { getFeatureNotSupportedError } = require('../../../util/errors')
const { resolveStructuredName } = require('../utils/handlerUtils')
const { findCsnTargetFor } = require('../../../../common/utils/csn')

const addLimit = (item, rows, offset) => {
  // ignore 0 offset -> truthy check
  if (rows != null || offset) {
    if (!item.limit) {
      item.limit = {}
    }
    if (rows != null) {
      item.limit.rows = { val: rows }
    }
    if (offset) {
      item.limit.offset = { val: offset }
    }
  }
}

const _isNum = type => {
  const nums = {
    'Edm.Byte': 1,
    'Edm.Int16': 1,
    'Edm.Int32': 1,
    'Edm.Double': 1
  }

  return type.toString() in nums
}

const _isBigNum = type => {
  const bigNums = {
    'Edm.Int64': 1,
    'Edm.Decimal': 1
  }

  return type.toString() in bigNums
}

const _getValue = (value, type) => {
  if (_isNum(type)) return Number(value)
  if (_isBigNum(type)) {
    const num = Number(value)
    return Number.isSafeInteger(num) ? num : value
  }
  if (type === 'Edm.Boolean') return value === 'true'
  return value
}

const convertKeyPredicatesToWhere = keyPredicates => {
  const where = []
  if (keyPredicates.length) {
    keyPredicates.forEach(kp => {
      if (where.length) where.push('and')
      const keyName = kp.getEdmRef().getName().replace(/\//g, '.')
      const type = kp.getEdmRef().getProperty().getType()
      let keyValue = _getValue(kp.getText(), type.toString())
      where.push({ ref: [keyName] }, '=', { val: keyValue })
    })
  }

  return where
}

const convertUrlPathToCqn = (segments, service) => {
  return segments
    .filter(
      segment =>
        segment.getKind() !== 'COUNT' && segment.getKind() !== 'PRIMITIVE.PROPERTY' && segment.getKind() !== 'VALUE'
    )
    .reduce((expr, segment) => {
      if (segment.getKind() === 'ENTITY' || segment.getKind() === 'ENTITY.COLLECTION') {
        const entity = segment.getEntitySet().getEntityType().getFullQualifiedName()
        const where = convertKeyPredicatesToWhere(segment.getKeyPredicates())
        let ref
        if (where.length) {
          ref = { ref: [{ id: findCsnTargetFor(entity.name, service.model, service.namespace).name, where }] }
        } else {
          ref = { ref: [findCsnTargetFor(entity.name, service.model, service.namespace).name] }
        }
        return ref
      }

      if (segment.getKind() === 'SINGLETON') {
        const singleton = segment.getSingleton().getEntityType().getFullQualifiedName()
        return { ref: [findCsnTargetFor(singleton.name, service.model, service.namespace).name] }
      }

      if (segment.getKind() === 'COMPLEX.PROPERTY') {
        expr.ref.push(segment.getProperty().getName())
        return expr
      }

      const where = convertKeyPredicatesToWhere(segment.getKeyPredicates())
      let ref
      if (where.length) {
        ref = { id: segment.getNavigationProperty().getName(), where }
      } else {
        ref = segment.getNavigationProperty().getName()
      }

      if (!expr.ref) expr.ref = []
      expr.ref.push(ref)
      return expr
    }, {})
}

const convertUrlPathToViewCqn = segments => {
  const args = segments[0].getKeyPredicates().reduce((prev, curr) => {
    const { keyName, val } = getSegmentKeyValue(curr)
    prev[keyName.replace(/\//g, '_')] = { val }
    return prev
  }, {})

  // REVISIT: Replace .getFullQualifiedName().toString() with findCsnTargetFor as done in convertUrlPathToCqn
  return {
    ref: [
      {
        id: segments[0]
          .getEntitySet()
          .getEntityType()
          .getFullQualifiedName()
          .toString()
          .replace(/Parameters$/, ''),
        args
      }
    ]
  }
}

const getPropertyParam = segments => {
  const index = segments[segments.length - 1].getKind() === 1 ? 2 : 1
  const prop = segments[segments.length - index].getProperty()
  const name = prop && prop.getName()
  return (
    name &&
    (segments.length > 1 ? { ref: resolveStructuredName(segments, segments.length - 2, [name]) } : { ref: [name] })
  )
}

const isSameArray = (arr1, arr2) => {
  return arr1.length === arr2.length && arr1.every((element, index) => element === arr2[index])
}

const _getStructKeys = (key, prefix, joinStructured) => {
  const structKeys = []
  for (const keyName in key.elements) {
    const keyElement = key.elements[keyName]
    if (keyElement._isStructured) {
      structKeys.push(..._getStructKeys(keyElement, [...prefix, keyName], joinStructured))
      continue
    }
    if (keyElement.isAssociation) continue
    const newKey = joinStructured ? [...prefix, keyName].join('_') : [...prefix, keyName]
    structKeys.push(newKey)
  }
  return structKeys
}

const getAllKeys = (entity, joinStructured = true) => {
  const allKeys = []
  if (entity && entity.elements) {
    // in elements because of aspects
    for (const keyName in entity.elements) {
      const key = entity.elements[keyName]
      if (!key.key || key.isAssociation || key.isComposition) continue
      if (key._isStructured) allKeys.push(..._getStructKeys(key, [keyName], joinStructured))
      else allKeys.push(keyName)
    }
  }
  return allKeys
}

const isPathSupported = (supported, pathSegments) => {
  for (const segment of pathSegments) {
    if (!supported[segment.getKind()]) {
      throw getFeatureNotSupportedError(`Request parameter "${segment.getKind()}"`)
    }
  }
}

const _parsePrimitiveValue = (edmRef, value) => {
  const typeName = edmRef.getProperty ? edmRef.getProperty().getType().getName() : edmRef.getType().getName()
  return typeName.startsWith('Int') ? Number(value) : typeName === 'Boolean' ? value === 'true' : value
}

const getSegmentKeyValue = segmentParam => {
  const edmRef = segmentParam.getEdmRef()
  const keyName = edmRef.getName()
  let val
  if (segmentParam.getAliasValue()) {
    const value = segmentParam.getAliasValue()
    // must be JSON  or a string according to
    // https://docs.oasis-open.org/odata/odata/v4.01/os/part2-url-conventions/odata-v4.01-os-part2-url-conventions.html#sec_ComplexandCollectionLiterals
    try {
      val = value === 'undefined' ? undefined : JSON.parse(value)
      return { keyName, val }
    } catch (e) {
      // plain string
    }
    val = _parsePrimitiveValue(edmRef, value) //> REVISIT: undefined handling needed here as well?
    return { keyName, val }
  }
  val = segmentParam.getText() === undefined ? undefined : _parsePrimitiveValue(edmRef, segmentParam.getText())
  return { keyName, val }
}

module.exports = {
  addLimit,
  convertUrlPathToCqn,
  convertUrlPathToViewCqn,
  isSameArray,
  getAllKeys,
  isPathSupported,
  getSegmentKeyValue,
  getPropertyParam
}
