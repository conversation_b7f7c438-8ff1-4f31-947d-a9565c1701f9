'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752579">
 *     OData CSDL # 12.1 Element edm:Action
 * </a>
 */
class CsdlAction {
  /**
   * @param {string} name - OData CSDL # 12.1.1 Attribute Name
   */
  constructor (name) {
    validateThat('name', name)
      .truthy()
      .typeOf('string')

    /**
     * OData CSDL # 12.1.1 Attribute Name.
     * @type {string}
     */
    this.name = name

    /**
     * OData CSDL # 12.1.2 Attribute IsBound
     * @type {boolean}
     */
    this.isBound = false

    /**
     * OData CSDL # 12.1.3 Attribute EntitySetPath
     * @type {string}
     */
    this.entitySetPath = null

    /**
     * OData CSDL # 12.4 Element edm:Parameter
     * @type {CsdlParameter[]}
     */
    this.parameters = []

    /**
     * OData CSDL # 12.3 Element edm:ReturnType
     * @type {CsdlReturnType}
     */
    this.returnType = null

    /**
     * OData CSDL # 14.3 Element edm:Annotation
     * @type {CsdlAnnotation[]}
     */
    this.annotations = []
  }

  /**
   * Sets whether the action is a bound action.
   * OData CSDL # 12.1.2 Attribute IsBound
   *
   * @param {boolean} isBound - indicates whether the action is a bound action
   * @returns {CsdlAction} this instance
   */
  setBound (isBound) {
    validateThat('isBound', isBound)
      .notNullNorUndefined()
      .typeOf('boolean')

    this.isBound = isBound
    return this
  }

  /**
   * Sets entity set path.
   * OData CSDL # 12.1.3 Attribute EntitySetPath
   *
   * @param {string} entitySetPath - entity set path for the action
   * @returns {CsdlAction} this instance
   */
  setEntitySetPath (entitySetPath) {
    validateThat('entitySetPath', entitySetPath)
      .truthy()
      .typeOf('string')

    this.entitySetPath = entitySetPath
    return this
  }

  /**
   * Sets parameters for the action.
   * OData CSDL # 12.4 Element edm:Parameter
   *
   * @param {CsdlParameter[]} parameters - action parameters
   * @returns {CsdlAction} this instance
   */
  setParameters (parameters) {
    validateThat('parameters', parameters)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.parameters = parameters
    return this
  }

  /**
   * Sets return type of the action.
   * OData CSDL # 12.3 Element edm:ReturnType
   *
   * @param {CsdlReturnType} returnType - return type of the action
   * @returns {CsdlAction} this instance
   */
  setReturnType (returnType) {
    validateThat('returnType', returnType)
      .truthy()
      .instanceOf(Object)

    this.returnType = returnType
    return this
  }

  /**
   * Sets a list of annotations for the action.
   * OData CSDL # 14.3 Element edm:Annotation
   *
   * @param {CsdlAnnotation[]} annotations - annotations for the action
   * @returns {CsdlAction} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.annotations = annotations
    return this
  }
}

module.exports = CsdlAction
