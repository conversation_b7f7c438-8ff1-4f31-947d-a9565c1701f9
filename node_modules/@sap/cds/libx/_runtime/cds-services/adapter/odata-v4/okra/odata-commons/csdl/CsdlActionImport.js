'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752610">
 *     OData CSDL # 13.5 Element edm:ActionImport
 * </a>
 */
class CsdlActionImport {
  /**
   * @param {string} name - OData CSDL # 13.5.1 Attribute Name
   * @param {FullQualifiedName} action - OData CSDL # 13.5.2 Attribute Action
   */
  constructor (name, action) {
    validateThat('name', name)
      .truthy()
      .typeOf('string')
    validateThat('action', action)
      .truthy()
      .instanceOf(Object)

    /**
     * OData CSDL # 13.5.1 Attribute Name
     * @type {string}
     */
    this.name = name

    /**
     * OData CSDL # 13.5.2 Attribute Action
     * @type {FullQualifiedName}
     */
    this.action = action

    /**
     * OData CSDL # 13.5.3 Attribute EntitySet
     * @type {string}
     */
    this.entitySet = null

    /**
     * OData CSDL # 14.3 Element edm:Annotation
     * @type {CsdlAnnotation[]}
     */
    this.annotations = []
  }

  /**
   * Sets entity set for the action import.
   * OData CSDL # 13.5.3 Attribute EntitySet
   *
   * @param {string} entitySet - name of the entity set for the action import
   * @returns {CsdlActionImport} this instance
   */
  setEntitySet (entitySet) {
    validateThat('entitySet', entitySet)
      .truthy()
      .typeOf('string')

    this.entitySet = entitySet
    return this
  }

  /**
   * Sets a list of annotations
   * OData CSDL # 14.3 Element edm:Annotation
   *
   * @param {CsdlAnnotation[]} annotations - annotations for the action import
   * @returns {CsdlActionImport} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.annotations = annotations
    return this
  }
}

module.exports = CsdlActionImport
