'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * This class represents the mapping between a namespace and the corresponding alias.
 */
class CsdlAliasInfo {
  /**
   * @param {string} namespace - namespace value
   * @param {string} alias - alias value for the namespace
   */
  constructor (namespace, alias) {
    validateThat('namespace', namespace)
      .truthy()
      .typeOf('string')
    validateThat('alias', alias)
      .truthy()
      .typeOf('string')

    /**
     * Namespace, which the alias corresponds to
     * @type {string}
     */
    this.namespace = namespace

    /**
     * Alias value for the namespace
     * @type {string}
     */
    this.alias = alias
  }

  /**
   * Returns the full qualified name of this alias.
   *
   * @returns {string} The full qualified name
   */
  toString () {
    return this.namespace + '.' + this.alias
  }
}

module.exports = CsdlAliasInfo
