'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752630">
 *     OData CSDL # 14.3 Element edm:Annotation
 * </a>
 */
class CsdlAnnotation {
  /**
   * @param {FullQualifiedName} term - Qualified name of the term attribute
   */
  constructor (term) {
    validateThat('term', term).instanceOf(Object)

    /**
     * The term attribute pointing to the Term in the scope
     * [OData CSDL] 14.3.1 Attribute Term
     * @type {FullQualifiedName}
     */
    this.term = term

    /**
     * [OData CSDL] 14.3.2 Attribute Qualifier
     * Used apply terms depending on a qualifier like 'tablet', 'desktop' or 'phone'
     * @type {string}
     */
    this.qualifier = null

    /**
     * Dynamic or constant annotation expression
     * @type {CsdlAnnotationExpression}
     */
    this.expression = null

    /**
     * OData CSDL # 14.3 Element edm:Annotation
     * A annotation may also be annotated
     * @type {CsdlAnnotation[]}
     */
    this.annotations = []
  }

  /**
   * Sets the annotated qualifier
   *
   * @param {string} qualifier annotated qualifier
   * @returns {CsdlAnnotation} this instance
   */
  setQualifier (qualifier) {
    validateThat('qualifier', qualifier)
      .truthy()
      .typeOf('string')
    this.qualifier = qualifier
    return this
  }

  /**
   * Sets the annotation expression
   *
   * @param {CsdlAnnotationExpression} expression annotation expression
   * @returns {CsdlAnnotation} this instance
   */
  setExpression (expression) {
    validateThat('expression', expression)
      .truthy()
      .instanceOf(Object)
    this.expression = expression
    return this
  }

  /**
   * Sets a list of annotations
   * OData CSDL # 14.3 Element edm:Annotation
   *
   * @param {CsdlAnnotation[]} annotations - annotations for this annotation
   * @returns {CsdlAnnotation} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.annotations = annotations
    return this
  }

  /**
   * Returns the stringified representation of this annotation.
   * The string representation is about stringified term#qualifier.
   *
   * @returns {string} The stringified representation of this annotation
   */
  toString () {
    if (this.qualifier) {
      return this.term.toString() + '#' + this.qualifier
    }
    return this.term.toString()
  }
}

module.exports = CsdlAnnotation
