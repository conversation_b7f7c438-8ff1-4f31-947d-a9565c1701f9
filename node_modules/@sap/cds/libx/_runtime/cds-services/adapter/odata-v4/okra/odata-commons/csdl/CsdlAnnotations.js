'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752627">
 *     OData CSDL # 14.2 Element edm:Annotations
 * </a>
 */
class CsdlAnnotations {
  /**
     @param {string} target - Target attribute whose value is a TargetPath
     */
  constructor (target) {
    validateThat('target', target)
      .truthy()
      .typeOf('string')

    /**
     * OData CSDL # 14.2.1 Attribute Target
     * @type {string}
     */
    this.target = target

    /**
     * OData CSDL # 14.2.2 Attribute Qualifier
     * @type {string}
     */
    this.qualifier = null

    /**
     * OData CSDL # 14.3 Element edm:Annotation
     * @type {CsdlAnnotation[]}
     */
    this.annotations = []
  }

  /**
   * Sets qualifier.
   * OData CSDL # 14.2.2 Attribute Qualifier
   *
   * @param {string} qualifier the qualifier
   * @returns {CsdlAnnotations} this instance
   */
  setQualifier (qualifier) {
    validateThat('qualifier', qualifier)
      .truthy()
      .typeOf('string')

    this.qualifier = qualifier
    return this
  }

  /**
   * Sets a list of annotations
   * OData CSDL # 14.3 Element edm:Annotation
   *
   * @param {CsdlAnnotation[]}annotations list of annotations
   * @returns {CsdlAnnotations} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .truthy()
      .array()
      .containsInstancesOf(Object)
    this.annotations = annotations
    return this
  }
}

module.exports = CsdlAnnotations
