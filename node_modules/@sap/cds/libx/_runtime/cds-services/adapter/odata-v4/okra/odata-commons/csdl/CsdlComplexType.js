'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752560">
 *  OData CSDL # 9.1 Element edm:ComplexType
 * </a>
 */
class CsdlComplexType {
  /**
   * @param {string} name - OData CSDL # 9.1.1 Attribute Name
   */
  constructor (name) {
    validateThat('name', name)
      .truthy()
      .typeOf('string')

    /**
     * OData CSDL # 9.1.1 Attribute Name
     * @type {string}
     */
    this.name = name

    /**
     * OData CSDL # 9.1.2 Attribute BaseType
     * @type {FullQualifiedName}
     */
    this.baseType = null

    /**
     * OData CSDL # 9.1.3 Attribute Abstract
     * @type {boolean}
     */
    this.isAbstract = false

    /**
     * OData CSDL # 6.1 Element edm:Property
     * @type {CsdlProperty[]}
     */
    this.properties = []

    /**
     * OData CSDL # 7.1 Element edm:NavigationProperty
     * @type {CsdlNavigationProperty[]}
     */
    this.navigationProperties = []

    /**
     * OData CSDL # 14.3 Element edm:Annotation
     * @type {CsdlAnnotation[]}
     */
    this.annotations = []
  }

  /**
   * Sets base type for the complex type.
   * OData CSDL # 9.1.2 Attribute BaseType
   *
   * @param {FullQualifiedName} baseType - name of the base type for the complex type
   * @returns {CsdlComplexType} this instance
   */
  setBaseType (baseType) {
    validateThat('baseType', baseType)
      .truthy()
      .instanceOf(Object)

    this.baseType = baseType
    return this
  }

  /**
   * OData CSDL # 9.1.4 Attribute OpenType
   * @type {boolean}
   */
  get isOpenType () {
    return false
  }

  /**
   * Sets properties for the complex type.
   * OData CSDL # 6.1 Element edm:Property
   *
   * @param {CsdlProperty[]} properties - properties for the complex type
   * @returns {CsdlComplexType} this instance
   */
  setProperties (properties) {
    validateThat('properties', properties)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.properties = properties
    return this
  }

  /**
   * Sets navigation properties for the complex type.
   * OData CSDL # 7.1 Element edm:NavigationProperty
   *
   * @param {CsdlNavigationProperty[]} navigationProperties - navigation properties for the
   * complex type
   * @returns {CsdlComplexType} this instance
   */
  setNavigationProperties (navigationProperties) {
    validateThat('navigationProperties', navigationProperties)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.navigationProperties = navigationProperties
    return this
  }

  /**
   * Sets annotations for the complex type.
   * OData CSDL # 14.3 Element edm:Annotation
   *
   * @param {CsdlAnnotation[]} annotations - annotations for the complex type
   * @returns {CsdlComplexType} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.annotations = annotations
    return this
  }
}

module.exports = CsdlComplexType
