'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752597">
 *     OData CSDL # 13.1 Element edm:EntityContainer
 * </a>
 */
class CsdlEntityContainer {
  /**
   * @param {string} name - OData CSDL # 12.1.1 Attribute Name
   */
  constructor (name) {
    validateThat('name', name)
      .truthy()
      .typeOf('string')

    /**
     * name of the entity container
     * OData CSDL # 13.1.1 Attribute Name
     * @type {string}
     */
    this.name = name

    /**
     * A list of entity sets
     * @type {CsdlEntitySet[]}
     */
    this.entitySets = null

    /**
     * A list of OData singletons
     * @type {CsdlSingleton[]}
     */
    this.singletons = []

    /**
     * A list of OData action imports
     * @type {CsdlActionImport[]}
     */
    this.actionImports = []

    /**
     * A list of OData function imports
     * @type {CsdlFunctionImport[]}
     */
    this.functionImports = []

    /**
     * OData CSDL # 14.3 Element edm:Annotation
     * @type {CsdlAnnotation[]}
     */
    this.annotations = []
  }

  /**
   * OData CSDL # 13.1.2 Attribute Extends
   * @type {FullQualifiedName}
   */
  get extends () {
    return null
  }

  /**
   * Sets entity sets.
   *
   * @param {CsdlEntitySet[]} entitySets Entity sets
   * @returns {CsdlEntityContainer} this instance
   */
  setEntitySets (entitySets) {
    validateThat('entitySets', entitySets)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)
    this.entitySets = entitySets
    return this
  }

  /**
   * Sets singletons.
   *
   * @param {CsdlSingleton[]} singletons the singletons
   * @returns {CsdlEntityContainer} this instance
   */
  setSingletons (singletons) {
    validateThat('singletons', singletons)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)
    this.singletons = singletons
    return this
  }

  /**
   * Sets action imports.
   *
   * @param {CsdlActionImport[]} actionImports the action imports
   * @returns {CsdlEntityContainer} this instance
   */
  setActionImports (actionImports) {
    validateThat('actionImports', actionImports)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)
    this.actionImports = actionImports
    return this
  }

  /**
   * Sets function imports.
   *
   * @param {CsdlFunctionImport[]} functionImports the function imports
   * @returns {CsdlEntityContainer} this instance
   */
  setFunctionImports (functionImports) {
    validateThat('functionImports', functionImports)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)
    this.functionImports = functionImports
    return this
  }

  /**
   * Sets annotations
   * OData CSDL # 14.3 Element edm:Annotation
   *
   * @param {CsdlAnnotation[]} annotations list of annotations
   * @returns {CsdlEntityContainer} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)
    this.annotations = annotations
    return this
  }
}

module.exports = CsdlEntityContainer
