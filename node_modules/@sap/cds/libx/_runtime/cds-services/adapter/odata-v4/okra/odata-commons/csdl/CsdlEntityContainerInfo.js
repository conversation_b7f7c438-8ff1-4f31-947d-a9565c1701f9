'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * This class represents a basic information about an entity container.
 *
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752597">
 *     OData CSDL # 13.1 Element edm:EntityContainer
 * </a>
 */
class CsdlEntityContainerInfo {
  /**
   * @param {FullQualifiedName} name - OData CSDL # 13.1.1 Attribute Name
   */
  constructor (name) {
    validateThat('name', name)
      .truthy()
      .instanceOf(Object)

    /**
     * OData CSDL # 13.1.1 Attribute Name
     * @type {FullQualifiedName}
     */
    this.name = name
  }

  /**
   * OData CSDL # 13.1.2 Attribute Extends
   * @type {FullQualifiedName}
   */
  get extends () {
    return null
  }
}

module.exports = CsdlEntityContainerInfo
