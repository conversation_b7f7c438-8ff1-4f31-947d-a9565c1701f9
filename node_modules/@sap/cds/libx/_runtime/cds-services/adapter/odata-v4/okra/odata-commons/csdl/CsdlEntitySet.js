'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752600">
 *     OData CSDL # 13.2 Element edm:EntitySet
 * </a>
 */
class CsdlEntitySet {
  /**
   * @param {string} name - OData CSDL # 13.2.1 Attribute Name
   * @param {FullQualifiedName} entityType - OData CSDL # 13.2.2 Attribute EntityType
   */
  constructor (name, entityType) {
    validateThat('name', name)
      .truthy()
      .typeOf('string')
    validateThat('entityType', entityType)
      .truthy()
      .instanceOf(Object)

    /**
     * OData CSDL # 13.2.1 Attribute Name
     * @type {string}
     */
    this.name = name

    /**
     * OData CSDL # 13.2.2 Attribute EntityType
     * @type {FullQualifiedName}
     */
    this.type = entityType

    /**
     * OData CSDL # 13.2.3 Attribute IncludeInServiceDocument
     * @type {boolean}
     */
    this.includeInServiceDocument = true

    /**
     * OData CSDL # 13.4 Element edm:NavigationPropertyBinding
     * @type {CsdlNavigationPropertyBinding[]}
     */
    this.navigationPropertyBindings = []

    /**
     * OData CSDL # 14.3 Element edm:Annotation
     * @type {CsdlAnnotation[]}
     */
    this.annotations = []
  }

  /**
   * Sets whether the entity set must be included into the service document.
   * OData CSDL # 13.2.3 Attribute IncludeInServiceDocument
   *
   * @param {boolean} includeInServiceDocument - indicates whether the entity set must be included
   *  into the service document
   * @returns {CsdlEntitySet} this instance
   */
  setIncludeInServiceDocument (includeInServiceDocument) {
    validateThat('includeInServiceDocument', includeInServiceDocument)
      .notNullNorUndefined()
      .typeOf('boolean')

    this.includeInServiceDocument = includeInServiceDocument
    return this
  }

  /**
   * Sets navigation property bindings.
   * OData CSDL # 13.4 Element edm:NavigationPropertyBinding
   *
   * @param  {CsdlNavigationPropertyBinding[]} navigationPropertyBindings - navigation property
   *  bindings for this entity set
   * @returns {CsdlEntitySet} this instance
   */
  setNavigationPropertyBindings (navigationPropertyBindings) {
    validateThat('navigationPropertyBindings', navigationPropertyBindings)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.navigationPropertyBindings = navigationPropertyBindings
    return this
  }

  /**
   * Sets annotations for this entity set.
   * OData CSDL # 14.3 Element edm:Annotation
   *
   * @param {CsdlAnnotation[]} annotations - annotations for this entity set
   * @returns {CsdlEntitySet} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.annotations = annotations
    return this
  }
}

module.exports = CsdlEntitySet
