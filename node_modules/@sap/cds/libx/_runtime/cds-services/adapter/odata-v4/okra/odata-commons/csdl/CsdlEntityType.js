'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752548">
 *     OData CSDL # 8.0 Element edm:EntityType
 * </a>
 */
class CsdlEntityType {
  /**
   * @param {string} name - OData CSDL # 8.1.1 Attribute Name
   */
  constructor (name) {
    validateThat('name', name)
      .truthy()
      .typeOf('string')

    /**
     * OData CSDL # 8.1.1 Attribute Name
     * @type {string}
     */
    this.name = name

    /**
     * OData CSDL # 8.2 Element edm:Key
     * @type {CsdlPropertyRef[]}
     */
    this.key = []

    /**
     * OData CSDL # 8.1.2 Attribute BaseType
     * @type {FullQualifiedName}
     */
    this.baseType = null

    /**
     * OData CSDL # 8.1.5 Attribute HasStream
     * @type {boolean}
     */
    this.hasStream = false

    /**
     * OData CSDL # 8.1.3 Attribute Abstract
     * @type {boolean}
     */
    this.isAbstract = false

    /**
     * Entity Type properties. OData CSDL # 6.1 Element edm:Property
     * @type {CsdlProperty[]}
     */
    this.properties = []

    /**
     * Entity Type navigation properties. OData CSDL # 7.1 Element edm:NavigationProperty
     * @type {CsdlNavigationProperty[]}
     */
    this.navigationProperties = []

    /**
     * Entity Type annotations. OData CSDL # 14.3 Element edm:Annotation
     * @type {CsdlAnnotation[]}
     */
    this.annotations = []
  }

  /**
   * Sets key properties.
   * OData CSDL # 8.2 Element edm:Key
   *
   * @param {CsdlPropertyRef[]} key - key properties
   * @returns {CsdlEntityType} this instance
   */
  setKey (key) {
    validateThat('key', key)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.key = key
    return this
  }

  /**
   * Sets base type.
   * OData CSDL # 8.1.2 Attribute BaseType
   *
   * @param {FullQualifiedName} baseType - the base type
   * @returns {CsdlEntityType} this instance
   */
  setBaseType (baseType) {
    validateThat('baseType', baseType)
      .truthy()
      .instanceOf(Object)

    this.baseType = baseType
    return this
  }

  /**
   * OData CSDL # 8.1.4 Attribute OpenType
   * @type {boolean}
   */
  get isOpenType () {
    return false
  }

  /**
   * Sets hasStream attribute.
   * OData CSDL # 8.1.5 Attribute HasStream
   *
   * @param {boolean} hasStream - hasStream attribute value
   * @returns {CsdlEntityType} this instance
   */
  setHasStream (hasStream) {
    validateThat('hasStream', hasStream)
      .notNullNorUndefined()
      .typeOf('boolean')

    this.hasStream = hasStream
    return this
  }

  /**
   * Sets properties.
   * OData CSDL # 6.1 Element edm:Property
   *
   * @param {CsdlProperty[]} properties - entity type properties
   * @returns {CsdlEntityType} this instance
   */
  setProperties (properties) {
    validateThat('properties', properties)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.properties = properties
    return this
  }

  /**
   * Sets navigation properties.
   * OData CSDL # 7.1 Element edm:NavigationProperty
   *
   * @param  {CsdlNavigationProperty[]} navigationProperties - entity type navigation properties
   * @returns {CsdlEntityType} this instance
   */
  setNavigationProperties (navigationProperties) {
    validateThat('navigationProperties', navigationProperties)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.navigationProperties = navigationProperties
    return this
  }

  /**
   * Sets a list of annotations.
   * OData CSDL # 14.3 Element edm:Annotation
   *
   * @param {CsdlAnnotation[]} annotations - entity type annotations
   * @returns {CsdlEntityType} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.annotations = annotations
    return this
  }
}

module.exports = CsdlEntityType
