'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752570">
 *     OData CSDL # 10.2 Element edm:Member
 * </a>
 */
class CsdlEnumMember {
  /**
   * @param {string} name - OData CSDL # 10.2.1 Attribute Name
   * @param {number} value - OData CSDL # 10.2.2 Attribute Value
   */
  constructor (name, value) {
    validateThat('name', name)
      .truthy()
      .typeOf('string')
    validateThat('value', value)
      .notNullNorUndefined()
      .typeOf('number')
      .integer()

    /**
     * OData CSDL # 10.2.1 Attribute Name
     * @type {string}
     */
    this.name = name

    /**
     * OData CSDL # 10.2.2 Attribute Value
     * @type {number}
     */
    this.value = value

    /**
     * OData CSDL # 14.3 Element edm:Annotation
     * @type {CsdlAnnotation[]}
     */
    this.annotations = []
  }

  /**
   * Sets a list of annotations for the member.
   * OData CSDL # 14.3 Element edm:Annotation
   *
   * @param {CsdlAnnotation[]} annotations - annotations for the member
   * @returns {CsdlEnumMember} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.annotations = annotations
    return this
  }
}

module.exports = CsdlEnumMember
