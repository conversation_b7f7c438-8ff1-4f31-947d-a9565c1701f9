'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752566">
 *     OData CSDL # 10.1 Element edm:EnumType
 * </a>
 */
class CsdlEnumType {
  /**
   * @param {string} name - OData CSDL # 10.1.1 Attribute Name
   * @param {FullQualifiedName} underlyingType - OData CSDL # 10.1.2 Attribute UnderlyingType
   */
  constructor (name, underlyingType) {
    validateThat('name', name)
      .truthy()
      .typeOf('string')
    validateThat('underlyingType', underlyingType)
      .truthy()
      .instanceOf(Object)

    /**
     * OData CSDL # 10.1.1 Attribute Name
     * @type {string}
     */
    this.name = name

    /**
     * OData CSDL # 10.1.2 Attribute UnderlyingType
     * @type {FullQualifiedName}
     */
    this.underlyingType = underlyingType

    /**
     * OData CSDL # 10.1.3 Attribute IsFlags
     * @type {boolean}
     */
    this.isFlags = false

    /**
     * Enumeration-type members. OData CSDL # 10.2 Element edm:Member
     * @type {CsdlEnumMember[]}
     */
    this.members = []

    /**
     * Enumeration-type annotations. OData CSDL # 14.3 Element edm:Annotation
     * @type {CsdlAnnotation[]}
     */
    this.annotations = []
  }

  /**
   * Sets IsFlags attribute.
   * OData CSDL # 10.1.3 Attribute IsFlags
   *
   * @param {boolean} isFlags - IsFlags attribute value
   * @returns {CsdlEnumType} this instance
   */
  setIsFlags (isFlags) {
    validateThat('isFlags', isFlags)
      .notNullNorUndefined()
      .typeOf('boolean')

    this.isFlags = isFlags
    return this
  }

  /**
   * Sets members.
   * OData CSDL # 10.2 Element edm:Member
   *
   * @param {CsdlEnumMember[]} members - enumeration-type members
   * @returns {CsdlEnumType} this instance
   */
  setMembers (members) {
    validateThat('members', members)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.members = members
    return this
  }

  /**
   * Sets a list of annotations.
   * OData CSDL # 14.3 Element edm:Annotation
   *
   * @param {CsdlAnnotation[]} annotations - entity type annotations
   * @returns {CsdlEnumType} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.annotations = annotations
    return this
  }
}

module.exports = CsdlEnumType
