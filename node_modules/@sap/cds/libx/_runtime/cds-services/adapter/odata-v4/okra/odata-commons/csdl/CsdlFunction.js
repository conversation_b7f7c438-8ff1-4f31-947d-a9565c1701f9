'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752583">
 *     OData CSDL # 12.2 Element edm:Function
 * </a>
 */
class CsdlFunction {
  /**
   * @param {string} name - OData CSDL # 12.2.1 Attribute Name
   * @param {CsdlReturnType} returnType - OData CSDL # 12.3 Element edm:ReturnType
   */
  constructor (name, returnType) {
    validateThat('name', name)
      .truthy()
      .typeOf('string')
    validateThat('returnType', returnType)
      .truthy()
      .instanceOf(Object)

    /**
     * OData CSDL # 12.2.1 Attribute Name.
     * @type {string}
     */
    this.name = name

    /**
     * OData CSDL # 12.3 Element edm:ReturnType
     * @type {CsdlReturnType}
     */
    this.returnType = returnType

    /**
     * OData CSDL # 12.2.2 Attribute IsBound
     * @type {boolean}
     */
    this.isBound = false

    /**
     * OData CSDL # 12.2.3 Attribute IsComposable
     * @type {boolean}
     */
    this.isComposable = false

    /**
     * OData CSDL # 12.2.4 Attribute EntitySetPath
     * @type {string}
     */
    this.entitySetPath = null

    /**
     * OData CSDL # 12.4 Element edm:Parameter
     * @type {CsdlParameter[]}
     */
    this.parameters = []

    /**
     * OData CSDL # 14.3 Element edm:Annotation
     * @type {CsdlAnnotation[]}
     */
    this.annotations = []
  }

  /**
   * Sets whether the function should be bound.
   * OData CSDL # 12.2.2 Attribute IsBound
   *
   * @param {boolean} isBound - indicates whether the function should be bound
   * @returns {CsdlFunction} this instance
   */
  setBound (isBound) {
    validateThat('isBound', isBound)
      .notNullNorUndefined()
      .typeOf('boolean')

    this.isBound = isBound
    return this
  }

  /**
   * Sets whether the function should be composable.
   * OData CSDL # 12.2.3 Attribute IsComposable
   *
   * @param {boolean} isComposable - indicates whether the function should be composable
   * @returns {CsdlFunction} this instance
   */
  setComposable (isComposable) {
    validateThat('isComposable', isComposable)
      .notNullNorUndefined()
      .typeOf('boolean')

    this.isComposable = isComposable
    return this
  }

  /**
   * Sets entity set path for the function.
   * OData CSDL # 12.2.4 Attribute EntitySetPath
   *
   * @param {string} entitySetPath - entity set path for the function
   * @returns {CsdlFunction} this instance
   */
  setEntitySetPath (entitySetPath) {
    validateThat('entitySetPath', entitySetPath)
      .truthy()
      .typeOf('string')

    this.entitySetPath = entitySetPath
    return this
  }

  /**
   * Sets parameters for the function.
   * OData CSDL # 12.4 Element edm:Parameter
   *
   * @param {CsdlParameter[]} parameters - parameters for the function
   * @returns {CsdlFunction} this instance
   */
  setParameters (parameters) {
    validateThat('parameters', parameters)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.parameters = parameters
    return this
  }

  /**
   * Sets a list of annotations for the function
   * OData CSDL # 14.3 Element edm:Annotation
   *
   * @param {CsdlAnnotation[]} annotations - annotations for the function
   * @returns {CsdlFunction} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.annotations = annotations
    return this
  }
}

module.exports = CsdlFunction
