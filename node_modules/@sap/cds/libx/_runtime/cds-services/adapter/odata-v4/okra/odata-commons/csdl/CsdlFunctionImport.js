'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752614">
 *     OData CSDL # 13.6 Element edm:FunctionImport
 * </a>
 */
class CsdlFunctionImport {
  /**
   * @param {string} name - OData CSDL # 13.6.1 Attribute Name
   * @param {FullQualifiedName} functionName - OData CSDL # 13.6.2 Attribute Function
   */
  constructor (name, functionName) {
    validateThat('name', name)
      .truthy()
      .typeOf('string')
    validateThat('functionName', functionName)
      .truthy()
      .instanceOf(Object)

    /**
     * OData CSDL # 13.6.1 Attribute Name
     * @type {string}
     */
    this.name = name

    /**
     * OData CSDL # 13.6.2 Attribute Function
     * @type {FullQualifiedName}
     */
    this.functionName = functionName

    /**
     * OData CSDL # 13.6.3 Attribute EntitySet
     * @type {string}
     */
    this.entitySet = null

    /**
     * OData CSDL # 13.6.4 Attribute IncludeInServiceDocument
     * @type {boolean}
     */
    this.includeInServiceDocument = false

    /**
     * OData CSDL # 14.3 Element edm:Annotation
     * @type {CsdlAnnotation[]}
     */
    this.annotations = []
  }

  /**
   * Sets entity set for the function import.
   * OData CSDL # 13.6.3 Attribute EntitySet
   *
   * @param {string} entitySet - name of the entity set for the function import
   * @returns {CsdlFunctionImport} this instance
   */
  setEntitySet (entitySet) {
    validateThat('entitySet', entitySet)
      .truthy()
      .typeOf('string')

    this.entitySet = entitySet
    return this
  }

  /**
   * Set whether the function import should be included into the service document.
   * OData CSDL # 13.6.4 Attribute IncludeInServiceDocument
   *
   * @param {boolean} includeInServiceDocument - indicates whether the function import should be
   *  included into the service document
   * @returns {CsdlFunctionImport} this instance
   */
  setIncludeInServiceDocument (includeInServiceDocument) {
    validateThat('includeInServiceDocument', includeInServiceDocument)
      .notNullNorUndefined()
      .typeOf('boolean')

    this.includeInServiceDocument = includeInServiceDocument
    return this
  }

  /**
   * Sets annotations for the function import.
   * OData CSDL # 14.3 Element edm:Annotation
   *
   * @param {CsdlAnnotation[]} annotations - annotations for the function import
   * @returns {CsdlFunctionImport} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.annotations = annotations
    return this
  }
}

module.exports = CsdlFunctionImport
