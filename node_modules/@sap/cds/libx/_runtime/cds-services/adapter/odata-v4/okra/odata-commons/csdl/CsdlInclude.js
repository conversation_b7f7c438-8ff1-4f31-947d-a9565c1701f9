'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752506">
 *     OData CSDL # 3.4 Element edmx:Include
 * </a>
 */
class CsdlInclude {
  /**
   * @param {string} namespace - OData CSDL # 3.4.1 Attribute Namespace
   */
  constructor (namespace) {
    validateThat('namespace', namespace)
      .truthy()
      .typeOf('string')

    /**
     * OData CSDL # 3.4.1 Attribute Namespace
     * @type {string}
     */
    this.namespace = namespace

    /**
     * OData CSDL # 3.4.2 Attribute Alias
     * @type {string}
     */
    this.alias = null

    this.annotations = []
  }

  /**
   * Sets the alias.
   * OData CSDL # 3.4.2 Attribute <PERSON>as
   *
   * @param {string} alias - alias value
   * @returns {CsdlInclude} this instance
   */
  set<PERSON><PERSON><PERSON> (alias) {
    validateThat('alias', alias)
      .truthy()
      .typeOf('string')

    this.alias = alias
    return this
  }

  /**
   * Sets a list of annotations.
   * OData CSDL # 14.3 Element edm:Annotation
   *
   * @param {CsdlAnnotation[]} annotations - Include annotations
   * @returns {CsdlInclude} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.annotations = annotations
    return this
  }
}

module.exports = CsdlInclude
