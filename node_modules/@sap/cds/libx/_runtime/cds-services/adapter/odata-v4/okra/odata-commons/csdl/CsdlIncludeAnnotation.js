'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752509">
 *     OData CSDL # 3.5 Element edmx:IncludeAnnotations
 * </a>
 */
class CsdlIncludeAnnotation {
  /**
   * @param {string} termNamespace - OData CSDL # 3.5.1 Attribute TermNamespace
   */
  constructor (termNamespace) {
    validateThat('termNamespace', termNamespace)
      .truthy()
      .typeOf('string')

    /**
     * OData CSDL # 3.5.1 Attribute TermNamespace
     * @type {string}
     */
    this.termNamespace = termNamespace

    /**
     * OData CSDL # 3.5.2 Attribute Qualifier
     * @type {string}
     */
    this.qualifier = null

    /**
     * OData CSDL # 3.5.3 Attribute TargetNamespace
     * @type {string}
     */
    this.targetNamespace = null
  }

  /**
   * Sets qualifier.
   * OData CSDL # 3.5.2 Attribute Qualifier
   *
   * @param {string} qualifier - qualifier value
   * @returns {CsdlIncludeAnnotation} this instance
   */
  setQualifier (qualifier) {
    validateThat('qualifier', qualifier)
      .truthy()
      .typeOf('string')

    this.qualifier = qualifier
    return this
  }

  /**
   * Sets target namespace.
   * OData CSDL # 3.5.3 Attribute TargetNamespace
   *
   * @param {string} targetNamespace - target namespace value
   * @returns {CsdlIncludeAnnotation} this instance
   */
  setTargetNamespace (targetNamespace) {
    validateThat('targetNamespace', targetNamespace)
      .truthy()
      .typeOf('string')

    this.targetNamespace = targetNamespace
    return this
  }
}

module.exports = CsdlIncludeAnnotation
