'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752537">
 *     OData CSDL # 7.1 Element edm:NavigationProperty
 * </a>
 */
class CsdlNavigationProperty {
  /**
   * @param {string} name - OData CSDL # 7.1.1 Attribute Name
   * @param {FullQualifiedName} type - OData CSDL # 7.1.2 Attribute Type
   */
  constructor (name, type) {
    validateThat('name', name)
      .truthy()
      .typeOf('string')
    validateThat('type', type)
      .truthy()
      .instanceOf(Object)

    /**
     * OData CSDL # 7.1.1 Attribute Name
     * @type {string}
     */
    this.name = name

    /**
     * OData CSDL # 7.1.2 Attribute Type
     * @type {FullQualifiedName}
     */
    this.type = type

    /**
     * OData CSDL # 7.1.3 Attribute Nullable
     * @type {boolean}
     */
    this.isNullable = true

    /**
     * OData CSDL # 7.1.4 Attribute Partner
     * @type {string}
     */
    this.partner = null

    /**
     * OData CSDL # 7.1.5 Attribute ContainsTarget
     * @type {boolean}
     */
    this.containsTarget = false

    /**
     * OData CSDL # 7.2 Element edm:ReferentialConstraint
     * @type {CsdlReferentialConstraint[]}
     */
    this.referentialConstraints = []

    /**
     * OData CSDL # 7.3 Element edm:OnDelete
     * @type {CsdlOnDelete}
     */
    this.onDelete = null

    /**
     * Indicates whether the navigation property references a collection
     * @type {boolean}
     */
    this.isCollection = false

    /**
     * OData CSDL # 14.3 Element edm:Annotation
     * @type {CsdlAnnotation[]}
     */
    this.annotations = []
  }

  /**
   * Sets whether the navigation property references a collection.
   *
   * @param {boolean} isCollection - indicates whether the navigation property references a
   * collection
   * @returns {CsdlNavigationProperty} this instance
   */
  setCollection (isCollection) {
    validateThat('isCollection', isCollection)
      .notNullNorUndefined()
      .typeOf('boolean')

    this.isCollection = isCollection
    return this
  }

  /**
   * OData CSDL # 7.1.3 Attribute Nullable
   *
   * @param {boolean} isNullable - indicates whether the navigation property is nullable
   * @returns {CsdlNavigationProperty} this instance
   */
  setNullable (isNullable) {
    validateThat('isNullable', isNullable)
      .notNullNorUndefined()
      .typeOf('boolean')

    this.isNullable = isNullable
    return this
  }

  /**
   * OData CSDL # 7.1.4 Attribute Partner
   *
   * @param {string} partner - value of the 'Partner' attribute
   * @returns {CsdlNavigationProperty} this instance
   */
  setPartner (partner) {
    validateThat('partner', partner)
      .notNullNorUndefined()
      .typeOf('string')

    this.partner = partner
    return this
  }

  /**
   * OData CSDL # 7.1.5 Attribute ContainsTarget
   *
   * @param {boolean} containsTarget - value of the 'ContainsTarget' attribute
   * @returns {CsdlNavigationProperty} this instance
   */
  setContainsTarget (containsTarget) {
    validateThat('containsTarget', containsTarget)
      .notNullNorUndefined()
      .typeOf('boolean')

    this.containsTarget = containsTarget
    return this
  }

  /**
   * Sets referential constraints.
   * OData CSDL # 7.2 Element edm:ReferentialConstraint
   *
   * @param {CsdlReferentialConstraint[]} referentialConstraints - referential constraints
   * @returns {CsdlNavigationProperty} this instance
   */
  setReferentialConstraints (referentialConstraints) {
    validateThat('referentialConstraints', referentialConstraints)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.referentialConstraints = referentialConstraints
    return this
  }

  /**
   * Sets value for 'OnDelete' attribute.
   * OData CSDL # 7.3 Element edm:OnDelete
   *
   * @param {CsdlOnDelete} onDelete - value for 'OnDelete' attribute
   * @returns {CsdlNavigationProperty} this instance
   */
  setOnDelete (onDelete) {
    validateThat('onDelete', onDelete)
      .notNullNorUndefined()
      .instanceOf(Object)

    this.onDelete = onDelete
    return this
  }

  /**
   * Sets a list of annotations.
   * OData CSDL # 14.3 Element edm:Annotation
   *
   * @param {CsdlAnnotation[]} annotations - list of annotations
   * @returns {CsdlNavigationProperty} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.annotations = annotations
    return this
  }
}

module.exports = CsdlNavigationProperty
