'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752607">
 *     OData CSDL # 13.4 Element edm:NavigationPropertyBinding
 * </a>
 */
class CsdlNavigationPropertyBinding {
  /**
   * @param {string} path - OData CSDL # 13.4.1 Attribute Path
   * @param {string} target - OData CSDL # 13.4.2 Attribute Target
   */
  constructor (path, target) {
    validateThat('path', path)
      .truthy()
      .typeOf('string')
    validateThat('target', target)
      .truthy()
      .typeOf('string')

    /**
     * OData CSDL # 13.4.1 Attribute Path
     * @type {string}
     */
    this.path = path

    /**
     * OData CSDL # 13.4.2 Attribute Target
     * @type {string}
     */
    this.target = target
  }
}

module.exports = CsdlNavigationPropertyBinding
