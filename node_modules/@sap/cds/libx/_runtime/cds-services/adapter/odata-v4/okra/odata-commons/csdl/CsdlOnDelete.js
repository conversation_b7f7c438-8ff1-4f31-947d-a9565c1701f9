'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat
const IllegalArgumentError = require('../errors/IllegalArgumentError')

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752546">
 *     OData CSDL # 7.3 Element edm:OnDelete
 * </a>
 */
class CsdlOnDelete {
  /**
   * @param {string} action - OData CSDL # 7.3.1 Attribute Action. A value from
   * CsdlOnDelete.Actions object must be used, e.g., CsdlOnDelete.Actions.Cascade.
   */
  constructor (action) {
    validateThat('action', action)
      .truthy()
      .typeOf('string')

    if (!CsdlOnDelete.Actions[action]) {
      throw IllegalArgumentError.createForIllegalValue('action', ...Object.keys(CsdlOnDelete.Actions))
    }

    /**
     * OData CSDL # 7.3.1 Attribute Action
     * @type {string}
     */
    this.action = action

    /**
     * OData CSDL # 14.3 Element edm:Annotation
     * @type {CsdlAnnotation[]}
     */
    this.annotations = []
  }

  /**
   * Sets a list of annotations
   * OData CSDL # 14.3 Element edm:Annotation
   *
   * @param {CsdlAnnotation[]} annotations - annotations for OnDelete element
   * @returns {CsdlOnDelete} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.annotations = annotations
    return this
  }
}

/**
 * Supported OnDelete actions.
 * OData CSDL # 7.3.1 Attribute Action
 *
 * @enum {string}
 * @readonly
 */
CsdlOnDelete.Actions = {
  Cascade: 'Cascade',
  None: 'None',
  SetNull: 'SetNull',
  SetDefault: 'SetDefault'
}

module.exports = CsdlOnDelete
