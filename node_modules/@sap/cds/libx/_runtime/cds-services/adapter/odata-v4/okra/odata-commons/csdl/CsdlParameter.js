'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat
const IllegalArgumentError = require('../errors/IllegalArgumentError')

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752591">
 *     OData CSDL # 12.4 Element edm:Parameter
 * </a>
 */
class CsdlParameter {
  /**
   * @param {string} name - OData CSDL # 12.4.1 Attribute Name
   * @param {FullQualifiedName} type - OData CSDL # 12.4.2 Attribute Type
   */
  constructor (name, type) {
    validateThat('name', name)
      .truthy()
      .typeOf('string')
    validateThat('type', type)
      .truthy()
      .instanceOf(Object)

    /**
     * OData CSDL # 12.4.1 Attribute Name
     * @type {string}
     */
    this.name = name

    /**
     * OData CSDL # 12.4.2 Attribute Type
     * @type {FullQualifiedName}
     */
    this.type = type

    /**
     * Indicates, whether the parameter value is a collection
     * @type {boolean}
     */
    this.isCollection = false

    /**
     * OData CSDL # 12.4.3 Attribute Nullable
     * @type {boolean}
     */
    this.isNullable = true

    /**
     * OData CSDL # 6.2.2 Attribute MaxLength
     * @type {number | string}
     */
    this.maxLength = null

    /**
     * OData CSDL # 6.2.3 Attribute Precision
     * @type {number}
     */
    this.precision = null

    /**
     * OData CSDL # 6.2.4 Attribute Scale
     * @type {number | string}
     */
    this.scale = null

    /**
     * [OData CSDL] 6.2.6 Attribute SRID
     * @type {number|string}
     */
    this.srid = null

    /**
     * OData CSDL # 14.3 Element edm:Annotation
     * @type {CsdlAnnotation[]}
     */
    this.annotations = []
  }

  /**
   * Sets whether the parameter represents a collection
   *
   * @param {boolean} isCollection - indicates, whether the parameter should represent a
   * collection
   * @returns {CsdlParameter} this instance
   */
  setCollection (isCollection) {
    validateThat('isCollection', isCollection)
      .notNullNorUndefined()
      .typeOf('boolean')

    this.isCollection = isCollection
    return this
  }

  /**
   * Sets whether the parameter is nullable.
   * OData CSDL # 12.4.3 Attribute Nullable
   *
   * @param {boolean} isNullable - indicates whether the parameter is nullable
   * @returns {CsdlParameter} this instance
   */
  setNullable (isNullable) {
    validateThat('isNullable', isNullable)
      .notNullNorUndefined()
      .typeOf('boolean')

    this.isNullable = isNullable
    return this
  }

  /**
   * Sets 'MaxLength' facet.
   * OData CSDL # 6.2.2 Attribute MaxLength
   *
   * @param {number | string} maxLength - value of the 'MaxLength' facet. It must be either a
   * positive integer value or a special 'max' string value. More information can be found in the
   * CSDL specification.
   * @returns {CsdlParameter} this instance
   */
  setMaxLength (maxLength) {
    const validate = validateThat('maxLength', maxLength).notNullNorUndefined()

    // check if the special 'max' value is specified
    if (typeof maxLength === 'string') {
      // check whether a string value is specified but not the allowed one
      if (maxLength !== 'max') {
        throw IllegalArgumentError.createForIllegalTypeValue('maxLength', 'string', 'max')
      }

      this.maxLength = 'max'
      return this
    }

    // otherwise the value must be a positive integer
    validate.integer().positiveInteger()

    this.maxLength = maxLength
    return this
  }

  /**
   * Sets Precision facet.
   * OData CSDL # 6.2.3 Attribute Precision
   *
   * @param {number} precision - value of the Precision facet.
   * @returns {CsdlParameter} this instance
   */
  setPrecision (precision) {
    validateThat('precision', precision)
      .notNullNorUndefined()
      .integer()
      .nonNegativeInteger()

    this.precision = precision
    return this
  }

  /**
   * Sets 'Scale' facet.
   * OData CSDL # 6.2.4 Attribute Scale
   *
   * @param {number | string} scale - value of the 'Scale' facet. It must be either a
   * non-negative integer value or a special 'variable' string value. More information can be
   * found in the CSDL specification.
   * @returns {CsdlParameter} this instance
   */
  setScale (scale) {
    const validate = validateThat('scale', scale).notNullNorUndefined()

    // check if the special 'variable' value is specified
    if (typeof scale === 'string') {
      // check whether a string value is specified but not the allowed one
      if (scale !== 'variable') {
        throw IllegalArgumentError.createForIllegalTypeValue('scale', 'string', 'variable')
      }

      this.scale = 'variable'
      return this
    }

    // otherwise the value must be a non-negative integer
    validate.integer().nonNegativeInteger()

    this.scale = scale
    return this
  }

  /**
   * Sets 'SRID' facet.
   * OData CSDL # 6.2.6 Attribute SRID.
   * @param {number|string} srid value of the 'SRID' facet; it must be either a
   *                             non-negative integer value or a special string 'variable'
   * @returns {CsdlParameter} this instance
   */
  setSrid (srid) {
    const validate = validateThat('srid', srid).notNullNorUndefined()
    if (typeof srid === 'string') {
      if (srid !== 'variable') {
        throw IllegalArgumentError.createForIllegalTypeValue('srid', 'string', 'variable')
      }
    } else {
      validate.integer().nonNegativeInteger()
    }

    this.srid = srid
    return this
  }

  /**
   * Sets a list of annotations for the parameter.
   * OData CSDL # 14.3 Element edm:Annotation
   *
   * @param {CsdlAnnotation[]} annotations - annotations for the parameter
   * @returns {CsdlParameter} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.annotations = annotations
    return this
  }
}

module.exports = CsdlParameter
