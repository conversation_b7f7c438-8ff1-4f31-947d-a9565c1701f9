'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752556">
 *     OData CSDL # 8.3 Element edm:PropertyRef
 * </a>
 */
class CsdlPropertyRef {
  /**
   * @param {string} name - OData CSDL # 8.3.1 Attribute Name
   */
  constructor (name) {
    validateThat('name', name)
      .truthy()
      .typeOf('string')

    /**
     * OData CSDL # 8.3.1 Attribute Name
     * @type {string}
     */
    this.name = name

    /**
     * OData CSDL # 8.3.2 Attribute Alias
     * @type {string}
     */
    this.alias = null
  }

  /**
   * Sets alias.
   * OData CSDL # 8.3.2 Attribute Alias
   *
   * @param {string} alias - value for the 'Alias' attribute
   * @returns {CsdlPropertyRef} this instance
   */
  setA<PERSON><PERSON> (alias) {
    validateThat('alias', alias)
      .truthy()
      .typeOf('string')

    this.alias = alias
    return this
  }
}

module.exports = CsdlPropertyRef
