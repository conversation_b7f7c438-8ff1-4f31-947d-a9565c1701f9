'use strict'

/**
 * This class is used by the EDM to dynamically load parts of the EDM. A application developer may
 * use this class directly and register the loaders for the specific artifacts or just inherit from
 * this class and overwrite methods for the specific artifacts.
 */
class CsdlProvider {
  /**
   * Returns a list with References {@see CsdlReference}
   * @returns {CsdlReference[]} the references
   */
  getReferences () {
    return null
  }

  /**
   * Return the enumeration type described via namespace or alias and name, or null if not found.
   *
   * @param  {FullQualifiedName} enumTypeName full qualified name of enumeration type
   * @returns {?CsdlEnumType} enumeration type for given name
   */
  getEnumType (enumTypeName) {
    // eslint-disable-line no-unused-vars
    return null
  }

  /**
   * Return the type definition described via namespace or alias and name, or null if not found
   *
   * @param  {FullQualifiedName} typeDefinitionName full qualified name of type definition
   * @returns {?CsdlTypeDefinition} Type definition type for given name
   */
  // eslint-disable-next-line no-unused-vars
  getTypeDefinition (typeDefinitionName) {
    return null
  }

  /**
   * Return the entity type described via namespace or alias and name, or null if not found
   *
   * @param  {FullQualifiedName} entityTypeName Entity type  name of type definition
   * @returns {?CsdlEntityType} Entity type for given name
   */
  // eslint-disable-next-line no-unused-vars
  getEntityType (entityTypeName) {
    return null
  }

  /**
   * Return the complex type described via namespace or alias and name, or null if not found
   *
   * @param {FullQualifiedName} complexTypeName full qualified name of complex type
   * @returns {?CsdlComplexType} Entity type for given name
   */
  // eslint-disable-next-line no-unused-vars
  getComplexType (complexTypeName) {
    return null
  }

  /**
   * Return the action overloads described via namespace or alias, or null if not found
   *
   * @param {FullQualifiedName} actionName full qualified name of action
   * @returns {?CsdlAction[]} Action list for given name
   */
  // eslint-disable-next-line no-unused-vars
  getActions (actionName) {
    return null
  }

  /**
   * Return the function overloads described via namespace or alias, or null if not found
   *
   * @param {FullQualifiedName} functionName full qualified name of function
   * @returns {?CsdlFunction[]} Function list for given name
   */
  // eslint-disable-next-line no-unused-vars
  getFunctions (functionName) {
    return null
  }

  /**
   * This method should return the entity set described by container and name,
   * or null if not found
   *
   * @param {FullQualifiedName} entityContainer this EntitySet is contained in
   * @param {string} entitySetName name of entity set
   * @returns {?CsdlEntitySet} EntitySet for given name
   */
  // eslint-disable-next-line no-unused-vars
  getEntitySet (entityContainer, entitySetName) {
    return null
  }

  /**
   * This method should return the singleton described by container and name,
   * or null if not found
   *
   * @param {FullQualifiedName} entityContainer this Singleton is contained in
   * @param {string} singletonName name of singleton
   * @returns {?CsdlSingleton} Singleton for given name
   */
  // eslint-disable-next-line no-unused-vars
  getSingleton (entityContainer, singletonName) {
    return null
  }

  /**
   * This method should return the action import described by container and name,
   * or null if not found
   *
   * @param {FullQualifiedName} entityContainer this ActionImport is contained in
   * @param {string} actionImportName name of action import
   * @returns {?CsdlActionImport} CsdlActionImport for given name
   */
  // eslint-disable-next-line no-unused-vars
  getActionImport (entityContainer, actionImportName) {
    return null
  }

  /**
   * This method should return the function import described by container and name,
   * or null if not found
   *
   * @param {FullQualifiedName} entityContainer this FunctionImport is contained in
   * @param {string} functionImportName name of function import
   * @returns {?CsdlFunctionImport} FunctionImport for given name
   */
  // eslint-disable-next-line no-unused-vars
  getFunctionImport (entityContainer, functionImportName) {
    return null
  }

  /**
   * This method should return the entity container info for the container name,
   * or null if not found
   *
   * @param {?FullQualifiedName} entityContainerName (null for default container)
   * @returns {?CsdlEntityContainerInfo} EntityContainerInfo for given name
   */
  // eslint-disable-next-line no-unused-vars
  getEntityContainerInfo (entityContainerName) {
    return null
  }

  /**
   * This method should return the list of defined aliases
   *
   * @returns {?(CsdlAliasInfo[])} AliasInfos
   */
  getAliasInfos () {
    return null
  }

  /**
   * This method should return the list of defined schemas
   *
   * @returns {?(CsdlSchema[])} Schemas
   */
  getSchemas () {
    return null
  }

  /**
   * This method should return the entity container for the container name,
   * or null if not found
   *
   * @param {FullQualifiedName} entityContainerName
   * @returns {?CsdlEntityContainer}
   */
  // eslint-disable-next-line no-unused-vars
  getEntityContainer (entityContainerName) {
    return null
  }

  /**
   * This method should return the term described by container and name,
   * or null if not found
   *
   * @param {FullQualifiedName} termName Term name
   * @returns {?CsdlTerm}
   */
  // eslint-disable-next-line no-unused-vars
  getTerm (termName) {
    return null
  }
}

module.exports = CsdlProvider
