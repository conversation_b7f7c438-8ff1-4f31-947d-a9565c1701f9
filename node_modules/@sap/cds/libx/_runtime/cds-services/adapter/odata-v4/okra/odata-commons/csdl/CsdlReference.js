'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752504">
 *     OData CSDL # 3.3 Element edmx:Reference
 * </a>
 */
class CsdlReference {
  /**
   * @param {string} uri - OData CSDL # 3.3.1 Attribute Uri
   */
  constructor (uri) {
    validateThat('uri', uri)
      .truthy()
      .typeOf('string')

    /**
     * OData CSDL # 3.3.1 Attribute Uri
     * @type {string}
     */
    this.uri = uri

    /**
     * OData CSDL # 3.4 Element edmx:Include
     * @type {CsdlInclude[]}
     */
    this.includes = []

    /**
     * OData CSDL # 3.5 Element edmx:IncludeAnnotations
     * @type {CsdlIncludeAnnotations[]}
     */
    this.includeAnnotations = []

    /**
     * OData CSDL # 14.3 Element edm:Annotation
     * @type {CsdlAnnotation[]}
     */
    this.annotations = []
  }

  /**
   * Sets a list of Include elements for this reference.
   * OData CSDL # 3.4 Element edmx:Include
   *
   * @param {CsdlInclude[]} includes - list of Include elements for this reference
   * @returns {CsdlReference} this instance
   */
  setIncludes (includes) {
    validateThat('includes', includes)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.includes = includes
    return this
  }

  /**
   * Sets a list of IncludeAnnotations elements for this reference.
   * OData CSDL # 3.5 Element edmx:IncludeAnnotations
   *
   * @param {CsdlIncludeAnnotations[]} includeAnnotations - list of IncludeAnnotations elements
   * for this reference
   * @returns {CsdlReference} this instance
   */
  setIncludeAnnotations (includeAnnotations) {
    validateThat('includeAnnotations', includeAnnotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.includeAnnotations = includeAnnotations
    return this
  }

  /**
   * Sets a list of annotations for the reference.
   * OData CSDL # 14.3 Element edm:Annotation
   *
   * @param {CsdlAnnotation[]} annotations - annotations for the reference
   * @returns {CsdlReference} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.annotations = annotations
    return this
  }
}

module.exports = CsdlReference
