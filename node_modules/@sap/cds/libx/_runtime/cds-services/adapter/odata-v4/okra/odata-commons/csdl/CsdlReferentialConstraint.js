'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752543">
 *     OData CSDL # 7.2 Element edm:ReferentialConstraint
 * </a>
 */
class CsdlReferentialConstraint {
  /**
   * @param {string} property - OData CSDL # 7.2.1 Attribute Property
   * @param {string} referencedProperty - OData CSDL # 7.2.2 Attribute ReferencedProperty
   */
  constructor (property, referencedProperty) {
    validateThat('property', property)
      .truthy()
      .typeOf('string')
    validateThat('referencedProperty', referencedProperty)
      .truthy()
      .typeOf('string')

    /**
     * OData CSDL # 7.2.1 Attribute Property
     * @type {string}
     */
    this.property = property

    /**
     * OData CSDL # 7.2.2 Attribute ReferencedProperty
     * @type {string}
     */
    this.referencedProperty = referencedProperty

    /**
     * OData CSDL # 14.3 Element edm:Annotation
     * @type {CsdlAnnotation[]}
     */
    this.annotations = []
  }

  /**
   * Sets a list of annotations
   * OData CSDL # 14.3 Element edm:Annotation
   *
   * @param {CsdlAnnotation[]} annotations - annotations for this referential constraint
   * @returns {CsdlReferentialConstraint} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.annotations = annotations
    return this
  }
}

module.exports = CsdlReferentialConstraint
