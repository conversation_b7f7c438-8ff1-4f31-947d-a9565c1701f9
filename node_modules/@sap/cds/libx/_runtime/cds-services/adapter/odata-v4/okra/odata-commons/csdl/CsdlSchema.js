'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * OData CSDL # 5.1 Element edm:Schema
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752521">
 *     5.1 Element edm:Schema
 * </a>
 */
class CsdlSchema {
  /**
   * @param {string} namespace - OData CSDL # 5.1.1 Attribute Namespace
   */
  constructor (namespace) {
    validateThat('namespace', namespace)
      .truthy()
      .typeOf('string')

    /**
     * Entity container
     * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752597">
     *     OData CSDL # 13.1 Element edm:EntityContainer
     * </a>
     * @type {CsdlEntityContainer}
     */
    this.entityContainer = null

    /**
     * Namespace
     * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752522">
     *     OData CSDL # 5.1.1 Attribute Namespace
     * </a>
     * @type {string}
     */
    this.namespace = namespace

    /**
     * Alias
     * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752523">
     *     * OData CSDL # 5.1.2 Attribute Alias
     * </a>
     * @type {string}
     */
    this.alias = null

    /**
     * List of enumeration types
     * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752566">
     *     OData CSDL # 10.1 Element edm:EnumType
     * </a>
     *
     * @type {CsdlEnumType[]}
     */
    this.enumTypes = []

    /**
     * List of type definitions
     * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752574">
     *     OData CSDL # 11.1 Element edm:TypeDefinition
     * </a>
     *
     * @type {CsdlTypeDefinition[]}
     */
    this.typeDefinitions = []

    /**
     * List of entity types
     * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752549">
     *     OData CSDL # 8.1 Element edm:EntityType
     * </a>
     * @type {CsdlEntityType[]}
     */
    this.entityTypes = []

    /**
     * List of complex types
     * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752560">
     *     OData CSDL # 9.1 Element edm:ComplexType
     *  </a>
     * @type {CsdlComplexType[]}
     */
    this.complexTypes = []

    /**
     * List of actions
     * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752579">
     *     OData CSDL # 12.1 Element edm:Action
     * </a>
     * @type {CsdlAction[]}
     */
    this.actions = []

    /**
     * List of functions
     * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752583">
     *     OData CSDL # 12.1 Element edm:Function
     *  </a>
     * @type {CsdlFunction[]}
     */
    this.functions = []

    /**
     * List of terms
     * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752620">
     *     OData CSDL # 14.1 Element edm:Term
     * </a>
     * @type {CsdlTerm[]}
     */
    this.terms = []

    /**
     * List of annotation groups
     * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752627">
     *     OData CSDL # 14.2 Element edm:Annotations
     * </a>
     * @type {CsdlAnnotations[]}
     */
    this.externalAnnotations = []

    /**
     * List of annotations
     * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752627">
     *     OData CSDL # 14.2 Element edm:Annotations
     * </a>
     * @type {CsdlAnnotation[]}
     */
    this.annotations = []
  }

  /**
   * Sets entity container.
   * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752597">
   *     OData CSDL # 13.1 Element edm:EntityContainer
   * </a>
   *
   * @param {CsdlEntityContainer} entityContainer Entity container
   * @returns {CsdlSchema} this instance
   */
  setEntityContainer (entityContainer) {
    validateThat('entityContainer', entityContainer)
      .notNullNorUndefined()
      .instanceOf(Object)
    this.entityContainer = entityContainer
    return this
  }

  /**
   * Sets alias.
   * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752523">
   *     * OData CSDL # 5.1.2 Attribute Alias
   * </a>
   *
   * @param {string} alias Alias
   * @returns {CsdlSchema} this instance
   */
  setAlias (alias) {
    validateThat('alias', alias)
      .truthy()
      .typeOf('string')
    this.alias = alias
    return this
  }

  /**
   * Sets enumeration types.
   * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752566">
   *     OData CSDL # 10.1 Element edm:EnumType
   * </a>
   *
   * @param{CsdlEnumType[]} enumTypes enumeration types
   * @returns {CsdlSchema} this instance
   */
  setEnumTypes (enumTypes) {
    validateThat('enumTypes', enumTypes)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)
    this.enumTypes = enumTypes
    return this
  }

  /**
   * Sets type definitions.
   * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752574">
   *     OData CSDL # 11.1 Element edm:TypeDefinition
   * </a>
   *
   * @param{CsdlTypeDefinition[]} typeDefinitions Type definitions
   * @returns {CsdlSchema} this instance
   */
  setTypeDefinitions (typeDefinitions) {
    validateThat('typeDefinitions', typeDefinitions)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)
    this.typeDefinitions = typeDefinitions
    return this
  }

  /**
   * Sets entity types.
   * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752549">
   *     OData CSDL # 8.1 Element edm:EntityType
   * </a>
   *
   * @param {CsdlEntityType[]} entityTypes Entity types
   * @returns {CsdlSchema} this instance
   */
  setEntityTypes (entityTypes) {
    validateThat('entityTypes', entityTypes)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)
    this.entityTypes = entityTypes
    return this
  }

  /**
   * Sets complex types.
   * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752560">
   *     OData CSDL # 9.1 Element edm:ComplexType
   *  </a>
   *
   * @param {CsdlComplexType[]} complexTypes Complex types
   * @returns {CsdlSchema} this instance
   */
  setComplexTypes (complexTypes) {
    validateThat('complexTypes', complexTypes)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)
    this.complexTypes = complexTypes
    return this
  }

  /**
   * Sets actions.
   * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752579">
   *     OData CSDL # 12.1 Element edm:Action
   * </a>
   *
   * @param {CsdlAction[]} actions Actions
   * @returns {CsdlSchema} this instance
   */
  setActions (actions) {
    validateThat('actions', actions)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)
    this.actions = actions
    return this
  }

  /**
   * Sets functions.
   * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752583">
   *     OData CSDL # 12.1 Element edm:Function
   *  </a>
   *
   * @param {CsdlFunction[]} functions Functions
   * @returns {CsdlSchema} this instance
   */
  setFunctions (functions) {
    validateThat('functions', functions)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)
    this.functions = functions
    return this
  }

  /**
   * Sets terms.
   * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752620">
   *     OData CSDL # 14.1 Element edm:Term
   * </a>
   *
   * @param {CsdlTerm[]} terms the List of terms
   * @returns {CsdlSchema} this instance
   */
  setTerms (terms) {
    validateThat('terms', terms)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)
    this.terms = terms
    return this
  }

  /**
   * Sets a list of annotations
   * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752627">
   *     OData CSDL # 14.2 Element edm:Annotations
   * </a>
   *
   * @param {CsdlAnnotations[]} externalAnnotations List of annotations
   * @returns {CsdlSchema} this instance
   */
  setExternalAnnotations (externalAnnotations) {
    validateThat('externalAnnotations', externalAnnotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)
    this.externalAnnotations = externalAnnotations
    return this
  }

  /**
   * Sets a list of annotations
   * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752627">
   *     OData CSDL # 14.2 Element edm:Annotations
   * </a>
   *
   * @param {CsdlAnnotation[]} annotations List of annotations
   * @returns {CsdlSchema} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)
    this.annotations = annotations
    return this
  }
}

module.exports = CsdlSchema
