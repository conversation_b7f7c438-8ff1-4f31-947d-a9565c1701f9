'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752604">
 *     OData CSDL # 13.3 Element edm:Singleton
 * </a>
 */
class CsdlSingleton {
  /**
   * @param {string} name - OData CSDL # 13.3.1 Attribute Name
   * @param {FullQualifiedName} type - OData CSDL # 13.3.2 Attribute Type
   */
  constructor (name, type) {
    validateThat('name', name)
      .truthy()
      .typeOf('string')
    validateThat('type', type)
      .truthy()
      .instanceOf(Object)

    /**
     * OData CSDL # 13.3.1 Attribute Name
     * @type {string}
     */
    this.name = name

    /**
     * OData CSDL # 13.3.2 Attribute Type
     * @type {FullQualifiedName}
     */
    this.type = type

    /**
     * OData CSDL # 13.4 Element edm:NavigationPropertyBinding
     * @type {CsdlNavigationPropertyBinding[]}
     */
    this.navigationPropertyBindings = []

    /**
     * OData CSDL # 14.3 Element edm:Annotation
     * @type {CsdlAnnotation[]}
     */
    this.annotations = []
  }

  /**
   * Sets navigation property bindings.
   * OData CSDL # 13.4 Element edm:NavigationPropertyBinding
   *
   * @param  {CsdlNavigationPropertyBinding[]} navigationPropertyBindings - navigation property
   *  bindings for this singleton
   * @returns {CsdlSingleton} this instance
   */
  setNavigationPropertyBindings (navigationPropertyBindings) {
    validateThat('navigationPropertyBindings', navigationPropertyBindings)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.navigationPropertyBindings = navigationPropertyBindings
    return this
  }

  /**
   * Sets annotations for this singleton.
   * OData CSDL # 14.3 Element edm:Annotation
   *
   * @param {CsdlAnnotation[]} annotations - annotations for this singleton
   * @returns {CsdlSingleton} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.annotations = annotations
    return this
  }
}

module.exports = CsdlSingleton
