'use strict'

const IllegalArgumentError = require('../errors/IllegalArgumentError')
const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752620">
 *     OData CSDL # 14.1 Element edm:Term
 * </a>
 */
class CsdlTerm {
  /**
   *
   * @param {string} name the term's name
   * @param {FullQualifiedName} type type of the value which must be returned by the
   *  expression contained in an annotation using this term
   */
  constructor (name, type) {
    validateThat('name', name)
      .truthy()
      .typeOf('string')
    validateThat('type', type)
      .truthy()
      .instanceOf(Object)

    /**
     * The name
     * [OData CSDL] 14.1.1 Attribute Name
     * @type {string}
     */
    this.name = name

    /**
     * [OData CSDL] 14.1.5 Attribute AppliesTo
     * @type {string[]}
     */
    this.appliesTo = null

    /**
     * The type of the term. Must be a full qualified type name.
     * [OData CSDL] 14.1.2 Attribute Type
     * @type {FullQualifiedName}
     */
    this.type = type

    /**
     * Base term
     * [OData CSDL] 14.1.3 Attribute BaseTerm
     * @type {FullQualifiedName}
     */
    this.baseTerm = null

    /**
     * [OData CSDL] 14.1.4 Attribute DefaultValue
     * @type {string}
     */
    this.defaultValue = null

    /**
     * Indicates whether the term represents a collection
     * @type {boolean}
     */
    this.isCollection = false

    // Facets

    /**
     * [OData CSDL] 6.2.1 Attribute Nullable
     * @type {boolean}
     */
    this.isNullable = true

    /**
     * [OData CSDL] 6.2.2 Attribute MaxLength
     * @type {number}
     */
    this.maxLength = null

    /**
     * [OData CSDL] 6.2.3 Attribute Precision
     * @type {number}
     */
    this.precision = null

    /**
     * [OData CSDL] 6.2.4 Attribute Scale
     * @type {number}
     */
    this.scale = null

    /**
     * [OData CSDL] 6.2.6 Attribute SRID
     * @type {number|string}
     */
    this.srid = null

    /**
     * OData CSDL # 14.3 Element edm:Annotation
     * @type {CsdlAnnotation[]}
     */
    this.annotations = []
  }

  /**
   * Sets name
   * [OData CSDL] 14.1.1 Attribute Name
   *
   * @param {string} name the name
   * @returns {CsdlTerm} this instance
   */
  setName (name) {
    validateThat('name', name)
      .truthy()
      .typeOf('string')
    this.name = name
    return this
  }

  /**
   * Sets a list of CSDL element names to which this term applies
   * OData CSDL # 14.1.5 Attribute AppliesTo
   *
   * @param {string[]} appliesTo List of CSDL element names
   * @returns {CsdlTerm} this instance
   */
  setAppliesTo (appliesTo) {
    validateThat('appliesTo', appliesTo)
      .notNullNorUndefined()
      .array()
      .containsElementsOfType('string')

    this.appliesTo = appliesTo
    return this
  }

  /**
   * Sets type of the term
   * [OData CSDL] 14.1.2 Attribute Type
   *
   * @param {FullQualifiedName} type the full qualified type name
   * @returns {CsdlTerm} this instance
   */
  setType (type) {
    validateThat('type', type)
      .truthy()
      .instanceOf(Object)
    this.type = type
    return this
  }

  /**
   * Sets type
   * [OData CSDL] 14.1.3 Attribute BaseTerm
   *
   * @param {FullQualifiedName} baseTerm the full qualified type name
   * @returns {CsdlTerm} this instance
   */
  setBaseTerm (baseTerm) {
    validateThat('baseTerm', baseTerm)
      .truthy()
      .instanceOf(Object)
    this.baseTerm = baseTerm
    return this
  }

  /**
   * Sets default value
   * [OData CSDL] 6.2.7 Attribute DefaultValue
   *
   * @param {string} defaultValue the default value
   * @returns {CsdlTerm} this instance
   */
  setDefaultValue (defaultValue) {
    validateThat('defaultValue', defaultValue).notNullNorUndefined()
    this.defaultValue = defaultValue
    return this
  }

  /**
   * Sets whether the property represents a collection
   *
   * @param {boolean} isCollection - indicates, whether the property should represent a collection
   * @returns {CsdlTerm} this instance
   */
  setCollection (isCollection) {
    validateThat('isCollection', isCollection)
      .notNullNorUndefined()
      .typeOf('boolean')

    this.isCollection = isCollection
    return this
  }

  /**
   * OData CSDL # 7.1.3 Attribute Nullable
   *
   * @param {boolean} isNullable - indicates whether the term property is nullable
   * @returns {CsdlTerm} this instance
   */
  setNullable (isNullable) {
    validateThat('isNullable', isNullable)
      .notNullNorUndefined()
      .typeOf('boolean')
    this.isNullable = isNullable
    return this
  }

  /**
   * Sets max length
   * [OData CSDL] 6.2.2 Attribute MaxLength
   *
   * @param {number|string} maxLength the max length
   * @returns {CsdlTerm} this instance
   */
  setMaxLength (maxLength) {
    const validate = validateThat('maxLength', maxLength).notNullNorUndefined()

    // check if the special 'max' value is specified
    if (typeof maxLength === 'string') {
      // check whether a string value is specified but not the allowed one
      if (maxLength !== 'max') {
        throw IllegalArgumentError.createForIllegalTypeValue('maxLength', 'string', 'max')
      }

      this.maxLength = 'max'
      return this
    }

    // otherwise the value must be a positive integer
    validate.integer().positiveInteger()

    this.maxLength = maxLength
    return this
  }

  /**
   * Sets precision
   * [OData CSDL] 6.2.3 Attribute Precision
   *
   * @param {number} precision the precision
   * @returns {CsdlTerm} this instance
   */
  setPrecision (precision) {
    validateThat('precision', precision)
      .notNullNorUndefined()
      .integer()
      .nonNegativeInteger()

    this.precision = precision
    return this
  }

  /**
   * Sets scale
   * [OData CSDL] 6.2.4 Attribute Scale
   *
   * @param {number|string} scale the scale
   * @returns {CsdlTerm} this instance
   */
  setScale (scale) {
    const validate = validateThat('scale', scale).notNullNorUndefined()

    // check if the special 'variable' value is specified
    if (typeof scale === 'string') {
      // check whether a string value is specified but not the allowed one
      if (scale !== 'variable') {
        throw IllegalArgumentError.createForIllegalTypeValue('scale', 'string', 'variable')
      }

      this.scale = 'variable'
      return this
    }

    // otherwise the value must be a non-negative integer
    validate.integer().nonNegativeInteger()

    this.scale = scale
    return this
  }

  /**
   * Sets 'SRID' facet.
   * OData CSDL # 6.2.6 Attribute SRID.
   * @param {number|string} srid value of the 'SRID' facet; it must be either a
   *                             non-negative integer value or a special string 'variable'
   * @returns {CsdlTerm} this instance
   */
  setSrid (srid) {
    const validate = validateThat('srid', srid).notNullNorUndefined()
    if (typeof srid === 'string') {
      if (srid !== 'variable') {
        throw IllegalArgumentError.createForIllegalTypeValue('srid', 'string', 'variable')
      }
    } else {
      validate.integer().nonNegativeInteger()
    }

    this.srid = srid
    return this
  }

  /**
   * Sets a list of annotations
   * OData CSDL # 14.3 Element edm:Annotation
   *
   * @param {CsdlAnnotation[]} annotations - annotations for the term
   * @returns {CsdlTerm} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.annotations = annotations
    return this
  }
}

module.exports = CsdlTerm
