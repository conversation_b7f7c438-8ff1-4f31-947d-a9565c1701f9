'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat
const IllegalArgumentError = require('../errors/IllegalArgumentError')

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752574">
 *     OData CSDL # 11.1 Element edm:TypeDefinition
 * </a>
 */
class CsdlTypeDefinition {
  /**
   * @param {string} name - OData CSDL # 11.1.1 Attribute Name
   * @param {FullQualifiedName} underlyingType - OData CSDL # 11.1.2 Attribute UnderlyingType
   */
  constructor (name, underlyingType) {
    validateThat('name', name)
      .truthy()
      .typeOf('string')
    validateThat('underlyingType', underlyingType)
      .truthy()
      .instanceOf(Object)

    /**
     * OData CSDL # 11.1.1 Attribute Name
     * @type {string}
     */
    this.name = name

    /**
     * OData CSDL # 11.1.2 Attribute UnderlyingType
     * @type {FullQualifiedName}
     */
    this.underlyingType = underlyingType

    // Facets
    /**
     * OData CSDL # 6.2.2 Attribute MaxLength
     * @type {number | string}
     */
    this.maxLength = null

    /**
     * OData CSDL # 6.2.3 Attribute Precision
     * @type {number}
     */
    this.precision = null

    /**
     * OData CSDL # 6.2.4 Attribute Scale
     * @type {number | string}
     */
    this.scale = null

    /**
     * OData CSDL # 6.2.6 Attribute SRID
     * @type {number|string}
     */
    this.srid = null

    /**
     * OData CSDL # # 14.3 Element edm:Annotation
     * @type {CsdlAnnotation[]}
     */
    this.annotations = []
  }

  /**
   * Sets 'MaxLength' facet.
   * OData CSDL # 6.2.2 Attribute MaxLength
   *
   * @param {number | string} maxLength - value of the 'MaxLength' facet. It must be either a
   * positive integer value or a special 'max' string value. More information can be found in the
   * CSDL specification.
   * @returns {CsdlTypeDefinition} this instance
   */
  setMaxLength (maxLength) {
    const validate = validateThat('maxLength', maxLength).notNullNorUndefined()

    // check if the special 'max' value is specified
    if (typeof maxLength === 'string') {
      // check whether a string value is specified but not the allowed one
      if (maxLength !== 'max') {
        throw IllegalArgumentError.createForIllegalTypeValue('maxLength', 'string', 'max')
      }

      this.maxLength = 'max'
      return this
    }

    // otherwise the value must be a positive integer
    validate.integer().positiveInteger()

    this.maxLength = maxLength
    return this
  }

  /**
   * Sets Precision facet.
   * OData CSDL # 6.2.3 Attribute Precision
   *
   * @param {number} precision - value of the Precision facet.
   * @returns {CsdlTypeDefinition} this instance
   */
  setPrecision (precision) {
    validateThat('precision', precision)
      .notNullNorUndefined()
      .integer()
      .nonNegativeInteger()

    this.precision = precision
    return this
  }

  /**
   * Sets 'Scale' facet.
   * OData CSDL # 6.2.4 Attribute Scale
   *
   * @param {number | string} scale - value of the 'Scale' facet. It must be either a
   * non-negative integer value or a special 'variable' string value. More information can be
   * found in the CSDL specification.
   * @returns {CsdlTypeDefinition} this instance
   */
  setScale (scale) {
    const validate = validateThat('scale', scale).notNullNorUndefined()

    // check if the special 'variable' value is specified
    if (typeof scale === 'string') {
      // check whether a string value is specified but not the allowed one
      if (scale !== 'variable') {
        throw IllegalArgumentError.createForIllegalTypeValue('scale', 'string', 'variable')
      }

      this.scale = 'variable'
      return this
    }

    // otherwise the value must be a non-negative integer
    validate.integer().nonNegativeInteger()

    this.scale = scale
    return this
  }

  /**
   * OData CSDL # 6.2.5 Attribute Unicode.
   * The property always has value 'true'. Modification of the property value is not supported.
   * @type {boolean}
   */
  get unicode () {
    return true
  }

  /**
   * Sets 'SRID' facet.
   * OData CSDL # 6.2.6 Attribute SRID.
   * @param {number|string} srid value of the 'SRID' facet; it must be either a
   *                             non-negative integer value or a special string 'variable'
   * @returns {CsdlTypeDefinition} this instance
   */
  setSrid (srid) {
    const validate = validateThat('srid', srid).notNullNorUndefined()
    if (typeof srid === 'string') {
      if (srid !== 'variable') {
        throw IllegalArgumentError.createForIllegalTypeValue('srid', 'string', 'variable')
      }
    } else {
      validate.integer().nonNegativeInteger()
    }

    this.srid = srid
    return this
  }

  /**
   * Sets annotations for the type definition.
   * OData CSDL # 14.3 Element edm:Annotation
   *
   * @param {CsdlAnnotation[]} annotations - annotations for the type definition
   * @returns {CsdlTypeDefinition} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.annotations = annotations
    return this
  }
}

module.exports = CsdlTypeDefinition
