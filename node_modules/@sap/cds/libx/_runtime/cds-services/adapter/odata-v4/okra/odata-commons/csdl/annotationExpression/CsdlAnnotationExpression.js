'use strict'

const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752619">
 *     OData CSDL # 14 Vocabulary and Annotation
 * </a>
 */

class CsdlAnnotationExpression {
  /**
   * @param {CsdlAnnotationExpression.kinds} kind the kind of the annotation expression
   */
  constructor (kind) {
    this.kind = kind

    /**
     * OData CSDL # 14.3 Element edm:Annotation
     * @type {CsdlAnnotation[]}
     */
    this.annotations = []
  }

  /**
   * Sets a list of annotations
   * OData CSDL # 14.3 Element edm:Annotation
   *
   * @param {CsdlAnnotation[]} annotations - annotations for this annotation expression
   * @returns {CsdlAnnotationExpression} this instance
   */
  setAnnotations (annotations) {
    validateThat('annotations', annotations)
      .notNullNorUndefined()
      .array()
      .containsInstancesOf(Object)

    this.annotations = annotations
    return this
  }
}

/**
 * @enum {string}
 * @readonly
 */
CsdlAnnotationExpression.kinds = {
  AnnotationPath: 'AnnotationPath',
  Apply: 'Apply',
  Arithmetic: 'Arithmetic',
  Binary: 'Binary',
  Cast: 'Cast',
  Collection: 'Collection',
  Constant: 'Constant',
  If: 'If',
  IsOf: 'IsOf',
  LabeledElement: 'LabeledElement',
  LabeledElementReference: 'LabeledElementReference',
  ModelElementPath: 'ModelElementPath',
  NavigationPropertyPath: 'NavigationPropertyPath',
  Negation: 'Negation',
  Not: 'Not',
  Null: 'Null',
  Path: 'Path',
  PropertyPath: 'PropertyPath',
  PropertyValue: 'PropertyValue',
  Record: 'Record',
  UrlRef: 'UrlRef',
  // The UNKNOWN kind is use for unknwon expressions regarding to the OASIS issue 1221
  Unknown: 'UNKNOWN'
}

module.exports = CsdlAnnotationExpression
