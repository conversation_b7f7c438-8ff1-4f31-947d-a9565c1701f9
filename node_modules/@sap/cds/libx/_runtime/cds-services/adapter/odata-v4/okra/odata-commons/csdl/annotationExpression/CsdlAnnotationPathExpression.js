'use strict'

const CsdlAnnotationExpression = require('./CsdlAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 *
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752648">
 *     OData CSDL # 14.5.2 Expression edm:AnnotationPath
 * </a>
 *
 * @extends CsdlAnnotationExpression
 */
class CsdlAnnotationPathExpression extends CsdlAnnotationExpression {
  /**
   * @param {string} annotationPath Annotation path to referenced annotation
   */
  constructor (annotationPath) {
    validateThat('annotationPath', annotationPath)
      .truthy()
      .typeOf('string')
    super(CsdlAnnotationExpression.kinds.AnnotationPath)

    /**
     * OData CSDL # 14.5.2 Expression edm:AnnotationPath
     * @type {string}
     * @private
     */
    this.annotationPath = annotationPath
  }
}

module.exports = CsdlAnnotationPathExpression
