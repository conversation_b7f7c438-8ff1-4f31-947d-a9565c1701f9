'use strict'

const CsdlAnnotationExpression = require('./CsdlAnnotationExpression')
const FullQualifiedName = require('../../FullQualifiedName')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 *
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752649">
 *     OData CSDL # 14.5.3 Expression edm:Apply
 * </a>
 *
 * @extends CsdlAnnotationExpression
 */
class CsdlApplyExpression extends CsdlAnnotationExpression {
  /**
   * @param {FullQualifiedName} functionName Function to be applied on client side (e.g. odata.concat)
   * @param {CsdlAnnotationExpression[]} parameters List with parameters of the function
   */
  constructor (functionName, parameters = []) {
    validateThat('functionName', functionName)
      .truthy()
      .instanceOf(Object)
    if (parameters) {
      validateThat('parameters', parameters)
        .array()
        .containsInstancesOf(Object)
    }

    super(CsdlAnnotationExpression.kinds.Apply)

    /**
     * OData CSDL # 14.5.3.1 Attribute Function
     * @type {FullQualifiedName}
     * @private
     */
    this.functionName = functionName

    /**
     * OData CSDL # # 14.5.3 Expression edm:Apply
     * @type {string}
     * @private
     */
    this.parameters = parameters
  }

  /**
   * Sets the list with the function parameters
   *
   * @param {CsdlAnnotationExpression[]} parameters List with function parameters
   * @returns {CsdlApplyExpression} this instance
   */
  setParameters (parameters) {
    validateThat('parameters', parameters)
      .truthy()
      .array()
      .containsInstancesOf(Object)
    this.parameters = parameters
    return this
  }
}

/**
 * Container for predefined functions
 *
 * @enum {FullQualifiedName}
 * @readonly
 */
CsdlApplyExpression.Functions = {
  FillUriTemplate: new FullQualifiedName('odata', 'fillUriTemplate'),
  UriEncode: new FullQualifiedName('odata', 'uriEncode'),
  // All canonical functions defined in [OData-URL] can be used as
  // client-side functions, qualified with the namespace odata.
  Contains: new FullQualifiedName('odata', 'contains'),
  StartsWith: new FullQualifiedName('odata', 'startswith'),
  EndsWith: new FullQualifiedName('odata', 'endswith'),
  Length: new FullQualifiedName('odata', 'length'),
  IndexOf: new FullQualifiedName('odata', 'indexof'),
  Substring: new FullQualifiedName('odata', 'substring'),
  ToLower: new FullQualifiedName('odata', 'tolower'),
  ToUpper: new FullQualifiedName('odata', 'toupper'),
  Trim: new FullQualifiedName('odata', 'trim'),
  Concat: new FullQualifiedName('odata', 'concat'),
  Year: new FullQualifiedName('odata', 'year'),
  Month: new FullQualifiedName('odata', 'month'),
  Day: new FullQualifiedName('odata', 'day'),
  Hour: new FullQualifiedName('odata', 'hour'),
  Minute: new FullQualifiedName('odata', 'minute'),
  Second: new FullQualifiedName('odata', 'second'),
  FractionalSeconds: new FullQualifiedName('odata', 'fractionalseconds'),
  TotalSeconds: new FullQualifiedName('odata', 'totalseconds'),
  Date: new FullQualifiedName('odata', 'date'),
  Time: new FullQualifiedName('odata', 'time'),
  TotalOffsetMinutes: new FullQualifiedName('odata', 'totaloffsetminutes'),
  MinDateTime: new FullQualifiedName('odata', 'mindatetime'),
  MaxDateTime: new FullQualifiedName('odata', 'maxdatetime'),
  Now: new FullQualifiedName('odata', 'now'),
  Round: new FullQualifiedName('odata', 'round'),
  Floor: new FullQualifiedName('odata', 'floor'),
  Ceiling: new FullQualifiedName('odata', 'ceiling'),
  Cast: new FullQualifiedName('odata', 'cast'),
  IsOf: new FullQualifiedName('odata', 'isof')
}

module.exports = CsdlApplyExpression
