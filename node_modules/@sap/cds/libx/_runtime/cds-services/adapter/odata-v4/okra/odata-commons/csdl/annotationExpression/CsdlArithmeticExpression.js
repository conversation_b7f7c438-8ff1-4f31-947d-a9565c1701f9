'use strict'

const CsdlAnnotationExpression = require('./CsdlAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 * @extends CsdlAnnotationExpression
 */
class CsdlArithmeticExpression extends CsdlAnnotationExpression {
  /**
   * @param {CsdlArithmeticExpression.Operators} operator operator
   * @param {CsdlAnnotationExpression} left Left operand, must evaluate to a numeric type
   * @param {CsdlAnnotationExpression} right Right operand, must evaluate to a numeric type
   */
  constructor (operator, left, right) {
    validateThat('operator', operator)
      .truthy()
      .typeOf('string')
    validateThat('left', left)
      .truthy()
      .instanceOf(Object)
    validateThat('right', right)
      .truthy()
      .instanceOf(Object)

    super(CsdlAnnotationExpression.kinds.Arithmetic)

    /**
     * Operator
     * @type {CsdlArithmeticExpression.Operators}
     */
    this.operator = operator

    /**
     * Left operand
     * @type {CsdlAnnotationExpression}
     */
    this.left = left

    /**
     * Right operand
     * @type {CsdlAnnotationExpression}
     */
    this.right = right
  }
}

/**
 * @enum {string}
 * @readonly
 */
CsdlArithmeticExpression.Operators = {
  /** Addition */
  Add: 'Add',

  /** Subtraction */
  Sub: 'Sub',

  /** Multiplication */
  Mul: 'Mul',

  /** Division (with integer result for integer operands) */
  Div: 'Div',

  /** Division (with fractional result also for integer operands) */
  DivBy: 'DivBy',

  /** Modulo */
  Mod: 'Mod'
}

module.exports = CsdlArithmeticExpression
