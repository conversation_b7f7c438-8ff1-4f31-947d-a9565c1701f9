'use strict'

const CsdlAnnotationExpression = require('./CsdlAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 *
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752647">
 *     OData CSDL # 14.5.1 Comparison and Logical Operators
 * </a>
 *
 * @extends CsdlAnnotationExpression
 */
class CsdlBinaryExpression extends CsdlAnnotationExpression {
  /**
   * @param {CsdlBinaryExpression.ComparisonOperators} operator Comparison or logical Operator
   * @param {CsdlAnnotationExpression} left Left operand, must evaluate to type Edm.Boolean
   * @param {CsdlAnnotationExpression} right Right operand, must evaluate to type Edm.Boolean
   */
  constructor (operator, left, right) {
    validateThat('operator', operator)
      .truthy()
      .typeOf('string')
    validateThat('left', left)
      .truthy()
      .instanceOf(Object)
    validateThat('right', right)
      .truthy()
      .instanceOf(Object)

    super(CsdlAnnotationExpression.kinds.Binary)

    /**
     * Operator
     * @type {CsdlBinaryExpression.ComparisonOperators|CsdlBinaryExpression.LogicalOperators}
     */
    this.operator = operator

    /**
     * Left operand
     * @type {CsdlAnnotationExpression}
     */
    this.left = left

    /**
     * Right operand
     * @type {CsdlAnnotationExpression}
     */
    this.right = right
  }
}

/**
 * @enum {string}
 * @readonly
 */
CsdlBinaryExpression.ComparisonOperators = {
  /**
   * Comparison expression 'equality'
   */
  Eq: 'Eq',

  /**
   * Comparison expression 'unequally'
   */
  Ne: 'Ne',

  /**
   * Comparison expression 'greater than'
   */
  Gt: 'Gt',

  /**
   * Comparison expression 'greater than or equal'
   */
  Ge: 'Ge',

  /**
   * Comparison expression 'less than'
   */
  Lt: 'Lt',

  /**
   * Comparison expression 'less than or equal'
   */
  Le: 'Le',

  /**
   * Comparison expression 'is in collection'
   */
  In: 'In'
}

/**
 * @enum {string}
 * @readonly
 */
CsdlBinaryExpression.LogicalOperators = {
  /**
   * Logical 'and' expression
   */
  And: 'And',

  /**
   * Logical 'or' expression
   */
  Or: 'Or'
}

module.exports = CsdlBinaryExpression
