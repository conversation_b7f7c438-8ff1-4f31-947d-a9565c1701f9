'use strict'

const CsdlAnnotationExpression = require('./CsdlAnnotationExpression')
const IllegalArgumentError = require('../../errors/IllegalArgumentError')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 *
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752650">
 *     OData CSDL # 14.5.4 Expression edm:Cast
 * </a>
 *
 * @extends CsdlAnnotationExpression
 */
class CsdlCastExpression extends CsdlAnnotationExpression {
  /**
   * @param {FullQualifiedName} type Target type of the cast
   * @param {CsdlAnnotationExpression} expression Expression to be casted
   */
  constructor (type, expression) {
    validateThat('type', type)
      .truthy()
      .instanceOf(Object)
    validateThat('expression', expression)
      .truthy()
      .instanceOf(Object)

    super(CsdlAnnotationExpression.kinds.Cast)

    /**
     * Target type of the cast
     * @type {FullQualifiedName}
     */
    this.type = type

    /**
     * Expression to be casted
     * @type {CsdlAnnotationExpression}
     */
    this.expression = expression

    /**
     * OData CSDL # 6.2.2 Attribute MaxLength
     * @type {number | string}
     */
    this.maxLength = null

    /**
     * OData CSDL # 6.2.3 Attribute Precision
     * @type {number}
     */
    this.precision = null

    /**
     * OData CSDL # 6.2.4 Attribute Scale
     * @type {number | string}
     */
    this.scale = null

    /**
     * OData CSDL # 6.2.6 Attribute SRID
     * @type {number|string}
     */
    this.srid = null

    /**
     * Indicates whether the type is a collection type
     * @type {boolean}
     */
    this.isCollection = false
  }

  /**
   * Sets 'MaxLength' facet.
   * OData CSDL # 6.2.2 Attribute MaxLength
   *
   * @param {number | string} maxLength - value of the 'MaxLength' facet. It must be either a
   * positive integer value or a special 'max' string value. More information can be found in the
   * CSDL specification.
   * @returns {CsdlCastExpression} this instance
   */
  setMaxLength (maxLength) {
    const validate = validateThat('maxLength', maxLength).notNullNorUndefined()

    // check if the special 'max' value is specified
    if (typeof maxLength === 'string') {
      // check whether a string value is specified but not the allowed one
      if (maxLength !== 'max') {
        throw IllegalArgumentError.createForIllegalTypeValue('maxLength', 'string', 'max')
      }

      this.maxLength = 'max'
      return this
    }

    // otherwise the value must be a positive integer
    validate.integer().positiveInteger()

    this.maxLength = maxLength
    return this
  }

  /**
   * Sets Precision facet.
   * OData CSDL # 6.2.3 Attribute Precision
   *
   * @param {number} precision - value of the Precision facet.
   * @returns {CsdlCastExpression} this instance
   */
  setPrecision (precision) {
    validateThat('precision', precision)
      .notNullNorUndefined()
      .integer()
      .nonNegativeInteger()

    this.precision = precision
    return this
  }

  /**
   * Sets 'Scale' facet.
   * OData CSDL # 6.2.4 Attribute Scale
   *
   * @param {number | string} scale - value of the 'Scale' facet. It must be either a
   * non-negative integer value or a special 'variable' string value. More information can be
   * found in the CSDL specification.
   * @returns {CsdlCastExpression} this instance
   */
  setScale (scale) {
    const validate = validateThat('scale', scale).notNullNorUndefined()

    // check if the special 'variable' value is specified
    if (typeof scale === 'string') {
      // check whether a string value is specified but not the allowed one
      if (scale !== 'variable') {
        throw IllegalArgumentError.createForIllegalTypeValue('scale', 'string', 'variable')
      }

      this.scale = 'variable'
      return this
    }

    // otherwise the value must be a non-negative integer
    validate.integer().nonNegativeInteger()

    this.scale = scale
    return this
  }

  /**
   * Sets 'SRID' facet.
   * OData CSDL # 6.2.6 Attribute SRID.
   * @param {number|string} srid value of the 'SRID' facet; it must be either a
   *                             non-negative integer value or a special string 'variable'
   * @returns {CsdlCastExpression} this instance
   */
  setSrid (srid) {
    const validate = validateThat('srid', srid).notNullNorUndefined()
    if (typeof srid === 'string') {
      if (srid !== 'variable') {
        throw IllegalArgumentError.createForIllegalTypeValue('srid', 'string', 'variable')
      }
    } else {
      validate.integer().nonNegativeInteger()
    }

    this.srid = srid
    return this
  }

  /**
   * Sets whether the navigation property references a collection.
   *
   * @param {boolean} isCollection - indicates whether the cast expression references a
   * collection
   * @returns {CsdlCastExpression} this instance
   */
  setCollection (isCollection) {
    validateThat('isCollection', isCollection)
      .notNullNorUndefined()
      .typeOf('boolean')

    this.isCollection = isCollection
    return this
  }
}

module.exports = CsdlCastExpression
