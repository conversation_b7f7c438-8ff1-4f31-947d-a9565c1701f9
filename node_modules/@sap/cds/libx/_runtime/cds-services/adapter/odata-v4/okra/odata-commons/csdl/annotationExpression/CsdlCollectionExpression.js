'use strict'

const CsdlAnnotationExpression = require('./CsdlAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 *
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752651">
 *     OData CSDL # 14.5.5 Expression edm:Collection
 * </a>
 *
 * @extends CsdlAnnotationExpression
 */
class CsdlCollectionExpression extends CsdlAnnotationExpression {
  /**
   * @param {CsdlAnnotationExpression[]} expressions List with function parameters
   */
  constructor (expressions) {
    if (expressions) {
      validateThat('expressions', expressions)
        .array()
        .containsInstancesOf(Object)
    }
    super(CsdlAnnotationExpression.kinds.Collection)

    /**
     * List of expressions which are part of the collection
     * @type {CsdlAnnotationExpression[]}
     */
    this.expressions = expressions || []
  }

  /**
   * Set the expressions which make up the collection
   *
   * @param {CsdlAnnotationExpression[]} expressions List with collection expression elements
   * @returns {CsdlCollectionExpression} this instance
   */
  setExpressions (expressions) {
    validateThat('expressions', expressions)
      .array()
      .containsInstancesOf(Object)
    this.expressions = expressions
    return this
  }
}

module.exports = CsdlCollectionExpression
