'use strict'

const CsdlAnnotationExpression = require('./CsdlAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752633">
 *     OData CSDL # 14.4 Constant Expressions
 * </a>
 *
 * @extends CsdlAnnotationExpression
 */
class CsdlConstantExpression extends CsdlAnnotationExpression {
  /**
   * @param {CsdlConstantExpression.Types} type Type of the constant expression
   * @param {*} [value] Value of the constant expression
   */
  constructor (type, value) {
    validateThat('type', type)
      .truthy()
      .typeOf('string')

    super(CsdlAnnotationExpression.kinds.Constant)

    /**
     * Type of the constant expression
     * @type {CsdlConstantExpression.Types}
     */
    this.type = type

    /**
     * OData CSDL # 14.4.*
     */
    this.value = value
  }

  /**
   * Sets the value of the constant expression
   *
   * @param {*} value Value of the constant expression
   * @returns {CsdlConstantExpression} this instance
   */
  setValue (value) {
    validateThat('value', value).notNullNorUndefined()
    this.value = value
    return this
  }
}

/**
 * @enum {string}
 * @readonly
 */
CsdlConstantExpression.Types = {
  /**
   * Type Edm.Binary
   */
  Binary: 'edm:Binary',

  /**
   * Type Edm.Bool
   */
  Bool: 'edm:Bool',

  /**
   * Type Edm.Date
   */
  Date: 'edm:Date',

  /**
   * Type Edm.DateTimeOffset
   */
  DateTimeOffset: 'edm:DateTimeOffset',

  /**
   * Type Edm.Decimal
   */
  Decimal: 'edm:Decimal',

  /**
   * Type Edm.Duration
   */
  Duration: 'edm:Duration',

  /**
   * Type Edm.EnumMember
   */
  EnumMember: 'edm:EnumMember',

  /**
   * Type Edm.Float
   */
  Float: 'edm:Float',

  /**
   * Type Edm.Guid
   */
  Guid: 'edm:Guid',

  /**
   * Type Integer
   */
  Int: 'edm:Int',

  /**
   * Type Edm.String
   */
  String: 'edm:String',

  /**
   * Type Edm.TimeOfDay
   */
  TimeOfDay: 'edm:TimeOfDay'
}

module.exports = CsdlConstantExpression
