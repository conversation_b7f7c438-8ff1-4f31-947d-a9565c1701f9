'use strict'

const CsdlAnnotationExpression = require('./CsdlAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 * <a href="OData%20Specification/odata-v4.0-os-part3-csdl.html#_Toc372794041">
 *     OData CSDL # 14.5.6 Expression edm:If
 * </a>
 *
 * @extends CsdlAnnotationExpression
 */
class CsdlIfExpression extends CsdlAnnotationExpression {
  /**
   * @param {CsdlAnnotationExpression} condition Must evaluate to type of boolean
   * @param {CsdlAnnotationExpression} ifTrue Return value if condition evaluates to true
   * @param {CsdlAnnotationExpression} [ifFalse] Return value if condition evaluates to false
   */
  constructor (condition, ifTrue, ifFalse = null) {
    validateThat('condition', condition).instanceOf(Object)
    validateThat('ifTrue', ifTrue).instanceOf(Object)

    if (ifFalse) {
      validateThat('ifFalse', ifFalse).instanceOf(Object)
    }
    super(CsdlAnnotationExpression.kinds.If)

    /**
     * Condition expression which must evaluate to a boolean value
     * @type {CsdlAnnotationExpression}
     */
    this.condition = condition

    /**
     * Expression used if the condition evaluates to true
     * @type {CsdlAnnotationExpression}
     */
    this.ifTrue = ifTrue

    /**
     * Expression used if the condition evaluates to false or null
     * @type {?CsdlAnnotationExpression}
     */
    this.ifFalse = ifFalse
  }

  /**
   * Sets the value which should be returned if the condition evaluates to false
   *
   * @param {CsdlAnnotationExpression} ifFalse Returned expression
   * @returns {CsdlIfExpression} this instance
   */
  setIfFalse (ifFalse) {
    validateThat('ifFalse', ifFalse)
      .truthy()
      .instanceOf(Object)
    this.ifFalse = ifFalse
    return this
  }
}

module.exports = CsdlIfExpression
