'use strict'

const CsdlAnnotationExpression = require('./CsdlAnnotationExpression')
const IllegalArgumentError = require('../../errors/IllegalArgumentError')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752653">
 *     OData CSDL # 14.5.7 Expression edm:IsOf
 * </a>
 *
 * @extends CsdlAnnotationExpression
 */
class CsdlIsOfExpression extends CsdlAnnotationExpression {
  /**
   * @param {FullQualifiedName} type Type used for check
   * @param {CsdlAnnotationExpression} expression Expression to be type checked
   */
  constructor (type, expression) {
    validateThat('type', type)
      .truthy()
      .instanceOf(Object)

    if (expression) {
      validateThat('expression', expression).instanceOf(Object)
    }
    super(CsdlAnnotationExpression.kinds.IsOf)

    /**
     * Type used for check
     * @type {FullQualifiedName}
     */
    this.type = type

    /**
     * Expression to be checked
     * @type {FullQualifiedName}
     */
    this.expression = expression

    /**
     * OData CSDL # 6.2.2 Attribute MaxLength
     * @type {number | string}
     */
    this.maxLength = undefined

    /**
     * OData CSDL # 6.2.3 Attribute Precision
     * @type {number}
     */
    this.precision = undefined

    /**
     * OData CSDL # 6.2.4 Attribute Scale
     * @type {number | string}
     */
    this.scale = undefined

    /**
     * OData CSDL # 6.2.6 Attribute SRID
     * @type {number|string}
     */
    this.srid = null

    /**
     * Indicates whether the type is a collection type
     * @type {boolean}
     */
    this.isCollection = false
  }

  /**
   * Set the expression to be type checked
   *
   * @param {CsdlAnnotationExpression} expression Expression
   * @returns {CsdlIsOfExpression} this instance
   */
  setExpression (expression) {
    validateThat('expression', expression)
      .truthy()
      .instanceOf(Object)
    this.expression = expression
    return this
  }

  /**
   * Sets 'MaxLength' facet.
   * OData CSDL # 6.2.2 Attribute MaxLength
   *
   * @param {number | string} maxLength - value of the 'MaxLength' facet. It must be either a
   * positive integer value or a special 'max' string value. More information can be found in the
   * CSDL specification.
   * @returns {CsdlIsOfExpression} this instance
   */
  setMaxLength (maxLength) {
    const validate = validateThat('maxLength', maxLength).notNullNorUndefined()

    // check if the special 'max' value is specified
    if (typeof maxLength === 'string') {
      // check whether a string value is specified but not the allowed one
      if (maxLength !== 'max') {
        throw IllegalArgumentError.createForIllegalTypeValue('maxLength', 'string', 'max')
      }

      this.maxLength = 'max'
      return this
    }

    // otherwise the value must be a positive integer
    validate.integer().positiveInteger()

    this.maxLength = maxLength
    return this
  }

  /**
   * Sets Precision facet.
   * OData CSDL # 6.2.3 Attribute Precision
   *
   * @param {number} precision - value of the Precision facet.
   * @returns {CsdlIsOfExpression} this instance
   */
  setPrecision (precision) {
    validateThat('precision', precision)
      .notNullNorUndefined()
      .integer()
      .nonNegativeInteger()

    this.precision = precision
    return this
  }

  /**
   * Sets 'Scale' facet.
   * OData CSDL # 6.2.4 Attribute Scale
   *
   * @param {number | string} scale - value of the 'Scale' facet. It must be either a
   * non-negative integer value or a special 'variable' string value. More information can be
   * found in the CSDL specification.
   * @returns {CsdlIsOfExpression} this instance
   */
  setScale (scale) {
    const validate = validateThat('scale', scale).notNullNorUndefined()

    // check if the special 'variable' value is specified
    if (typeof scale === 'string') {
      // check whether a string value is specified but not the allowed one
      if (scale !== 'variable') {
        throw IllegalArgumentError.createForIllegalTypeValue('scale', 'string', 'variable')
      }

      this.scale = 'variable'
      return this
    }

    // otherwise the value must be a non-negative integer
    validate.integer().nonNegativeInteger()

    this.scale = scale
    return this
  }

  /**
   * Sets 'SRID' facet.
   * OData CSDL # 6.2.6 Attribute SRID.
   * @param {number|string} srid value of the 'SRID' facet; it must be either a
   *                             non-negative integer value or a special string 'variable'
   * @returns {CsdlIsOfExpression} this instance
   */
  setSrid (srid) {
    const validate = validateThat('srid', srid).notNullNorUndefined()
    if (typeof srid === 'string') {
      if (srid !== 'variable') {
        throw IllegalArgumentError.createForIllegalTypeValue('srid', 'string', 'variable')
      }
    } else {
      validate.integer().nonNegativeInteger()
    }

    this.srid = srid
    return this
  }

  /**
   * Sets whether the navigation property references a collection.
   *
   * @param {boolean} isCollection - indicates whether the expression references a collection
   * @returns {CsdlIsOfExpression} this instance
   */
  setCollection (isCollection) {
    validateThat('isCollection', isCollection)
      .notNullNorUndefined()
      .typeOf('boolean')

    this.isCollection = isCollection
    return this
  }
}

module.exports = CsdlIsOfExpression
