'use strict'

const CsdlAnnotationExpression = require('./CsdlAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752654">
 *     OData CSDL # 14.5.8 Expression edm:LabeledElement
 * </a>
 *
 * @extends CsdlAnnotationExpression
 */
class CsdlLabeledElementExpression extends CsdlAnnotationExpression {
  /**
   * @param {string} name Label name which is used to reference the expression
   * @param {CsdlAnnotationExpression} expression Expression which is referenced
   */
  constructor (name, expression) {
    validateThat('name', name)
      .truthy()
      .typeOf('string')
    validateThat('expression', expression)
      .truthy()
      .instanceOf(Object)

    super(CsdlAnnotationExpression.kinds.LabeledElement)

    /**
     * OData CSDL # 14.5.8.1 Attribute Name
     * @type {string}
     */
    this.name = name

    /**
     * Labeled expression
     * @type {CsdlAnnotationExpression}
     */
    this.expression = expression
  }
}

module.exports = CsdlLabeledElementExpression
