'use strict'

const CsdlAnnotationExpression = require('./CsdlAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752655">
 *     OData CSDL # 14.5.9 Expression edm:LabeledElementReference
 * </a>
 *
 * @extends CsdlAnnotationExpression
 */
class CsdlLabeledElementReferenceExpression extends CsdlAnnotationExpression {
  /**
   * @param {FullQualifiedName} name Label used to find referenced expression
   */
  constructor (name) {
    validateThat('name', name)
      .truthy()
      .instanceOf(Object)

    super(CsdlAnnotationExpression.kinds.LabeledElementReference)
    /**
     * Referenced label
     * @type {string}
     */
    this.name = name
  }
}

module.exports = CsdlLabeledElementReferenceExpression
