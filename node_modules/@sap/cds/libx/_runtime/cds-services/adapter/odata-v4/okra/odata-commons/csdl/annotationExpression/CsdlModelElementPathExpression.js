'use strict'

const CsdlAnnotationExpression = require('./CsdlAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 * TODO: Docu
 *
 * @extends CsdlAnnotationExpression
 */
class CsdlModelElementPathExpression extends CsdlAnnotationExpression {
  /**
   * @param {string} modelElementPath - Model Element Path
   */
  constructor (modelElementPath) {
    validateThat('modelElementPath', modelElementPath)
      .truthy()
      .typeOf('string')

    super(CsdlAnnotationExpression.kinds.ModelElementPath)

    /**
     * @type {string}
     */
    this.modelElementPath = modelElementPath
  }
}

module.exports = CsdlModelElementPathExpression
