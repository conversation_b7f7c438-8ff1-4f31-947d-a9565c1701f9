'use strict'

const CsdlAnnotationExpression = require('./CsdlAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752657">
 *     OData CSDL # 14.5.11 Expression edm:NavigationPropertyPath
 * </a>
 *
 * @extends CsdlAnnotationExpression
 */
class CsdlNavigationPropertyPathExpression extends CsdlAnnotationExpression {
  /**
   * @param {string} navigationPropertyPath Path to a navigation property
   */
  constructor (navigationPropertyPath) {
    validateThat('navigationPropertyPath', navigationPropertyPath)
      .truthy()
      .typeOf('string')

    super(CsdlAnnotationExpression.kinds.NavigationPropertyPath)
    /**
     * Path to the referenced navigation property
     * @type {string}
     */
    this.navigationPropertyPath = navigationPropertyPath
  }
}

module.exports = CsdlNavigationPropertyPathExpression
