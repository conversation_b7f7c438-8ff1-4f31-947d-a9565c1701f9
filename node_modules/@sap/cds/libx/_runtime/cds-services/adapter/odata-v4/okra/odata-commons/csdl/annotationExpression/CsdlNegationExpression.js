'use strict'

const CsdlAnnotationExpression = require('./CsdlAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 * @extends CsdlAnnotationExpression
 */
class CsdlNegationExpression extends CsdlAnnotationExpression {
  /**
   * @param {CsdlAnnotationExpression} expression Operand, must evaluate to a numeric type
   */
  constructor (expression) {
    validateThat('expression', expression)
      .truthy()
      .instanceOf(Object)
    super(CsdlAnnotationExpression.kinds.Negation)

    /**
     * Expression to be negated
     * @type {CsdlAnnotationExpression}
     */
    this.expression = expression
  }
}

module.exports = CsdlNegationExpression
