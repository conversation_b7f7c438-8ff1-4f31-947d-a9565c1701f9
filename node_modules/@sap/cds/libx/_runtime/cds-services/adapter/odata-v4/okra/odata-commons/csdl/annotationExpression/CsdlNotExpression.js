'use strict'

const CsdlAnnotationExpression = require('./CsdlAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752647">
 *     OData CSDL # 14.5.1 Comparison and Logical Operators
 * </a>
 *
 * @extends CsdlAnnotationExpression
 */
class CsdlNotExpression extends CsdlAnnotationExpression {
  /**
   * @param {CsdlAnnotationExpression} expression Operand, must evaluate to type Edm.Boolean
   */
  constructor (expression) {
    validateThat('expression', expression)
      .truthy()
      .instanceOf(Object)
    super(CsdlAnnotationExpression.kinds.Not)

    /**
     * Expression to be negated
     * @type {CsdlAnnotationExpression}
     */
    this.expression = expression
  }
}

module.exports = CsdlNotExpression
