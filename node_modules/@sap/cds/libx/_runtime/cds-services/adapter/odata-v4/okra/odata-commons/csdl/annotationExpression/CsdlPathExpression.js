'use strict'

const CsdlAnnotationExpression = require('./CsdlAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752658">
 *     OData CSDL # 14.5.12 Expression edm:Path
 * </a>
 *
 * @extends CsdlAnnotationExpression
 */
class CsdlPathExpression extends CsdlAnnotationExpression {
  /**
   * @param {string} path Path to referenced edm artifact (e.g. entity container, entity set, type, property, ...)
   */
  constructor (path) {
    validateThat('path', path)
      .truthy()
      .typeOf('string')

    super(CsdlAnnotationExpression.kinds.Path)

    /**
     * Path to referenced edm artifact
     * @type {string}
     */
    this.path = path
  }
}

module.exports = CsdlPathExpression
