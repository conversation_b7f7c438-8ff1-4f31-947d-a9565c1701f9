'use strict'

const CsdlAnnotationExpression = require('./CsdlAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752659">
 *     OData CSDL # 14.5.13 Expression edm:PropertyPath
 * </a>
 *
 * @extends CsdlAnnotationExpression
 */
class CsdlPropertyPathExpression extends CsdlAnnotationExpression {
  /**
   * @param {string} propertyPath Path to property that specifies Edm.PropertyPath
   */
  constructor (propertyPath) {
    validateThat('propertyPath', propertyPath)
      .truthy()
      .typeOf('string')

    super(CsdlAnnotationExpression.kinds.PropertyPath)

    /**
     * Path to referenced property
     * @type {string}
     */
    this.propertyPath = propertyPath
  }
}

module.exports = CsdlPropertyPathExpression
