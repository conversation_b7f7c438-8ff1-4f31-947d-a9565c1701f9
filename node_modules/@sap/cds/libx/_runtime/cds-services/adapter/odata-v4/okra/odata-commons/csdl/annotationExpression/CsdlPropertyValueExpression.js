'use strict'

const CsdlAnnotationExpression = require('./CsdlAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752660">
 *     OData CSDL # 14.5.14.2 Element edm:PropertyValue
 * </a>
 *
 * @extends CsdlAnnotationExpression
 */
class CsdlPropertyValueExpression extends CsdlAnnotationExpression {
  /**
   * @param {string} property Property name
   * @param {CsdlAnnotationExpression} expression PropertyValue used to build content of CsdlRecordExpression
   */
  constructor (property, expression) {
    validateThat('property', property)
      .truthy()
      .typeOf('string')

    if (expression) {
      validateThat('propertyValue', expression).instanceOf(Object)
    }
    super(CsdlAnnotationExpression.kinds.PropertyValue)

    /**
     * Property name
     * @type {string}
     */
    this.property = property

    /**
     * Property value
     * @type {CsdlAnnotationExpression}
     */
    this.expression = expression
  }

  /**
   * Set the expression to be casted
   * @param {CsdlAnnotationExpression} propertyValue Expression
   * @returns {CsdlPropertyValueExpression} this instance
   */
  setPropertyValue (propertyValue) {
    validateThat('propertyValue', propertyValue)
      .truthy()
      .instanceOf(Object)
    this.propertyValue = propertyValue
    return this
  }
}

module.exports = CsdlPropertyValueExpression
