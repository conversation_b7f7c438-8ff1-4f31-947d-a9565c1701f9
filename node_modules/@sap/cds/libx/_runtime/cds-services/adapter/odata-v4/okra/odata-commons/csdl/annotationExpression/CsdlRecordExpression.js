'use strict'

const CsdlAnnotationExpression = require('./CsdlAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752660">
 *     14.5.14 Expression edm:Record
 * </a>
 *
 * @extends CsdlAnnotationExpression
 */
class CsdlRecordExpression extends CsdlAnnotationExpression {
  /**
   * @param {FullQualifiedName} type Type of the record
   * @param {CsdlPropertyValueExpression[]} propertyValues List with the records properties
   */
  constructor (type, propertyValues) {
    if (propertyValues) {
      validateThat('propertyValues', propertyValues)
        .array()
        .containsInstancesOf(Object)
    }
    super(CsdlAnnotationExpression.kinds.Record)

    /**
     * Target type of the cast
     * @type {FullQualifiedName}
     */
    this.type = type

    /**
     * List of property values
     */
    this.propertyValues = propertyValues || []
  }

  /**
   * Sets the records properties
   *
   * @param {CsdlPropertyValueExpression[]} propertyValues List with the records properties
   * @returns {CsdlRecordExpression} this instance
   */
  setPropertyValues (propertyValues) {
    validateThat('properties', propertyValues)
      .truthy()
      .array()
      .containsInstancesOf(Object)
    this.propertyValues = propertyValues
    return this
  }
}

module.exports = CsdlRecordExpression
