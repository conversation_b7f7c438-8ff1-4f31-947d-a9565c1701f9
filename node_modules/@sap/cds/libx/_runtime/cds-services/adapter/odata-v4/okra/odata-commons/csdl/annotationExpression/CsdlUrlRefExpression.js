'use strict'

const CsdlAnnotationExpression = require('./CsdlAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752661">
 *     OData CSDL # 14.5.15 Expression edm:UrlRef
 * </a>
 *
 * @extends CsdlAnnotationExpression
 */
class CsdlUrlRefExpression extends CsdlAnnotationExpression {
  /**
   * @param {CsdlConstantExpression} expression Constant expression of type String
   */
  constructor (expression) {
    validateThat('expression', expression)
      .truthy()
      .instanceOf(Object)
    super(CsdlAnnotationExpression.kinds.UrlRef)

    /**
     * Url expression
     * @type {CsdlAnnotationExpression}
     */
    this.expression = expression
  }
}

module.exports = CsdlUrlRefExpression
