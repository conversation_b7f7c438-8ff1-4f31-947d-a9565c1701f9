'use strict'

const AbstractEdmOperationImport = require('./AbstractEdmOperationImport')

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752610">
 *     OData CSDL # 13.5 Element edm:ActionImport
 * </a>
 *
 * @extends AbstractEdmOperationImport
 * @hideconstructor
 */
class EdmActionImport extends AbstractEdmOperationImport {
  /**
   * Constructor
   * @param {Edm} edm The edm itself
   * @param {EdmEntityContainer} container The entity container the action import belongs too
   * @param {CsdlActionImport} actionImport The action import
   */
  constructor (edm, container, actionImport) {
    super(edm, container, actionImport)

    /**
     * @type {CsdlActionImport}
     * @private
     */
    this._actionImport = actionImport
  }

  /**
   * Returns the action of this action import.
   * @returns {EdmAction} the action
   */
  getUnboundAction () {
    return this.edm.getUnboundAction(this._actionImport.action)
  }
}

module.exports = EdmActionImport
