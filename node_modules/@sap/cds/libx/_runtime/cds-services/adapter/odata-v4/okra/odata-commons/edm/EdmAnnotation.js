'use strict'

const AnnotationFactory = require('./annotationExpression/AnnotationExpressionFactory')
const Edm = require('./Edm')
const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752630">
 *     OData CSDL # 14.3 Element edm:EdmAnnotation
 * </a>
 *
 * @hideconstructor
 */
class EdmAnnotation {
  /**
   * Constructor
   * @param {Edm} edm The edm itself
   * @param {CsdlAnnotation} annotation The annotation
   */
  constructor (edm, annotation) {
    validateThat('edm', edm)
      .truthy()
      .instanceOf(Edm)
    validateThat('annotation', annotation)
      .truthy()
      .instanceOf(Object)

    this._edm = edm

    /**
     * @type {CsdlAnnotation}
     * @private
     */
    this._annotation = annotation

    /**
     * @type {EdmTerm}
     * @private
     */
    this._term = null

    /**
     * @type {EdmAnnotationExpression}
     * @private
     */
    this._annotationExpression = null

    /**
     * @type {EdmAnnotation[]}
     * @private
     */
    this._annotations = null
  }

  /**
   * Returns the full-qualified name of the term of the annotation.
   * @returns {FullQualifiedName} the full-qualified name
   */
  getTermFullQualifiedName () {
    return this._annotation.term
  }

  /**
   * Returns the term of the annotation.
   * @returns {EdmTerm} the term
   */
  getTerm () {
    if (!this._term) this._term = this._edm.getTerm(this._annotation.term)
    return this._term
  }

  /**
   * Returns annotation's qualifier.
   * @returns {string} the qualifier
   */
  getQualifier () {
    return this._annotation.qualifier
  }

  /**
   * Returns the annotation expression.
   * @returns {EdmAnnotationExpression} the annotation expression
   */
  getExpression () {
    if (!this._annotationExpression) {
      this._annotationExpression = AnnotationFactory.createEdmExpressionFromCsdlExpression(
        this._edm,
        this._annotation.expression
      )
    }
    return this._annotationExpression
  }

  /**
   * Returns the annotations for this object.
   * @returns {EdmAnnotation[]} the annotations
   */
  getAnnotations () {
    if (!this._annotations) {
      this._annotations = this._annotation.annotations.map(item => new EdmAnnotation(this._edm, item))
    }
    return this._annotations
  }
}

module.exports = EdmAnnotation
