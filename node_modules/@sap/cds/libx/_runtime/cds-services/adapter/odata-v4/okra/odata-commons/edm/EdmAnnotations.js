'use strict'
const EdmAnnotation = require('./EdmAnnotation')
const Edm = require('./Edm')
const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752627">
 *     OData CSDL # 14.2 Element edm:Annotations
 * </a>
 * @hideconstructor
 */
class EdmAnnotations {
  /**
   * Constructor
   * @param {Edm} edm The edm itself
   * @param {CsdlAnnotations} externalAnnotations The annotation
   */
  constructor (edm, externalAnnotations) {
    validateThat('edm', edm)
      .truthy()
      .instanceOf(Edm)
    validateThat('externalAnnotations', externalAnnotations)
      .truthy()
      .instanceOf(Object)

    this._edm = edm

    this._externalAnnotations = externalAnnotations

    /**
     * @type {EdmAnnotation[]}
     * @private
     */
    this._annotations = null
  }

  getTarget () {
    return this._externalAnnotations.target
  }

  getQualifier () {
    return this._externalAnnotations.qualifier
  }

  /**
   * Returns the annotations for this object.
   * @returns {EdmAnnotation[]} the annotations
   */
  getAnnotations () {
    if (!this._annotations) {
      this._annotations = this._externalAnnotations.annotations.map(item => new EdmAnnotation(this._edm, item))
    }
    return this._annotations
  }
}

module.exports = EdmAnnotations
