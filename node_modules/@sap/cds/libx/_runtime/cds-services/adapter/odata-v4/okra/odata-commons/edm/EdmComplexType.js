'use strict'

const AbstractEdmStructuredType = require('./AbstractEdmStructuredType')
const EdmTypeKind = require('./EdmType').TypeKind

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752560">
 *     OData CSDL # 9.1 Element edm:ComplexType
 * </a>
 *
 * @extends AbstractEdmStructuredType
 * @hideconstructor
 */
class EdmComplexType extends AbstractEdmStructuredType {
  /**
   * Creates an instance of EdmComplexType.
   * @param {Edm} edm The edm itself
   * @param {FullQualifiedName} fqn The full qualified name of this complex type
   * @param {CsdlComplexType} csdlComplexType The csdl complex type structure
   * @param {Object} configuration Configuration object with additional configuration properties
   * @param {string[]} configuration.customAggregates the custom aggregates defined for this type
   */
  constructor (edm, fqn, csdlComplexType, configuration) {
    super(edm, fqn, EdmTypeKind.COMPLEX, csdlComplexType, configuration)

    /**
     * @type {Edm}
     * @private
     */
    this._edm = edm
  }

  /**
   * @override
   * @returns {?EdmComplexType} the base type
   */
  getBaseType () {
    if (this.edmBaseType) return this.edmBaseType

    if (!this.csdlStructuredType.baseType) return null

    this.edmBaseType = this._edm.getComplexType(this.csdlStructuredType.baseType)

    return this.edmBaseType
  }
}

module.exports = EdmComplexType
