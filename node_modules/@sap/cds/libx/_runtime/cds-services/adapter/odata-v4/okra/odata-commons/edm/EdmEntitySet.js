'use strict'

const AbstractEdmBindingTarget = require('./AbstractEdmBindingTarget')

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752600">
 *     OData CSDL # 13.2 Element edm:EntitySet
 * </a>
 *
 * @extends AbstractEdmBindingTarget
 * @hideconstructor
 */
class EdmEntitySet extends AbstractEdmBindingTarget {
  /**
   * @param {Edm} edm The Edm itself
   * @param {EdmEntityContainer} container Entity container
   * @param {CsdlEntitySet} entitySet Csdl entity set
   * @param {Object} configuration Configuration object with additional configuration properties
   */
  constructor (edm, container, entitySet, configuration) {
    super(edm, container, entitySet, configuration)
    this._entitySet = entitySet
  }

  /**
   * Returns true if the entityset is included in the service document, otherwise false
   * @returns {boolean} whether the entity set should appear in the service document
   */
  isIncludeInServiceDocument () {
    return this._entitySet.includeInServiceDocument
  }

  /**
   * Returns the maxPageSize for server driven paging if configured via edm configuration or null if not configured.
   * @returns {?number} the configured maxPageSize or null if not configured
   */
  getMaxPageSize () {
    const maxPageSize = this._configuration.maxPageSize
    return Number.isInteger(maxPageSize) ? maxPageSize : null
  }
}

module.exports = EdmEntitySet
