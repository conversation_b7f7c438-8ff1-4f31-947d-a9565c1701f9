'use strict'

const AbstractEdmStructuredType = require('./AbstractEdmStructuredType')
const EdmTypeKind = require('./EdmType').TypeKind
const EdmKeyPropertyRef = require('./EdmKeyPropertyRef')
const FeatureSupport = require('../FeatureSupport')

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752548">
 *     OData CSDL # 8. Element edm:EntityType
 * </a>
 *
 * @extends AbstractEdmStructuredType
 * @hideconstructor
 */
class EdmEntityType extends AbstractEdmStructuredType {
  /**
   * Creates an instance of EdmEntityType.
   * @param {Edm} edm The edm itself
   * @param {FullQualifiedName} fqn The full qualified name of this entity type
   * @param {CsdlEntityType} csdlEntityType The csdl entity type structure
   * @param {Object} configuration Configuration object with additional configuration properties
   * @param {string[]} configuration.customAggregates the custom aggregates defined for this type
   */
  constructor (edm, fqn, csdlEntityType, configuration) {
    super(edm, fqn, EdmTypeKind.ENTITY, csdlEntityType, configuration)

    if (csdlEntityType.hasStream) {
      FeatureSupport.failUnsupported(FeatureSupport.features.AttributeHasStream)
    }

    /**
     * @type {EdmEntityType}
     * @private
     */
    this._edmBaseType = null

    /**
     * @type {Map.<string, EdmKeyPropertyRef>}
     * @private
     */
    this._ownKeyPropertyRefs = null
  }

  /**
   * Returns the base type. Null if no base type exists.
   * @returns {?EdmEntityType} the base type
   * @override
   */
  getBaseType () {
    if (this._edmBaseType) return this._edmBaseType

    if (!this.csdlStructuredType.baseType) return null

    this._edmBaseType = this._edm.getEntityType(this.csdlStructuredType.baseType)

    return this._edmBaseType
  }

  /**
   * Returns a Map of key-property references.
   * These properties define the key in this EDM type excluding those from the base type.
   * @returns {Map.<string, EdmKeyPropertyRef>} the Map of key-property references
   */
  getOwnKeyPropertyRefs () {
    if (this._ownKeyPropertyRefs) return this._ownKeyPropertyRefs

    this._ownKeyPropertyRefs = new Map()
    for (const property of this.csdlStructuredType.key) {
      // property --> CsdlPropertyRef
      const name = property.alias || property.name
      const edmProp = new EdmKeyPropertyRef(this, property)
      this._ownKeyPropertyRefs.set(name, edmProp)
    }

    return this._ownKeyPropertyRefs
  }

  /**
   * Returns a Map of key-property references.
   * An entity is uniquely identified within an entity set by its key.
   * The key definition consists of its key-property reference objects.
   *
   * @returns {Map.<string, EdmKeyPropertyRef>} the Map of key-property references
   */
  getKeyPropertyRefs () {
    if (this.getOwnKeyPropertyRefs().size > 0) {
      return this.getOwnKeyPropertyRefs()
    }
    return this.getBaseType() ? this.getBaseType().getKeyPropertyRefs() : new Map()
  }

  /**
   * Returns the key-property reference found by its name.
   * @param {string} name Name of the requested key property
   * @returns {EdmKeyPropertyRef} the key-property reference
   */
  getKeyPropertyRef (name) {
    return this.getKeyPropertyRefs().get(name)
  }

  /**
   * Returns true if this entity type has a stream. A value of true specifies that the entity
   * type is a media entity. Media entities are entities that represent a media stream,
   * such as a photo.
   *
   * @returns {boolean} whether this entity type is a media entity type
   */
  hasStream () {
    if (this.csdlStructuredType.hasStream) return true
    if (this.getBaseType() && this.getBaseType().hasStream()) return true
    const streamProp = this.csdlStructuredType &&
      this.csdlStructuredType.properties &&
      this.csdlStructuredType.properties.find(prop => prop.type && prop.type.namespace + '.' + prop.type.name === 'Edm.Stream')
    return !!streamProp
  }
}

module.exports = EdmEntityType
