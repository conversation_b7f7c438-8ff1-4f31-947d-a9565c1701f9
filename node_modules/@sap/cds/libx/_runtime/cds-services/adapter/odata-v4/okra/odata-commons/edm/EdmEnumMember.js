'use strict'

const EdmAnnotation = require('./EdmAnnotation')
const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * * * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752570">
 *     OData CSDL # 10.2 Element edm:Member
 * </a>
 * @hideconstructor
 */
class EdmEnumMember {
  /**
   * Constructor
   * @param {Edm} edm The edm itself
   * @param {CsdlEnumMember} enumMember the CSDL enum member
   */
  constructor (edm, enumMember) {
    validateThat('edm', edm).truthy()
    validateThat('enumMember', enumMember).truthy()

    /**
     * @type {Edm}
     * @private
     */
    this._edm = edm

    /**
     * @type {CsdlEnumMember}
     * @private
     */
    this._enumMember = enumMember

    /**
     * @type {EdmAnnotation[]}
     * @private
     */
    this._annotations = null
  }

  /**
   * Returns the name.
   * @returns {string} the name
   */
  getName () {
    return this._enumMember.name
  }

  /**
   * Returns the value.
   * @returns {number} the value
   */
  getValue () {
    return this._enumMember.value
  }

  /**
   * Returns the annotations for this member.
   * @returns {EdmAnnotation[]} the annotations
   */
  getAnnotations () {
    if (!this._annotations) {
      this._annotations = this._enumMember.annotations.map(item => new EdmAnnotation(this._edm, item))
    }
    return this._annotations
  }
}

module.exports = EdmEnumMember
