'use strict'

const Edm = require('./Edm')
const EdmAnnotation = require('./EdmAnnotation')
const EdmType = require('./EdmType')
const EdmPrimitiveTypeKind = require('./EdmPrimitiveTypeKind')
const EdmEnumMember = require('./EdmEnumMember')
const validateThat = require('../validator/ParameterValidator').validateThat
const IllegalArgumentError = require('../errors/IllegalArgumentError')

/**
 * Enumeration types are nominal types that represent a series of related values.
 * Enumeration types expose these related values as members of the enumeration.
 *
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752565">
 *  OData CSDL # 10. Enumeration Type
 * </a>
 *
 * @extends EdmType
 * @hideconstructor
 */
class EdmEnumType extends EdmType {
  /**
   * Constructor
   * @param {Edm} edm entity data model
   * @param {FullQualifiedName} fqn full qualified name of the enumeration type
   * @param {CsdlEnumType} csdlEnumType CSDL object structure
   */
  constructor (edm, fqn, csdlEnumType) {
    validateThat('edm', edm)
      .truthy()
      .instanceOf(Edm)

    super(fqn, EdmType.TypeKind.ENUM)

    validateThat('csdlEnumType', csdlEnumType)
      .truthy()
      .instanceOf(Object)

    if (csdlEnumType.underlyingType.name === 'Int64') {
      throw new IllegalArgumentError(
        `Underlying type '${csdlEnumType.underlyingType.name}' not supported for enumeration type '${fqn}'`
      )
    }

    this._edm = edm

    /**
     * @type {CsdlEnumType|Object}
     * @private
     */
    this._csdlEnumType = csdlEnumType

    /**
     * @type {EdmPrimitiveType}
     * @private
     */
    this._edmPrimitiveType = null

    /**
     * @type {Map.<string, EdmEnumMember>}
     * @private
     */
    this._members = null

    /**
     * @type {EdmAnnotation[]}
     * @private
     */
    this._annotations = null
  }

  /**
   * Returns the corresponding underlying primitive type like Edm.Int16.
   * @returns {EdmPrimitiveType} The underlying primitive type
   */
  getUnderlyingType () {
    if (!this._edmPrimitiveType) {
      this._edmPrimitiveType = EdmPrimitiveTypeKind.fromName(this._csdlEnumType.underlyingType.name)
    }
    return this._edmPrimitiveType
  }

  /**
   * Returns true if this enumeration type allows multiple members to be selected simultaneously.
   * @returns {boolean} true if multiple members can be selected, false otherwise
   */
  isFlags () {
    return this._csdlEnumType.isFlags
  }

  /**
   * Returns a map with all members.
   * @returns {Map.<string, EdmEnumMember>} the members
   */
  getMembers () {
    if (!this._members) {
      this._members = new Map(
        this._csdlEnumType.members.map(member => [member.name, new EdmEnumMember(this._edm, member)])
      )
    }
    return this._members
  }

  /**
   * Returns the annotations for this object.
   * @returns {EdmAnnotation[]} the annotations
   */
  getAnnotations () {
    if (!this._annotations) {
      this._annotations = this._csdlEnumType.annotations.map(item => new EdmAnnotation(this._edm, item))
    }
    return this._annotations
  }
}

module.exports = EdmEnumType
