'use strict'

const AbstractEdmOperation = require('./AbstractEdmOperation')

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752583">
 *     OData CSDL # 12.2 Element edm:Function
 * </a>
 *
 * @extends AbstractEdmOperation
 * @hideconstructor
 */
class EdmFunction extends AbstractEdmOperation {
  /**
   * Constructor
   * @param {Edm} edm the EDM
   * @param {FullQualifiedName} name the qualified name of the function
   * @param {CsdlFunction} ffunction the CSDL function
   */
  constructor (edm, name, ffunction) {
    super(edm, name, ffunction)

    /**
     * @type {CsdlFunction}
     * @private
     */
    this._function = ffunction
  }

  /**
   * Returns true if the function is composable, otherwise false.
   * @returns {boolean} whether the function is composable
   */
  isComposable () {
    return this._function.isComposable
  }
}

module.exports = EdmFunction
