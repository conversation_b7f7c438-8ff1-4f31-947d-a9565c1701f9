'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752509">
 *     OData CSDL # 3.5 Element edmx:IncludeAnnotations
 * </a>
 * @hideconstructor
 */
class EdmIncludeAnnotation {
  /**
   * Constructor
   * @param {CsdlIncludeAnnotation} includeAnnotation the CSDL include annotation
   */
  constructor (includeAnnotation) {
    validateThat('includeAnnotation', includeAnnotation).truthy()

    /**
     * @type {CsdlIncludeAnnotation}
     * @private
     */
    this._includeAnnotation = includeAnnotation
  }

  /**
   * @returns {string} the namespace
   */
  getTermNamespace () {
    return this._includeAnnotation.termNamespace
  }

  /**
   * @returns {string} the qualifier
   */
  getQualifier () {
    return this._includeAnnotation.qualifier
  }

  /**
   * @returns {string} the target namespace
   */
  getTargetNamespace () {
    return this._includeAnnotation.targetNamespace
  }
}

module.exports = EdmIncludeAnnotation
