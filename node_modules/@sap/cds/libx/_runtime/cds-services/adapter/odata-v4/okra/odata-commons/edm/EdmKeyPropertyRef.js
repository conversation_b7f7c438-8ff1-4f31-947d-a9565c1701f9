'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752556">
 *  OData CSDL # 8.3 Element edm:PropertyRef
 * </a>
 * @hideconstructor
 */
class EdmKeyPropertyRef {
  /**
   * Creates instance of EdmKeyPropertyRef.
   * @param {EdmEntityType} edmEntityType The corresponding entity type
   * @param {CsdlPropertyRef} ref the CSDL property ref structure
   */
  constructor (edmEntityType, ref) {
    validateThat('edmEntityType', edmEntityType).truthy()
    validateThat('ref', ref).truthy()

    /**
     * @type {EdmEntityType}
     * @private
     */
    this._edmEntityType = edmEntityType

    /**
     * @type {CsdlPropertyRef}
     * @private
     */
    this._ref = ref

    /**
     * @type {EdmProperty}
     * @private
     */
    this._property = null
  }

  /**
   * Returns the name of the edm key property reference. The name can be a simple identifier or a
   * property path like 'PropertyCompComp/PropertyComp/PropertyString'.
   *
   * @returns {string} The name
   */
  getName () {
    return this._ref.name
  }

  /**
   * Returns the alias of the edm key property reference. The alias must be a simple identifier.
   * @returns {?string} The alias or null
   */
  getAlias () {
    return this._ref.alias
  }

  /**
   * Returns the property referenced by this key property ref instance.
   * @returns {EdmProperty} The referenced property
   */
  getProperty () {
    if (!this._property) {
      if (this.getAlias()) {
        let currentType = this._edmEntityType
        for (const propertyName of this.getName().split('/')) {
          this._property =
            currentType.getStructuralProperty(propertyName) || currentType.getNavigationProperty(propertyName)
          currentType = this._property.getType ? this._property.getType() : this._property.getEntityType()
        }
      } else {
        this._property = this._edmEntityType.getStructuralProperty(this.getName())
      }
    }
    return this._property
  }
}

module.exports = EdmKeyPropertyRef
