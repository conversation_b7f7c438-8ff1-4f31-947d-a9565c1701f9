'use strict'

const EdmAnnotation = require('./EdmAnnotation')
const EdmOnDelete = require('./EdmOnDelete')
const EdmReferentialConstraint = require('./EdmReferentialConstraint')
const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * A navigation property allows navigation to related entities.
 *
 * <a href="../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752537">
 *  OData CSDL # 7.1 Element edm:NavigationProperty
 * </a>
 * @hideconstructor
 */
class EdmNavigationProperty {
  /**
   * Creates an instance of EdmNavigationProperty.
   * @param {Edm} edm The edm itself
   * @param {CsdlNavigationProperty} navigationProperty The navigation property structure
   */
  constructor (edm, navigationProperty) {
    validateThat('edm', edm).truthy()
    validateThat('navigationProperty', navigationProperty).truthy()

    /**
     * @type {Edm}
     * @private
     */
    this._edm = edm

    /**
     * @type {string}
     * @private
     */
    this._name = navigationProperty.name

    /**
     * @type {CsdlNavigationProperty}
     * @private
     */
    this._navigationProperty = navigationProperty

    /**
     * @type {EdmReferentialConstraint[]}
     * @private
     */
    this._referentialConstraints = null

    /**
     * @type {EdmEntityType}
     * @private
     */
    this._entityType = null

    /**
     * @type {EdmNavigationProperty}
     * @private
     */
    this._partnerNavigationProperty = null

    /**
     * @type {EdmOnDelte}
     * @private
     */
    this._onDelete = null

    /**
     * @type {EdmAnnotation[]}
     * @private
     */
    this._annotations = null
  }

  /**
   * Return true if this navigation property's target is a collection, else false.
   * @returns {boolean} True if this navigation property's target is a collection, else false
   */
  isCollection () {
    return this._navigationProperty.isCollection
  }

  /**
   * Returns true if the navigation property is nullable, otherwise false
   * @returns {boolean} True if this navigation property is nullable, else false
   */
  isNullable () {
    return this._navigationProperty.isNullable
  }

  /**
   * True if this navigation property contains target, else false
   * @returns {boolean} whether the target is contained
   */
  containsTarget () {
    return this._navigationProperty.containsTarget
  }

  /**
   * Returns the EdmOnDelete instance.
   * @returns {EdmOnDelete|null} The edm on delete instance or null if no OnDelete is available
   */
  getOnDelete () {
    if (!this._navigationProperty.onDelete) return null
    if (this._onDelete) return this._onDelete

    this._onDelete = new EdmOnDelete(this._edm, this._navigationProperty.onDelete)
    return this._onDelete
  }

  /**
   * Returns the corresponding entity type.
   * @returns {EdmEntityType} the entity type of the target
   */
  getEntityType () {
    if (!this._entityType) this._entityType = this._edm.getEntityType(this._navigationProperty.type)
    return this._entityType
  }

  /**
   * Returns the name of this navigation property.
   * @returns {string} The name of the property
   */
  getName () {
    return this._name
  }

  /**
   * Returns the partner navigation property.
   * @returns {EdmNavigationProperty} the partner
   */
  getPartner () {
    if (!this._navigationProperty.partner) {
      return null
    }

    if (this._partnerNavigationProperty) {
      return this._partnerNavigationProperty
    }

    let partner = this._navigationProperty.partner
    let currentType = this.getEntityType()
    for (const name of partner.split('/')) {
      this._partnerNavigationProperty = currentType.getNavigationProperty(name)
      currentType = this._partnerNavigationProperty.getEntityType()
    }

    return this._partnerNavigationProperty
  }

  /**
   * Returns the list of referential constraints.
   * @returns {Map.<string, EdmReferentialConstraint>} The collection of referential constraints
   */
  getReferentialConstraints () {
    if (!this._referentialConstraints) {
      this._referentialConstraints = new Map()

      let providerConstraints = this._navigationProperty.referentialConstraints
      if (providerConstraints && providerConstraints.length > 0) {
        // constraint --> CsdlReferentialConstraint
        for (const constraint of providerConstraints) {
          this._referentialConstraints.set(constraint.property, new EdmReferentialConstraint(this._edm, constraint))
        }
      }
    }
    return this._referentialConstraints
  }

  /**
   * Returns the annotations for this object.
   * @returns {EdmAnnotation[]} the annotations
   */
  getAnnotations () {
    if (!this._annotations) {
      this._annotations = this._navigationProperty.annotations.map(item => new EdmAnnotation(this._edm, item))
    }
    return this._annotations
  }
}

module.exports = EdmNavigationProperty
