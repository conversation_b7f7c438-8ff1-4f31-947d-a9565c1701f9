'use strict'

const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752607">
 *  OData CSDL # 13.4 Element edm:NavigationPropertyBinding
 * </a>
 * @hideconstructor
 */
class EdmNavigationPropertyBinding {
  /**
   * Constructor
   * @param {string} path Path
   * @param {string} target Target
   */
  constructor (path, target) {
    validateThat('path', path)
      .truthy()
      .typeOf('string')
    validateThat('target', target)
      .truthy()
      .typeOf('string')

    this.path = path
    this.target = target
  }

  /**
   * Returns the path.
   * @returns {string} the path
   */
  getPath () {
    return this.path
  }

  /**
   * Returns the target.
   * @returns {string} the target
   */
  getTarget () {
    return this.target
  }
}

module.exports = EdmNavigationPropertyBinding
