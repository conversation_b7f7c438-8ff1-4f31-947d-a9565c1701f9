'use strict'

const EdmType = require('./EdmType')
const FullQualifiedName = require('../FullQualifiedName')

/**
 * Represents a primitive type from the 'Edm' namespace.
 * @extends EdmType
 * @hideconstructor
 */
class EdmPrimitiveType extends EdmType {
  /**
   * Constructor
   * @param {string} name the name
   */
  constructor (name) {
    super(new FullQualifiedName(EdmPrimitiveType.EDM_NAMESPACE, name), EdmType.TypeKind.PRIMITIVE)
  }

  toString () {
    return EdmPrimitiveType.EDM_NAMESPACE + '.' + this.getName()
  }
}

/**
 * @constant {string}
 * @default
 */
EdmPrimitiveType.EDM_NAMESPACE = 'Edm'

module.exports = EdmPrimitiveType
