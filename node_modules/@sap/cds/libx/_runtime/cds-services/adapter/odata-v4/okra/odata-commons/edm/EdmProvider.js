'use strict'

const Edm = require('./Edm')
const FullQualifiedName = require('../FullQualifiedName')
const EdmAction = require('./EdmAction')
const EdmFunction = require('./EdmFunction')
const EdmComplexType = require('./EdmComplexType')
const EdmEntityContainer = require('./EdmEntityContainer')
const EdmEntityType = require('./EdmEntityType')
const EdmTerm = require('./EdmTerm')
const EdmSchema = require('./EdmSchema')
const EdmReference = require('./EdmReference')

const EdmEnumType = require('./EdmEnumType')
const EdmTypeDefinition = require('./EdmTypeDefinition')

/**
 *  Uses a given CsdlProvider which contains classes from the application
 *  @extends Edm
 */
class EdmProvider extends Edm {
  /**
   * @param {CsdlProvider} provider the CSDL provider
   * @param {Object} configuration the configuration
   */
  constructor (provider, configuration) {
    super()

    this._configuration = configuration

    /**
     * @type {CsdlProvider}
     * @private
     */
    this._provider = provider

    /**
     * @type {CsdlSchema}
     * @private
     */
    this.localSchemas = null

    /**
     * @type {Map.<FullQualifiedName, CsdlAction[]>}
     * @private
     */
    this.actionsMap = new Map()

    /**
     * @type {Map.<FullQualifiedName, CsdlFunction[]>}
     * @private
     */
    this.functionsMap = new Map()
  }

  /**
   * Returns the CSDL Versions found by the underlying provider.
   * @returns {Map.<string, boolean>} The CSDL Versions found
   * @private
   */
  _getCsdlVersionsFound () {
    return this._provider._getCsdlVersionsFound()
  }

  /**
   * Creates the references list.
   * @returns {Map.<string, EdmReference>} the references
   * @override
   */
  createReferences () {
    const references = this._provider.getReferences()
    let ret = new Map()
    for (const reference of references) {
      ret.set(reference.uri, new EdmReference(this, reference))
    }
    return ret
  }

  /**
   * Creates the schemas map.
   * @returns {Map.<string, EdmSchema>} the schemas
   * @override
   */
  createSchemas () {
    const localSchemas = this._provider.getSchemas()

    let providerSchemas = new Map()
    for (let schema of localSchemas) {
      providerSchemas.set(schema.namespace, new EdmSchema(this, this._provider, schema, this._configuration))
    }

    return providerSchemas
  }

  /**
   * Creates a map to resolve aliases to namespaces.
   * @returns {Map.<string, string>} the aliases map
   * @override
   */
  createAliasToNamespaceInfo () {
    let aliasToNamespaceInfos = new Map()

    /**
     * @type {CsdlAliasInfo[]}
     */
    const aliasInfos = this._provider.getAliasInfos()
    if (aliasInfos) {
      for (let info of aliasInfos) {
        aliasToNamespaceInfos.set(info.alias, info.namespace)
      }
    }

    return aliasToNamespaceInfos
  }

  /**
   * Creates an entity container.
   * @param {FullQualifiedName} containerName Container name
   * @returns {EdmEntityContainer} the entity container
   * @override
   */
  createEntityContainer (containerName) {
    const entityContainerInfo = this._provider.getEntityContainerInfo(containerName) // CsdlEntityContainerInfo

    return entityContainerInfo
      ? EdmEntityContainer.createWithContainerInfo(this, this._provider, entityContainerInfo, this._configuration)
      : null
  }

  /**
   * Creates an enumeration type.
   * @param {FullQualifiedName} enumTypeName Name of the enumeration type
   * @returns {EdmEnumType} the created enumeration type
   * @override
   */
  createEnumType (enumTypeName) {
    const enumType = this._provider.getEnumType(enumTypeName)
    return enumType ? new EdmEnumType(this, enumTypeName, enumType) : null
  }

  /**
   * Creates a type definition.
   * @param {FullQualifiedName} typeDefinitionName Name of the type definition
   * @returns {EdmTypeDefinition} the created type definition
   * @override
   */
  createTypeDefinition (typeDefinitionName) {
    const typeDefinition = this._provider.getTypeDefinition(typeDefinitionName)
    return typeDefinition ? new EdmTypeDefinition(this, typeDefinitionName, typeDefinition) : null
  }

  /**
   * Creates an entity type.
   * @param {FullQualifiedName} entityTypeName Type name
   * @returns {EdmEntityType} the created entity type
   * @override
   */
  createEntityType (entityTypeName) {
    const entityType = this._provider.getEntityType(entityTypeName) // CsdlEntityType
    if (entityType) {
      const config =
        this._configuration && this._configuration[entityTypeName.namespace]
          ? this._configuration[entityTypeName.namespace][entityTypeName.name]
          : null
      return new EdmEntityType(this, entityTypeName, entityType, config)
    }
    return null
  }

  /**
   * Creates a complex type.
   * @param {FullQualifiedName} complexTypeName Type name
   * @returns {EdmComplexType} the created complex type
   * @override
   */
  createComplexType (complexTypeName) {
    const complexType = this._provider.getComplexType(complexTypeName) // CsdlEntityType
    if (complexType) {
      const config =
        this._configuration && this._configuration[complexTypeName.namespace]
          ? this._configuration[complexTypeName.namespace][complexTypeName.name]
          : null
      return new EdmComplexType(this, complexTypeName, complexType, config)
    }
    return null
  }

  /**
   * Creates an unbound action.
   * @param {FullQualifiedName} actionName Action name
   * @returns {EdmAction} the created action
   * @override
   */
  createUnboundAction (actionName) {
    /**
     * @type {CsdlAction[]}
     */
    let actions = this.actionsMap.get(actionName.toString())
    if (!actions) {
      actions = this._provider.getActions(actionName)
      if (!actions) {
        return null
      }
      this.actionsMap.set(actionName.toString(), actions)
    }

    // Search for first unbound action
    for (let action of actions) {
      if (!action.isBound) {
        return new EdmAction(this, actionName, action)
      }
    }
    return null
  }

  /**
   * Creates all overloadings of a bound function.
   * @param {FullQualifiedName} functionName Function name
   * @returns {EdmFunction[]} the functions
   * @override
   */
  createBoundFunctions (functionName) {
    let result = []
    let functions = this.functionsMap.get(functionName.toString())

    if (!functions) {
      functions = this._provider.getFunctions(functionName)
      if (functions) this.functionsMap.set(functionName.toString(), functions)
    }

    if (functions) {
      for (let ffunction of functions) {
        if (ffunction.isBound) {
          result.push(new EdmFunction(this, functionName, ffunction))
        }
      }
    }
    return result
  }

  /**
   * Creates all overloadings of an unbound function.
   * @param {FullQualifiedName} functionName Function name
   * @returns {EdmFunction[]} the functions
   * @override
   */
  createUnboundFunctions (functionName) {
    let result = []
    let functions = this.functionsMap.get(functionName.toString())

    if (!functions) {
      functions = this._provider.getFunctions(functionName)
      if (functions) this.functionsMap.set(functionName.toString(), functions)
    }

    if (functions) {
      for (let ffunction of functions) {
        if (!ffunction.isBound) {
          result.push(new EdmFunction(this, functionName, ffunction))
        }
      }
    }
    return result
  }

  /**
   * Creates a specific unbound function.
   * @param {FullQualifiedName} functionName Function name
   * @param {string[]} parameterNames Parameter names to identify the overloading
   * @returns {EdmFunction} the function
   * @override
   */
  createUnboundFunction (functionName, parameterNames) {
    let functions = this.functionsMap.get(functionName.toString())
    if (!functions) {
      functions = this._provider.getFunctions(functionName)
      if (!functions) {
        return null
      }
      this.functionsMap.set(functionName.toString(), functions)
    }

    let parameterNamesCopy = !parameterNames ? [] : parameterNames
    for (let ffunction of functions) {
      if (!ffunction.isBound) {
        const providerParameters = ffunction.parameters || []
        if (parameterNamesCopy.length === providerParameters.length) {
          const functionParameterNames = providerParameters.map(parameter => parameter.name)
          if (functionParameterNames.every(name => parameterNamesCopy.indexOf(name) > -1)) {
            return new EdmFunction(this, functionName, ffunction)
          }
        }
      }
    }
    return null
  }

  /**
   * Creates a bound action.
   * @param {FullQualifiedName} actionName Action name
   * @param {FullQualifiedName} bindingParameterTypeName Name of the binding parameter type
   * @param {boolean} isBindingParameterCollection True if the binding parameter is a collection, otherwise false
   * @returns {EdmAction} the action
   * @override
   */
  createBoundAction (actionName, bindingParameterTypeName, isBindingParameterCollection) {
    /**
     * @type {CsdlAction[]}
     */
    let actions = this.actionsMap.get(actionName.toString())

    if (!actions) {
      actions = this._provider.getActions(actionName)
      if (!actions) {
        return null
      }
      this.actionsMap.set(actionName.toString(), actions)
    }
    // Search for bound action where binding parameter matches

    for (let action of actions) {
      if (action.isBound) {
        const parameters = action.parameters
        const parameter = parameters[0]
        const paramFqn = this.resolvePossibleAlias(parameter.type)
        if (
          FullQualifiedName.equals(bindingParameterTypeName, paramFqn) &&
          isBindingParameterCollection === parameter.isCollection
        ) {
          return new EdmAction(this, actionName, action)
        }
      }
    }
    return null
  }

  /**
   * Creates a specific overloading of a bound function.
   * @param {FullQualifiedName} functionName Function name
   * @param {FullQualifiedName} bindingParameterTypeName Name of the binding parameter type
   * @param {boolean} isBindingParameterCollection True if the binding parameter is a collection, otherwise false
   * @param {string[]} parameterNames Parameter names to identify the overloading
   * @returns {EdmFunction} the function
   * @override
   */
  createBoundFunction (
    functionName,
    bindingParameterTypeName,
    isBindingParameterCollection,
    parameterNames
  ) {
    let functions = this.functionsMap.get(functionName.toString())
    if (!functions) {
      functions = this._provider.getFunctions(functionName)
      if (!functions) {
        return null
      }
      this.functionsMap.set(functionName.toString(), functions)
    }

    let parameterNamesCopy = parameterNames === null ? [] : parameterNames
    for (let ffunction of functions) {
      // CsdlFunction
      if (ffunction.isBound) {
        let providerParameters = ffunction.parameters

        let bindingParameter = providerParameters[0]
        if (
          FullQualifiedName.equals(bindingParameterTypeName, bindingParameter.type) &&
          isBindingParameterCollection === bindingParameter.isCollection
        ) {
          if (parameterNamesCopy.length === providerParameters.length - 1) {
            let providerParameterNames = []
            for (let i = 1; i < providerParameters.length; i++) {
              providerParameterNames.push(providerParameters[i].name)
            }

            if (providerParameterNames.every(name => parameterNamesCopy.indexOf(name) > -1)) {
              return new EdmFunction(this, functionName, ffunction)
            }
          }
        }
      }
    }
    return null
  }

  /**
   * Creates a term.
   * @param {FullQualifiedName} termName Term name
   * @returns {EdmTerm} the term
   */
  createTerm (termName) {
    const providerTerm = this._provider.getTerm(termName)
    return providerTerm ? new EdmTerm(this, providerTerm) : null
  }
}

module.exports = EdmProvider
