'use strict'

const EdmAnnotation = require('../edm/EdmAnnotation')
const EdmInclude = require('../edm/EdmInclude')
const EdmIncludeAnnotation = require('../edm/EdmIncludeAnnotation')
const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752504">
 *     OData CSDL # 3.3 Element edmx:Reference
 * </a>
 * @hideconstructor
 */
class EdmReference {
  /**
   * @param {Edm} edm The edm itself
   * @param {CsdlReference} reference the CSDL reference
   */
  constructor (edm, reference) {
    validateThat('edm', edm).truthy()
    validateThat('reference', reference).truthy()

    this._edm = edm

    /**
     * @type {CsdlReference}
     * @private
     */
    this._reference = reference

    /**
     * @type {EdmInclude[]}
     * @private
     */
    this._includes = null

    /**
     * @type {EdmIncludeAnnotation[]}
     * @private
     */
    this._includeAnnotations = null

    /**
     * @type {EdmAnnotation[]}
     * @private
     */
    this._annotations = null
  }

  getUri () {
    return this._reference.uri
  }

  /**
   * @returns {EdmInclude[]} the includes
   */
  getIncludes () {
    if (!this._includes) {
      this._includes = this._reference.includes.map(include => new EdmInclude(this._edm, include))
    }
    return this._includes
  }

  /**
   * @returns {EdmIncludeAnnotation[]} the include annotations
   */
  getIncludeAnnotations () {
    if (!this._includeAnnotations) {
      this._includeAnnotations = this._reference.includeAnnotations.map(
        includeAnnotation => new EdmIncludeAnnotation(includeAnnotation)
      )
    }
    return this._includeAnnotations
  }

  /**
   * Returns the annotations for this object.
   * @returns {EdmAnnotation[]} the annotations
   */
  getAnnotations () {
    if (!this._annotations) {
      this._annotations = this._reference.annotations.map(item => new EdmAnnotation(this._edm, item))
    }
    return this._annotations
  }
}

module.exports = EdmReference
