'use strict'

const EdmAnnotation = require('./EdmAnnotation')
const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * This class represents A referential constraint.
 * A referential constraint asserts that the dependent property (the property defined on the
 * dependent entity containing the navigation property) MUST have the same value as the principal
 * property (the referenced property defined on the principal entity that is the target of the
 * navigation).
 *
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752543">
 *     OData CSDL # 7.2 Element edm:ReferentialConstraint
 * </a>
 * @hideconstructor
 */
class EdmReferentialConstraint {
  /**
   * Create and instance of  EdmReferentialConstraint
   * @param {Edm} edm The edm itself
   * @param {CsdlReferentialConstraint} constraint the CSDL referential constraint
   */
  constructor (edm, constraint) {
    validateThat('edm', edm).truthy()
    validateThat('constraint', constraint).truthy()

    this._edm = edm

    /**
     * @type {CsdlReferentialConstraint}
     * @private
     */
    this.constraint = constraint

    /**
     * @type {EdmAnnotation[]}
     * @private
     */
    this._annotations = null
  }

  /**
   * Return the property name.
   * @returns {string} the property name
   */
  getPropertyName () {
    return this.constraint.property
  }

  /**
   * Return the referenced property name.
   * @returns {string} the referenced property name
   */
  getReferencedPropertyName () {
    return this.constraint.referencedProperty
  }

  /**
   * Returns the annotations for this object.
   * @returns {EdmAnnotation[]} the annotations
   */
  getAnnotations () {
    if (!this._annotations) {
      this._annotations = this.constraint.annotations.map(item => new EdmAnnotation(this._edm, item))
    }
    return this._annotations
  }
}

module.exports = EdmReferentialConstraint
