'use strict'

const FullQualifiedName = require('../FullQualifiedName')
const validateThat = require('../validator/ParameterValidator').validateThat

const EdmAnnotation = require('./EdmAnnotation')
const EdmAnnotations = require('./EdmAnnotations')
const EdmAction = require('./EdmAction')
const EdmFunction = require('./EdmFunction')

/**
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752521">
 *     OData CSDL # 5.1 Element edm:Schema
 * </a>
 * @hideconstructor
 */
class EdmSchema {
  /**
   * @param {EdmProvider} edm The EDM itself
   * @param {CsdlProvider} provider CSDL provider
   * @param {CsdlSchema} schema CSDL schema
   * @param {Object} configuration the current configuration object
   */
  constructor (edm, provider, schema, configuration) {
    validateThat('edm', edm).truthy()
    validateThat('provider', provider).truthy()
    validateThat('schema', schema).truthy()

    this._configuration = configuration

    /**
     * @type {EdmProvider}
     * @private
     */
    this._edm = edm

    /**
     * @type {CsdlProvider}
     * @private
     */
    this._provider = provider

    /**
     * @type {CsdlSchema}
     * @private
     */
    this.schema = schema

    /**
     * @type {string}
     * @private
     */
    this._namespace = schema.namespace

    /**
     * @type {?string}
     * @private
     */
    this._alias = schema.alias
    if (this._alias) edm.cacheAliasNamespaceInfo(this._alias, this._namespace)

    /**
     * @type {EdmEnumType[]}
     * @private
     */
    this._enumTypes = this._createEnumTypes()

    /**
     * @type {EdmTypeDefinition[]}
     * @private
     */
    this._typeDefinitions = this._createTypeDefinitions()

    /**
     * @type {EdmEntityType[]}
     * @private
     */
    this._entityTypes = this._createEntityTypes()

    /**
     * @type {EdmComplexType[]}
     * @private
     */
    this._complexTypes = this._createComplexTypes()

    /**
     * @type {EdmAction[]}
     * @private
     */
    this._actions = this._createActions()

    /**
     * @type {EdmFunction[]}
     * @private
     */
    this._functions = this._createFunctions()

    /**
     * @type {EdmEntityContainer}
     * @private
     */
    this._entityContainer = this._createEntityContainer()

    /**
     * @type {EdmTerm[]}
     * @private
     */
    this._terms = this._createTerms()

    /**
     * @type {EdmAnnotation[]}
     * @private
     */
    this._annotations = null

    /**
     * @type {EdmAnnotations[]}
     * @private
     */
    this._externalAnnotations = null
  }

  /**
   * Returns all entity types.
   * @returns {EdmEntityType[]} the entity types
   */
  getEntityTypes () {
    return this._entityTypes
  }

  /**
   * Returns all complex types.
   * @returns {EdmComplexType[]} the complex types
   */
  getComplexTypes () {
    return this._complexTypes
  }

  /**
   * Returns all terms.
   * @returns {EdmTerm[]} the terms
   */
  getTerms () {
    return this._terms
  }

  /**
   * Returns all actions.
   * @returns {EdmAction[]} the actions
   */
  getActions () {
    return this._actions
  }

  /**
   * Returns all functions.
   * @returns {EdmFunction[]} the functions
   */
  getFunctions () {
    return this._functions
  }

  /**
   * Returns all enumeration types.
   * @returns {EdmEnumType[]} the enumeration types
   */
  getEnumTypes () {
    return this._enumTypes
  }

  /**
   * Returns all type definitions.
   * @returns {EdmTypeDefinition[]} the type definitions
   */
  getTypeDefinitions () {
    return this._typeDefinitions
  }

  /**
   * Returns the entity container.
   * @returns {EdmEntityContainer} the entity container
   */
  getEntityContainer () {
    return this._entityContainer
  }

  /**
   * Returns the namespace.
   * @returns {string} the namespace
   */
  getNamespace () {
    return this._namespace
  }

  /**
   * Returns the alias.
   * @returns {?string} the alias
   */
  getAlias () {
    return this._alias
  }

  /**
   * Returns the annotations for this object.
   * @returns {EdmAnnotation[]} the annotations
   */
  getAnnotations () {
    if (!this._annotations) {
      this._annotations = this.schema.annotations.map(item => new EdmAnnotation(this._edm, item))
    }
    return this._annotations
  }

  /**
   * Returns the external annotations.
   * @returns {EdmAnnotations[]} the external annotations
   */
  getExternalAnnotations () {
    if (!this._externalAnnotations) {
      this._externalAnnotations = this.schema.externalAnnotations.map(item => new EdmAnnotations(this._edm, item))
    }
    return this._externalAnnotations
  }

  /**
   * Creates the entity container.
   * @returns {EdmEntityContainer} the entity container
   * @private
   */
  _createEntityContainer () {
    const container = this.schema.entityContainer

    return container ? this._edm.getEntityContainer(new FullQualifiedName(this._namespace, container.name)) : null
  }

  /**
   * Creates all terms.
   * @returns {EdmTerm[]} the terms
   * @private
   */
  _createTerms () {
    const providerTerms = this.schema.terms

    if (providerTerms && providerTerms.length) {
      return providerTerms.map(term => this._edm.getTerm(new FullQualifiedName(this._namespace, term.name)))
    }
    return []
  }

  /**
   * Creates all enumeration types.
   * @returns {EdmEnumType[]} the enumeration types
   * @private
   */
  _createEnumTypes () {
    const providerEnumTypes = this.schema.enumTypes

    if (providerEnumTypes && providerEnumTypes.length) {
      return providerEnumTypes.map(enumType =>
        this._edm.getEnumType(new FullQualifiedName(this._namespace, enumType.name))
      )
    }
    return []
  }

  /**
   * Creates all type definitions.
   * @returns {EdmTypeDefinition[]} the type definitions
   * @private
   */
  _createTypeDefinitions () {
    const providerTypeDefinitions = this.schema.typeDefinitions

    if (providerTypeDefinitions && providerTypeDefinitions.length) {
      return providerTypeDefinitions.map(def =>
        this._edm.getTypeDefinition(new FullQualifiedName(this._namespace, def.name))
      )
    }
    return []
  }

  /**
   * Creates all entity types.
   * @returns {EdmEntityType[]} the entity types
   * @private
   */
  _createEntityTypes () {
    const providerEntityTypes = this.schema.entityTypes // CsdlEntityType

    if (providerEntityTypes && providerEntityTypes.length) {
      return providerEntityTypes.map(entityType =>
        this._edm.getEntityType(new FullQualifiedName(this._namespace, entityType.name))
      )
    }
    return []
  }

  /**
   * Creates all complex types.
   * @returns {EdmComplexType[]} the complex types
   * @private
   */
  _createComplexTypes () {
    const providerComplexTypes = this.schema.complexTypes

    if (providerComplexTypes && providerComplexTypes.length) {
      return providerComplexTypes.map(complexType =>
        this._edm.getComplexType(new FullQualifiedName(this._namespace, complexType.name))
      )
    }
    return []
  }

  /**
   * Creates all actions.
   * @returns {EdmAction[]} the actions
   * @private
   */
  _createActions () {
    const actions = [] // EdmAction
    const providerActions = this.schema.actions

    if (providerActions && providerActions.length) {
      for (let action of providerActions) {
        // CsdlAction
        let actionName = new FullQualifiedName(this._namespace, action.name)
        let edmAction = new EdmAction(this._edm, actionName, action)
        actions.push(edmAction)
        this._edm.cacheAction(actionName, edmAction)
      }
    }
    return actions
  }

  /**
   * Creates all functions.
   * @returns {EdmFunction[]} the functions
   * @private
   */
  _createFunctions () {
    const functions = [] // EdmFunction
    const providerFunctions = this.schema.functions
    if (providerFunctions && providerFunctions.length) {
      for (let ffunction of providerFunctions) {
        let functionName = new FullQualifiedName(this._namespace, ffunction.name)
        let functionImpl = new EdmFunction(this._edm, functionName, ffunction)
        functions.push(functionImpl)
        this._edm.cacheFunction(functionName, functionImpl)
      }
    }
    return functions
  }
}

module.exports = EdmSchema
