'use strict'

const FullQualifiedName = require('../FullQualifiedName')
const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * Superclass of all types of the EDM
 * @abstract
 * @hideconstructor
 */
class EdmType {
  /**
   * Constructor
   * @param {FullQualifiedName} fqn Full qualified name of the type
   * @param {EdmType.TypeKind|number} kind Kind of the type
   */
  constructor (fqn, kind) {
    validateThat('fqn', fqn)
      .truthy()
      .instanceOf(FullQualifiedName)
    validateThat('kind', kind)
      .integer()
      .nonNegativeInteger()

    /**
     * @type {FullQualifiedName}
     * @private
     */
    this._fqn = fqn

    /**
     * @type {EdmType.TypeKind}
     * @private
     */
    this._kind = kind
  }

  /**
   * Return the name of the edm type.
   *
   * @returns {string} The name of this edm type
   */
  getName () {
    return this._fqn.name
  }

  /**
   * Returns the full qualified name object for this edm type.
   *
   * @returns {FullQualifiedName} The full qualified name object
   */
  getFullQualifiedName () {
    return this._fqn
  }

  /**
   * Return the namespace of the edm type.
   *
   * @returns {string} The namespace of this edm type
   */
  getNamespace () {
    return this._fqn.namespace
  }

  /**
   * Return the type kind of the edm type.
   *
   * @returns {EdmType.TypeKind} The type kind of this edm type
   */
  getKind () {
    return this._kind
  }
}

/**
 * Defined kinds of EDM types.
 * @enum {number}
 * @readonly
 */
EdmType.TypeKind = {
  /** primitive type */
  PRIMITIVE: 0,
  /** enumeration type */
  ENUM: 1,
  /** type definition */
  DEFINITION: 2,
  /** complex type */
  COMPLEX: 3,
  /** entity type */
  ENTITY: 4,
  /** vocabulary-term type */
  VOCABULARY_TERM: 5
}

module.exports = EdmType
