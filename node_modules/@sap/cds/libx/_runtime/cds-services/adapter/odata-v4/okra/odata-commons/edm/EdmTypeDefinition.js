'use strict'

const Edm = require('./Edm')
const EdmAnnotation = require('./EdmAnnotation')
const EdmType = require('./EdmType')
const EdmPrimitiveTypeKind = require('./EdmPrimitiveTypeKind')
const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * A type definition defines a specialization of one of the primitive types.
 * Type definitions can be used wherever a primitive type is used (other than as the
 * underlying type in a new type definition), and are type-comparable with their underlying
 * types and any type definitions defined using the same underlying type.
 *
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752573">
 *  OData CSDL # 11. Element TypeDefinition
 * </a>
 *
 * @extends EdmType
 * @hideconstructor
 */
class EdmTypeDefinition extends EdmType {
  /**
   * Constructor
   * @param {Edm} edm The Edm
   * @param {FullQualifiedName} fqn Full qualified name of the type definition
   * @param {CsdlTypeDefinition} csdlTypeDefinition The csdl object structure
   */
  constructor (edm, fqn, csdlTypeDefinition) {
    validateThat('edm', edm)
      .truthy()
      .instanceOf(Edm)

    super(fqn, EdmType.TypeKind.DEFINITION)

    validateThat('csdlTypeDefinition', csdlTypeDefinition)
      .truthy()
      .instanceOf(Object)

    this._edm = edm

    /**
     * @type {CsdlTypeDefinition|Object}
     * @private
     */
    this._csdlTypeDefinition = csdlTypeDefinition

    /**
     * @type {EdmPrimitiveType}
     * @private
     */
    this._edmPrimitiveType = null

    /**
     * @type {EdmAnnotation[]}
     * @private
     */
    this._annotations = null
  }

  /**
   * Returns the corresponding underlying primitive type like Edm.String or Edm.Int16.
   * @returns {EdmPrimitiveType} The underlying primitive type
   */
  getUnderlyingType () {
    if (!this._edmPrimitiveType) {
      this._edmPrimitiveType = EdmPrimitiveTypeKind.fromName(this._csdlTypeDefinition.underlyingType.name)
    }
    return this._edmPrimitiveType
  }

  /**
   * The value of this attribute specifies the maximum length of the value of the property on a type instance.
   * @returns {number|string} The max length value, can be int, shorthand like 'max' or null
   */
  getMaxLength () {
    return this._csdlTypeDefinition.maxLength
  }

  /**
   * For a decimal property the value of this attribute specifies the maximum number of digits
   * allowed in the property’s value; it MUST be a positive integer. If no value is specified,
   * the decimal property has unspecified precision.
   * For a temporal property the value of this attribute specifies the number of decimal
   * places allowed in the seconds portion of the property’s value; it MUST be a non-negative
   * integer between zero and twelve. If no value is specified, the temporal property has a
   * precision of zero.
   *
   * @returns {number|string} The precision value or null
   */
  getPrecision () {
    return this._csdlTypeDefinition.precision
  }

  /**
   * A decimal property MAY define a non-negative integer value or variable for the Scale
   * attribute. This attribute specifies the maximum number of digits allowed to the right of
   * the decimal point. The value variable means that the number of digits to the right of the
   * decimal point may vary from zero to the value of the Precision attribute.
   * The value of the Scale attribute MUST be less than or equal to the value of the Precision
   * attribute. If no value is specified, the Scale facet defaults to zero.
   *
   * @returns {string|number} The scale as int, 'variable' or null
   */
  getScale () {
    return this._csdlTypeDefinition.scale
  }

  /**
   * Returns the annotations for this object
   * @returns {EdmAnnotation[]} the annotations
   */
  getAnnotations () {
    if (!this._annotations) {
      this._annotations = this._csdlTypeDefinition.annotations.map(item => new EdmAnnotation(this._edm, item))
    }
    return this._annotations
  }
}

module.exports = EdmTypeDefinition
