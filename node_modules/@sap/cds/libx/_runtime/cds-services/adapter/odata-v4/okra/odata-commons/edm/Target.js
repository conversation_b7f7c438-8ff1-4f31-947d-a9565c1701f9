'use strict'

const FullQualifiedName = require('../FullQualifiedName')
const validateThat = require('../validator/ParameterValidator').validateThat

/**
 * An Edm target element. It contains a target name ({@link EdmEntitySet} or {@link EdmSingleton})
 * as well as the FullQualifiedName} of the entity container it is contained in.
 * @hideconstructor
 */
class Target {
  /**
   * Constructor
   * @param {string} target Target
   * @param {EdmEntityContainer} container Entity container
   */
  constructor (target, container) {
    validateThat('target', target)
      .truthy()
      .typeOf('string')
    validateThat('container', container).truthy()

    let bindingTargetParts = target.split('/')

    this._targetName = undefined
    this._entityContainer = undefined

    if (bindingTargetParts.length === 1) {
      this._entityContainer = container.getFullQualifiedName()
      this._targetName = bindingTargetParts[0]
    } else {
      this._entityContainer = FullQualifiedName.createFromNameSpaceAndName(bindingTargetParts[0])
      this._targetName = bindingTargetParts[1]
    }
  }

  /**
   * @returns {string} name of the target as a String
   */
  getTargetName () {
    return this._targetName
  }

  /**
   * @returns {FullQualifiedName} qualified name of the entity container this target is contained in
   */
  getEntityContainer () {
    return this._entityContainer
  }

  toString () {
    return this._entityContainer ? this._entityContainer.toString() + '/' + this._targetName : this._targetName
  }
}

module.exports = Target
