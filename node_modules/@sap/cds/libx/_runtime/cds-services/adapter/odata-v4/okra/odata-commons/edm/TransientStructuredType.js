'use strict'

const EdmType = require('./EdmType')
const FullQualifiedName = require('../FullQualifiedName')

/**
 * A transient structured type used to incorporate properties containing aggregations.
 * It resembles EdmComplexType to the extent necessary for its intended usage.
 * @extends EdmType
 */
class TransientStructuredType extends EdmType {
  /**
   * Create a transient structured type, starting from an existing structured type.
   * @param {EdmEntityType|EdmComplexType} startType the type to start from
   */
  constructor (startType) {
    super(new FullQualifiedName(startType.getNamespace(), 'TransientStructuredType'), EdmType.TypeKind.COMPLEX)

    this._startType = startType

    this._properties = new Map(startType.getProperties())
    this._ignoredProperties = new Map()
    this._optionalProperties = new Map()
    this._protected = new Set()
    this._isReduced = false

    this._navigationProperties = new Map(startType.getNavigationProperties())
    this._ignoredNavigationProperties = new Map()
  }

  /**
   * Return the base type of this instance.
   * The start type is seen as base type of this instance,
   * although it might be totally different, to enable type casts.
   * @returns {EdmEntityType|EdmComplexType} the base type
   */
  getBaseType () {
    return this._startType
  }

  /**
   * Add a property to be ignored during validation.
   * @param {EdmProperty} property the property
   * @returns {TransientStructuredType} this instance
   * @package
   */
  addIgnoredProperty (property) {
    this._ignoredProperties.set(property.getName(), property)
    return this
  }

  /**
   * Add a navigation property to be ignored during validation.
   * @param {EdmProperty} property the navigation property
   * @returns {TransientStructuredType} this instance
   * @package
   */
  addIgnoredNavigationProperty (property) {
    this._ignoredNavigationProperties.set(property.getName(), property)
    return this
  }

  /**
   * Add a property.
   * @param {EdmProperty} property the property
   * @param {boolean} optional whether the property is optional
   * @returns {TransientStructuredType} this instance
   * @package
   */
  addProperty (property, optional) {
    this._properties.set(property.getName(), property)
    if (optional) this._optionalProperties.set(property.getName(), property)
    this.protectProperty(property.getName())
    return this
  }

  /**
   * Is the property protected?
   * @param {name} name name of the property to be protected
   * @returns {Boolean} Is property protected
   * @package
   */
  isProtected (name) {
    return this._protected.has(name)
  }

  /**
   * Protect a structural property against deleting.
   * @param {name} name name of the property to be protected
   * @returns {TransientStructuredType} this instance
   * @package
   */
  protectProperty (name) {
    this._protected.add(name)
    return this
  }

  /**
   * Unprotect all structural properties.
   * @returns {TransientStructuredType} this instance
   * @package
   */
  unprotectProperties () {
    this._protected.clear()
    return this
  }

  /**
   * Remove all unprotected structural properties.
   * @returns {TransientStructuredType} this instance
   * @package
   */
  deleteProperties () {
    for (const name of this._properties.keys()) {
      if (!this._protected.has(name)) {
        this._properties.delete(name)
        this._isReduced = true
      }
    }
    return this
  }

  /**
   * Return true if structural properties have been deleted.
   * @returns {boolean} whether structural properties have been deleted
   * @package
   */
  isReduced () {
    return this._isReduced
  }

  /**
   * Return the requested property found by its name.
   * @param {string} name name of the requested property
   * @returns {?(EdmProperty|EdmNavigationProperty)} the property
   */
  getProperty (name) {
    return this._properties.get(name) || this._navigationProperties.get(name) || null
  }

  /**
   * Return the requested structural property found by its name.
   * @param {string} name name of the requested property
   * @returns {EdmProperty} the property
   */
  getStructuralProperty (name) {
    return this._properties.get(name)
  }

  /**
   * Return the requested ignored navigation property found by its name.
   * @param {string} name name of the requested property
   * @returns {EdmNavigationProperty} the navigation property
   */
  getIgnoredNavigationProperty (name) {
    return this._ignoredNavigationProperties.get(name)
  }

  /**
   * Return the requested navigation property found by its name.
   * @param {string} name name of the requested property
   * @returns {EdmNavigationProperty} the navigation property
   */
  getNavigationProperty (name) {
    return this._navigationProperties.get(name)
  }

  /**
   * Return the requested optional property found by its name.
   * @param {string} name name of the requested property
   * @returns {?EdmProperty} the optional property
   */
  getOptionalProperty (name) {
    return this._optionalProperties.get(name)
  }

  /**
   * Return true if the provided type is compatible to this instance type.
   * Refer to the base type of this instance, although it might be totally different, to enable type casts.
   * @param {EdmEntityType|EdmComplexType} type the provided type
   * @returns {boolean} whether the provided type is compatible to this instance type
   */
  compatibleTo (type) {
    return this._startType.compatibleTo(type)
  }

  /**
   * Return a Map of structural properties.
   * @returns {Map.<string, EdmProperty>} a Map of structural properties
   */
  getProperties () {
    return this._properties
  }

  /**
   * Return a Map of ignored properties.
   * @returns {Map.<string, EdmProperty>} a Map of structural properties
   */
  getIgnoredProperties () {
    return this._ignoredProperties
  }

  /**
   * Return a Map of navigation properties.
   * @returns {Map.<string, EdmNavigationProperty>} a Map of navigation properties
   */
  getNavigationProperties () {
    return this._navigationProperties
  }

  /**
   * Return a Map of optional properties.
   * @returns {Map.<string, EdmProperty>} a Map of optional properties
   */
  getOptionalProperties () {
    return this._optionalProperties
  }

  /**
   * Return a Map of key properties.
   * @returns {Map.<string, EdmKeyPropertyRef>} a Map of key-property references
   */
  getKeyPropertyRefs () {
    return this._startType.getKeyPropertyRefs()
  }

  /**
   * Return the custom aggregates.
   * @returns {Map.<string, EdmPrimitiveType|EdmTypeDefinition>} a Map of custom aggregates
   */
  getCustomAggregates () {
    return this._startType.getCustomAggregates()
  }

  /**
   * Return the custom aggregation methods.
   * @returns {Map.<string, EdmPrimitiveType|EdmTypeDefinition>} a Map of custom aggregation methods
   */
  getCustomAggregationMethods () {
    return this._startType.getCustomAggregationMethods()
  }
}

module.exports = TransientStructuredType
