'use strict'

const CsdlAnnotationExpression = require('../../csdl/annotationExpression/CsdlAnnotationExpression')
const EdmAnnotationExpression = require('./EdmAnnotationExpression')
const IllegalArgumentError = require('../../errors/IllegalArgumentError')

/**
 *
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752648">
 *     OData CSDL # 14.5.2 Expression edm:AnnotationPath
 * </a>
 *
 * @extends EdmAnnotationExpression
 * @hideconstructor
 */
class EdmAnnotationPathExpression extends EdmAnnotationExpression {
  /**
   * @param {CsdlAnnotationPathExpression} annotationPath The csdl annotation path expression
   */
  constructor (annotationPath) {
    if (!annotationPath) {
      throw IllegalArgumentError.createForIllegalInstance('annotationPath', 'CsdlAnnotationPathExpression')
    }
    super(CsdlAnnotationExpression.kinds.AnnotationPath)

    /**
     * @type {CsdlAnnotationPathExpression}
     * @private
     */
    this._annotationPath = annotationPath
  }

  /**
   * Returns the annotation path pointing to a term (e.g "Supplier/@Communication.Contact")
   * @returns {string} the annotation path
   */
  getAnnotationPath () {
    return this._annotationPath.annotationPath
  }
}

module.exports = EdmAnnotationPathExpression
