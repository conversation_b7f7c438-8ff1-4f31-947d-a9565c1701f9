'use strict'

const AnnotationFactory = require('./AnnotationExpressionFactory')
const CsdlAnnotationExpression = require('../../csdl/annotationExpression/CsdlAnnotationExpression')
const EdmAnnotationExpression = require('./EdmAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 * @extends EdmAnnotationExpression
 * @hideconstructor
 */
class EdmArithmeticExpression extends EdmAnnotationExpression {
  /**
   * @param {Edm} edm The edm itself
   * @param {CsdlArithmeticExpression} arithmeticExpression The csdl arithmetic expression
   */
  constructor (edm, arithmeticExpression) {
    validateThat('edm', edm).truthy()
    validateThat('arithmeticExpression', arithmeticExpression).truthy()

    super(CsdlAnnotationExpression.kinds.Arithmetic)

    /**
     * @type {Edm}
     * @private
     */
    this._edm = edm

    /**
     * @type {CsdlArithmeticExpression}
     * @private
     */
    this._arithmeticExpression = arithmeticExpression

    /**
     * @type {EdmAnnotationExpression}
     * @private
     */
    this._left = AnnotationFactory.createEdmExpressionFromCsdlExpression(edm, this._arithmeticExpression.left)

    /**
     * @type {EdmAnnotationExpression}
     * @private
     */
    this._right = AnnotationFactory.createEdmExpressionFromCsdlExpression(edm, this._arithmeticExpression.right)

    /**
     * @type {EdmAnnotation[]}
     * @private
     */
    this._annotations = null
  }

  /**
   * Returns the operator.
   * @returns {CsdlArithmeticExpression.Operators} Operator
   */
  getOperator () {
    return this._arithmeticExpression.operator
  }

  /**
   * Returns the left operand.
   * @returns {EdmAnnotationExpression} left operand
   */
  getLeft () {
    return this._left
  }

  /**
   * Returns the right operand.
   * @returns {EdmAnnotationExpression} right operand
   */
  getRight () {
    return this._right
  }

  /**
   * Returns the annotations for this arithmetic expression.
   * @returns {EdmAnnotation[]} the annotations
   */
  getAnnotations () {
    if (!this._annotations) {
      this._annotations = this._arithmeticExpression.annotations.map(item =>
        AnnotationFactory.createAnnotation(this._edm, item)
      )
    }
    return this._annotations
  }
}

module.exports = EdmArithmeticExpression
