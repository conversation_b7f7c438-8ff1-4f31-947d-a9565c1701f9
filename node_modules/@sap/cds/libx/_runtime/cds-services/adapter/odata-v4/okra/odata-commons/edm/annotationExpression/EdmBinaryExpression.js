'use strict'

const AnnotationFactory = require('./AnnotationExpressionFactory')
const CsdlAnnotationExpression = require('../../csdl/annotationExpression/CsdlAnnotationExpression')
const EdmAnnotationExpression = require('./EdmAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 *
 * Abstract parent class for all mathematical binary expressions
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752647">
 *     OData CSDL # 14.5.1 Comparison and Logical Operators
 * </a>
 *
 * @extends EdmAnnotationExpression
 * @hideconstructor
 */
class EdmBinaryExpression extends EdmAnnotationExpression {
  /**
   * @param {Edm} edm The edm itself
   * @param {CsdlBinaryExpression} binary The csdl binary operator expression
   */
  constructor (edm, binary) {
    validateThat('edm', edm).truthy()
    validateThat('binary', binary).truthy()

    super(CsdlAnnotationExpression.kinds.Binary)

    /**
     * @type {Edm}
     * @private
     */
    this._edm = edm

    /**
     * @type {CsdlBinaryExpression}
     * @private
     */
    this._binary = binary

    /**
     * @type {EdmAnnotationExpression}
     * @private
     */
    this._left = AnnotationFactory.createEdmExpressionFromCsdlExpression(edm, this._binary.left)

    /**
     * @type {EdmAnnotationExpression}
     * @private
     */
    this._right = AnnotationFactory.createEdmExpressionFromCsdlExpression(edm, this._binary.right)

    /**
     * @type {EdmAnnotation[]}
     * @private
     */
    this._annotations = null
  }

  /**
   * Returns the operator.
   * @returns {CsdlBinaryExpression.ComparisonOperators} Operator
   */
  getOperator () {
    return this._binary.operator
  }

  /**
   * Returns the left operand.
   * @returns {EdmAnnotationExpression} left operand
   */
  getLeft () {
    return this._left
  }

  /**
   * Returns the right operand.
   * @returns {EdmAnnotationExpression} right operand
   */
  getRight () {
    return this._right
  }

  /**
   * Returns the annotations for this binary expression.
   * @returns {EdmAnnotation[]} the annotations
   */
  getAnnotations () {
    if (!this._annotations) {
      this._annotations = this._binary.annotations.map(item => AnnotationFactory.createAnnotation(this._edm, item))
    }
    return this._annotations
  }
}

module.exports = EdmBinaryExpression
