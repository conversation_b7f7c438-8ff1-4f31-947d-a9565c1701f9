'use strict'

const AnnotationFactory = require('./AnnotationExpressionFactory')
const CsdlAnnotationExpression = require('../../csdl/annotationExpression/CsdlAnnotationExpression')
const EdmAnnotationExpression = require('./EdmAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 *
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752633">
 *     OData CSDL # 14.4 Constant Expressions
 * </a>
 *
 * @extends EdmAnnotationExpression
 * @hideconstructor
 */
class EdmConstantExpression extends EdmAnnotationExpression {
  /**
   * @param {Edm} edm the EDM itself
   * @param {CsdlConstantExpression} constantExpression the CSDL constant expression
   */
  constructor (edm, constantExpression) {
    validateThat('edm', edm)
      .truthy()
      .instanceOf(Object)
    validateThat('constantExpression', constantExpression)
      .truthy()
      .instanceOf(Object)

    super(CsdlAnnotationExpression.kinds.Constant)

    /**
     * @type {Edm}
     * @private
     */
    this._edm = edm

    /**
     * @type {CsdlConstantExpression}
     * @private
     */
    this._constantExpression = constantExpression

    /**
     * @type {EdmAnnotation[]}
     * @private
     */
    this._annotations = null
  }

  /**
   * Return the type of the constant expression; this a not an EdmType but a string like, e.g., 'Edm:Binary'.
   * @returns {CsdlConstantExpression.Types} the type
   */
  getTypeName () {
    return this._constantExpression.type
  }

  /**
   * Returns the value of the expression.
   * Depending on the type this may be a string, a number, a boolean, or a Buffer object.
   *
   * @returns {string|number|boolean|Buffer} the value
   */
  getValue () {
    return this._constantExpression.value
  }

  /**
   * Returns the annotations for this constant expression.
   * @returns {EdmAnnotation[]} the annotations
   */
  getAnnotations () {
    if (!this._annotations) {
      this._annotations = this._constantExpression.annotations.map(item =>
        AnnotationFactory.createAnnotation(this._edm, item)
      )
    }
    return this._annotations
  }
}

module.exports = EdmConstantExpression
