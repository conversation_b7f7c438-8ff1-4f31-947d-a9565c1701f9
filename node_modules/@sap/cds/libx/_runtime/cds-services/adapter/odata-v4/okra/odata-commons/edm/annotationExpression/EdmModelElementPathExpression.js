'use strict'

const CsdlAnnotationExpression = require('../../csdl/annotationExpression/CsdlAnnotationExpression')
const EdmAnnotationExpression = require('./EdmAnnotationExpression')
const IllegalArgumentError = require('../../errors/IllegalArgumentError')

/**
 * @extends EdmAnnotationExpression
 * @hideconstructor
 */
class EdmModelElementPathExpression extends EdmAnnotationExpression {
  /**
   * @param {CsdlModelElementPathExpression} modelElementPath model-element path expression
   */
  constructor (modelElementPath) {
    if (!modelElementPath) {
      throw IllegalArgumentError.createForIllegalInstance('modelElementPath', 'CsdlModelElementPathExpression')
    }

    super(CsdlAnnotationExpression.kinds.ModelElementPath)

    this._modelElementPath = modelElementPath
  }

  /**
   * Return the model-element path.
   *
   * @returns {string} model-element path
   */
  getModelElementPath () {
    return this._modelElementPath.modelElementPath
  }
}

module.exports = EdmModelElementPathExpression
