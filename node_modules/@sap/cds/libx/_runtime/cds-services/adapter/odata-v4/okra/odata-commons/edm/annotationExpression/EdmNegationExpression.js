'use strict'

const AnnotationFactory = require('./AnnotationExpressionFactory')
const CsdlAnnotationExpression = require('../../csdl/annotationExpression/CsdlAnnotationExpression')
const EdmAnnotationExpression = require('./EdmAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 * @extends EdmAnnotationExpression
 * @hideconstructor
 */
class EdmNegationExpression extends EdmAnnotationExpression {
  /**
   * @param {Edm} edm The edm itself
   * @param {CsdlNegationExpression} negationExpression the CSDL negation expression
   */
  constructor (edm, negationExpression) {
    validateThat('edm', edm).truthy()
    validateThat('negationExpression', negationExpression).truthy()

    super(CsdlAnnotationExpression.kinds.Negation)

    /**
     * @type {Edm}
     * @private
     */
    this._edm = edm

    /**
     * @type {CsdlNegationExpression}
     * @private
     */
    this._negationExpression = negationExpression

    /**
     * @type {EdmAnnotationExpression}
     * @private
     */
    this._expression = AnnotationFactory.createEdmExpressionFromCsdlExpression(edm, this._negationExpression.expression)

    /**
     * @type {EdmAnnotation[]}
     * @private
     */
    this._annotations = null
  }

  /**
   * Returns the expression to be negated.
   * @returns {EdmAnnotationExpression} the expression
   */
  getExpression () {
    return this._expression
  }

  /**
   * Returns the annotations for this expression.
   * @returns {EdmAnnotation[]} the annotations
   */
  getAnnotations () {
    if (!this._annotations) {
      this._annotations = this._negationExpression.annotations.map(item =>
        AnnotationFactory.createAnnotation(this._edm, item)
      )
    }
    return this._annotations
  }
}

module.exports = EdmNegationExpression
