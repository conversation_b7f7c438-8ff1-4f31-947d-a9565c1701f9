'use strict'

const AnnotationFactory = require('./AnnotationExpressionFactory')
const CsdlAnnotationExpression = require('../../csdl/annotationExpression/CsdlAnnotationExpression')
const EdmAnnotationExpression = require('./EdmAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 *
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752647">
 *     OData CSDL # 14.5.1 Comparison and Logical Operators
 * </a>
 *
 * @extends EdmAnnotationExpression
 * @hideconstructor
 */
class EdmNotExpression extends EdmAnnotationExpression {
  /**
   * @param {Edm} edm The edm itself
   * @param {CsdlNotExpression} notExpression the CSDL not expression
   */
  constructor (edm, notExpression) {
    validateThat('edm', edm).truthy()
    validateThat('notExpression', notExpression).truthy()

    super(CsdlAnnotationExpression.kinds.Not)

    /**
     * @type {Edm}
     * @private
     */
    this._edm = edm

    /**
     * @type {CsdlNotExpression}
     * @private
     */
    this._notExpression = notExpression

    /**
     * @type {EdmAnnotationExpression}
     * @private
     */
    this._expression = AnnotationFactory.createEdmExpressionFromCsdlExpression(edm, this._notExpression.expression)

    /**
     * @type {EdmAnnotation[]}
     * @private
     */
    this._annotations = null
  }

  /**
   * Returns the expression to be negated.
   * @returns {EdmAnnotationExpression} the expression
   */
  getExpression () {
    return this._expression
  }

  /**
   * Returns the annotations for this expression.
   * @returns {EdmAnnotation[]} the annotations
   */
  getAnnotations () {
    if (!this._annotations) {
      this._annotations = this._notExpression.annotations.map(item =>
        AnnotationFactory.createAnnotation(this._edm, item)
      )
    }
    return this._annotations
  }
}

module.exports = EdmNotExpression
