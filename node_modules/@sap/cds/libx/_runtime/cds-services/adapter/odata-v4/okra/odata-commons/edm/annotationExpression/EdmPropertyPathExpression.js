'use strict'

const CsdlAnnotationExpression = require('../../csdl/annotationExpression/CsdlAnnotationExpression')
const EdmAnnotationExpression = require('./EdmAnnotationExpression')
const validateThat = require('../../validator/ParameterValidator').validateThat

/**
 *
 * <a href="./../ODataSpecification/odata-v4.0-errata03-os/complete/part3-csdl/odata-v4.0-errata03-os-part3-csdl-complete.html#_Toc453752659">
 *     OData CSDL # 14.5.13  Expression edm:PropertyPath
 * </a>
 *
 * @extends EdmAnnotationExpression
 * @hideconstructor
 */
class EdmPropertyPathExpression extends EdmAnnotationExpression {
  /**
   * @param {CsdlPropertyPathExpression} propertyPath the CSDL property path
   */
  constructor (propertyPath) {
    validateThat('propertyPath', propertyPath).truthy()

    super(CsdlAnnotationExpression.kinds.PropertyPath)

    this._propertyPath = propertyPath
  }

  /**
   * Returns the path to the referenced property.
   * @returns {string} the path
   */
  getPropertyPath () {
    return this._propertyPath.propertyPath
  }
}

module.exports = EdmPropertyPathExpression
